import { appCacheDir, join } from '@tauri-apps/api/path';
import { convertFileSrc } from '@tauri-apps/api/core';
import { useEffect, useState } from 'react';

const cacheDirPath = await appCacheDir();

export default function Image({ src, ...props }) {
  const [assetUrl, setAssetUrl] = useState(null);
  useEffect(() => {
    join(cacheDirPath, src)
      .then((path) => {
        setAssetUrl(convertFileSrc(path));
      })
      .catch((e) => {
        console.log(e);
      });
  }, []);
  return <img src={assetUrl} {...props} />;
}
