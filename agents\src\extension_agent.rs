use agent_manager::AgentRunnable;
use anyhow::Result;
use async_trait::async_trait;
use data_collection::DataCollectionExtension;
use logger::{info, ModuleLogger};
use std::{sync::Arc, time::Duration};
use tokio::{select, sync::RwLock, time::sleep};
use utils::shutdown::get_shutdown_signal;

pub struct ExtensionAgent {
    name: String,
    extension: RwLock<Box<dyn DataCollectionExtension>>,
    logger: Arc<ModuleLogger>,
}

impl ExtensionAgent {
    pub fn new(extension: Box<dyn DataCollectionExtension>) -> Self {
        let logger = extension.logger();
        Self {
            name: extension.get_name().to_owned(),
            extension: RwLock::new(extension),
            logger,
        }
    }
}

#[async_trait]
impl AgentRunnable for ExtensionAgent {
    fn logger(&self) -> Arc<ModuleLogger> {
        self.logger.clone()
    }

    fn get_name(&self) -> &str {
        &self.name
    }

    async fn start(&self, logger: Arc<ModuleLogger>) -> Result<()> {
        info!(
            "---------------------- Starting {} Extension ------------------------",
            self.get_name()
        );

        let mut extension = self.extension.write().await;

        extension.collect_and_send(Some(logger.clone())).await.ok();

        drop(extension);

        let mut shutdown_signal = get_shutdown_signal();

        loop {
            let ext = self.extension.read().await;

            let interval = ext.get_refresh_interval();

            drop(ext);

            select! {
                biased;

                _ = shutdown_signal.recv() => {
                    info!("Shutting Down {} Extension", self.get_name());
                    break;
                }

                _ = sleep(Duration::from_secs(interval)) => {
                    let mut extension = self.extension.write().await;
                    extension.collect_and_send(Some(logger.clone())).await.ok();
                },
            }
        }
        info!(
            "---------------------- Stopped {} Extension ------------------------",
            self.get_name()
        );
        Ok(())
    }
}
