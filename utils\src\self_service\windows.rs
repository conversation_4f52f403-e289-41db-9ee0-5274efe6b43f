use std::collections::HashMap;
use std::ffi::OsString;
use std::io;
use std::iter::once;
use std::os::windows::ffi::OsStrExt;
use std::path::PathBuf;
use std::ptr::null_mut;
use std::sync::{LazyLock, Mutex};
use std::time::Duration;
use tracing::{debug, error, info};
use windows::core::PCWSTR;
use windows::Win32::Security::{
    AdjustTokenPrivileges, LookupPrivilegeValueW, SecurityImpersonation, LUID_AND_ATTRIBUTES,
    SE_ASSIGNPRIMARYTOKEN_NAME, SE_INCREASE_QUOTA_NAME, SE_PRIVILEGE_ENABLED, SE_TCB_NAME,
    TOKEN_ACCESS_MASK, TOKEN_ADJUST_PRIVILEGES, TOKEN_PRIVILEGES, TOKEN_QUERY,
};
use windows::Win32::System::Diagnostics::ToolHelp::{
    Create<PERSON><PERSON>hel<PERSON><PERSON>Snapshot, Process32FirstW, Process32NextW, PROCESSENTRY32W, TH32CS_SNAPPROCESS,
};
use windows::Win32::System::RemoteDesktop::{
    ProcessIdToSessionId, WTSActive, WTSConnectState, WTSEnumerateSessionsW, WTSFreeMemory,
    WTSQuerySessionInformationW, WTS_CONNECTSTATE_CLASS, WTS_CURRENT_SERVER_HANDLE,
    WTS_SESSION_INFOW,
};
use windows::Win32::System::Threading::{
    CreateProcessAsUserW, OpenEventW, OpenProcess, TerminateProcess, CREATE_NEW_CONSOLE,
    CREATE_UNICODE_ENVIRONMENT, PROCESS_INFORMATION, PROCESS_QUERY_INFORMATION, PROCESS_TERMINATE,
    STARTUPINFOW, SYNCHRONIZATION_SYNCHRONIZE,
};
use windows::{
    core::PWSTR,
    Win32::{
        Foundation::{GetLastError, *},
        Security::{DuplicateTokenEx, TokenPrimary},
        System::{
            Environment::{CreateEnvironmentBlock, DestroyEnvironmentBlock},
            RemoteDesktop::WTSQueryUserToken,
        },
    },
};

use crate::constants::ENDPOINTOPS_BINARY_NAME;

const SESSION_PROCESS_MAP: LazyLock<Mutex<HashMap<u32, u32>>> =
    LazyLock::new(|| Mutex::new(HashMap::new()));
const SESSION_HANDLE_MAP: LazyLock<Mutex<HashMap<u32, HANDLE>>> =
    LazyLock::new(|| Mutex::new(HashMap::new()));

fn wait_for_user_desktop_ready(session_id: u32, timeout_secs: u64) -> bool {
    unsafe {
        let start = std::time::Instant::now();

        while start.elapsed().as_secs() < timeout_secs {
            // 1. Check if explorer.exe exists in that session
            let snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0).unwrap();
            let mut entry = PROCESSENTRY32W {
                dwSize: std::mem::size_of::<PROCESSENTRY32W>() as u32,
                ..Default::default()
            };

            if Process32FirstW(snapshot, &mut entry).is_ok() {
                loop {
                    let exe = String::from_utf16_lossy(&entry.szExeFile);
                    if exe
                        .trim_end_matches('\0')
                        .eq_ignore_ascii_case("explorer.exe")
                    {
                        // To confirm it belongs to our session
                        let mut proc_session = 0;
                        ProcessIdToSessionId(entry.th32ProcessID, &mut proc_session).ok();
                        if proc_session == session_id {
                            debug!("Explorer.exe found for session {}", session_id);
                            CloseHandle(snapshot).ok();
                            return true;
                        }
                    }

                    if Process32NextW(snapshot, &mut entry).is_err() {
                        break;
                    }
                }
            }

            CloseHandle(snapshot).ok();
            std::thread::sleep(Duration::from_secs(2));
        }

        debug!("Explorer.exe not found for session {}", session_id);
        false
    }
}

pub fn wide_null(s: &str) -> Vec<u16> {
    OsString::from(s).encode_wide().chain(once(0)).collect()
}

pub fn start_command_for_session(session_id: u32, cmd: &str) -> windows::core::Result<()> {
    unsafe {
        enable_privilege()?;
        // Query connection state (WTSConnectState)
        let mut p_state: *mut WTS_CONNECTSTATE_CLASS = null_mut();
        let mut bytes_returned: u32 = 0;
        if WTSQuerySessionInformationW(
            Some(WTS_CURRENT_SERVER_HANDLE),
            session_id,
            WTSConnectState,
            &mut p_state as *mut _ as *mut _,
            &mut bytes_returned,
        )
        .is_err()
        {
            error!(
                "WTSQuerySessionInformationW failed for session {}",
                session_id
            );
        }

        if p_state.is_null() {
            error!(
                "WTSQuerySessionInformationW returned null state for {}",
                session_id
            );
        }

        let state = *p_state;
        WTSFreeMemory(p_state as _);
        if state != WTSActive {
            info!("session {} not active, skipping", session_id);
            return Ok(());
        }
        if let Some(pid) = SESSION_PROCESS_MAP.lock().unwrap().get(&session_id) {
            info!(
                "session {} already has a process checking if the process is live",
                session_id
            );
            if OpenProcess(PROCESS_QUERY_INFORMATION, false, pid.to_owned()).is_ok() {
                info!("session {} process is live, skipping", session_id);
                return Ok(());
            }
        }
        // we wait for user desktop to be ready
        if wait_for_user_desktop_ready(session_id, 30) == false {
            return Err(windows::core::Error::new(
                windows::core::Error::from_win32().code(),
                "User desktop not ready",
            ));
        }
        // Get impersonation token for that session
        let mut h_impersonation_token = HANDLE::default();
        if let Err(error) = WTSQueryUserToken(session_id, &mut h_impersonation_token) {
            error!(
                "WTSQueryUserToken failed for session {} with error {:?}",
                session_id, error
            );
        }
        // Duplicate to primary token
        let mut h_user_token = HANDLE::default();
        // request MAXIMUM_ALLOWED - simpler and avoids missing rights; DuplicateTokenEx will fail if not allowed.
        if DuplicateTokenEx(
            h_impersonation_token,
            TOKEN_ACCESS_MASK(0),
            None,
            // SecurityImpersonation,
            SecurityImpersonation,
            TokenPrimary,
            &mut h_user_token,
        )
        .is_err()
        {
            error!("DuplicateTokenEx failed for session {}", session_id);
            CloseHandle(h_impersonation_token).ok();
        }

        // Create environment block for the user token
        let mut lp_environment: *mut core::ffi::c_void = std::ptr::null_mut();
        if CreateEnvironmentBlock(&mut lp_environment, Some(h_user_token), false).is_err() {
            error!("CreateEnvironmentBlock failed for session {}", session_id);
            CloseHandle(h_user_token).ok();
            CloseHandle(h_impersonation_token).ok();
        }

        // Build mutable command line buffer (must be writable)
        let mut cmd_wide = wide_null(cmd);
        // Startup info (desktop to winsta0\\default so GUI appears)
        let mut desktop_wide = wide_null("winsta0\\default");

        let mut si: STARTUPINFOW = std::mem::zeroed();
        si.cb = std::mem::size_of::<STARTUPINFOW>() as u32;
        // si.lpDesktop expects PWSTR; we pass pointer to our desktop_wide buffer
        si.lpDesktop = PWSTR(desktop_wide.as_mut_ptr());

        let mut pi: PROCESS_INFORMATION = std::mem::zeroed();

        // CreateProcessAsUserW: pass lpEnvironment as *const _ (pointer returned by CreateEnvironmentBlock)
        if let Err(error) = CreateProcessAsUserW(
            Some(h_user_token),
            None, // lpApplicationName - let windows parse
            Some(PWSTR::from_raw(cmd_wide.as_mut_ptr())), // lpCommandLine - must be mutable buffer
            None, // lpProcessAttributes
            None, // lpThreadAttributes
            false, // bInheritHandles
            CREATE_UNICODE_ENVIRONMENT | CREATE_NEW_CONSOLE,
            Some(lp_environment as *const _), // lpEnvironment - pointer returned earlier
            None,                             // lpCurrentDirectory
            &si,
            &mut pi,
        ) {
            error!(
                "CreateProcessAsUserW failed for session {}: {:?}",
                session_id, error
            );
        } else {
            info!(
                "Process created for session {} with pid {}",
                session_id, pi.dwProcessId
            );
            SESSION_PROCESS_MAP
                .lock()
                .unwrap()
                .insert(session_id, pi.dwProcessId);
            CloseHandle(pi.hProcess).ok();
            CloseHandle(pi.hThread).ok();
        }
        // cleanup
        DestroyEnvironmentBlock(lp_environment).ok();
        CloseHandle(h_user_token).ok();
        CloseHandle(h_impersonation_token).ok();
        // no harm to call RevertToSelf even if we didn't impersonate directly here
        windows::Win32::Security::RevertToSelf().ok();
    }
    Ok(())
}

pub fn stop_for_session(session_id: u32) -> windows::core::Result<()> {
    if let Some(handle) = SESSION_HANDLE_MAP.lock().unwrap().remove(&session_id) {
        unsafe {
            CloseHandle(handle).ok();
        }
    }
    if let Some(pid) = SESSION_PROCESS_MAP.lock().unwrap().remove(&session_id) {
        unsafe {
            let h_process: HANDLE = match OpenProcess(PROCESS_TERMINATE, false, pid) {
                Ok(handle) => handle,
                Err(error) => {
                    error!(?error, "Failed to open process for session {}", session_id);
                    return Err(error);
                }
            };
            TerminateProcess(h_process, 0).ok();
        }
    }
    Ok(())
}

fn enable_privilege() -> windows::core::Result<()> {
    use windows::Win32::Foundation::ERROR_SUCCESS;
    use windows::Win32::System::Threading::{GetCurrentProcess, OpenProcessToken};

    unsafe {
        let mut token = HANDLE::default();
        OpenProcessToken(
            GetCurrentProcess(),
            TOKEN_ADJUST_PRIVILEGES | TOKEN_QUERY,
            &mut token,
        )?;

        for privilege in [
            SE_INCREASE_QUOTA_NAME,
            SE_TCB_NAME,
            SE_ASSIGNPRIMARYTOKEN_NAME,
        ] {
            let mut luid = LUID::default();
            LookupPrivilegeValueW(None, privilege, &mut luid)?;

            let tp = TOKEN_PRIVILEGES {
                PrivilegeCount: 1,
                Privileges: [LUID_AND_ATTRIBUTES {
                    Luid: luid,
                    Attributes: SE_PRIVILEGE_ENABLED,
                }],
            };

            AdjustTokenPrivileges(
                token,
                false,
                Some(&tp),
                std::mem::size_of::<TOKEN_PRIVILEGES>() as u32,
                None,
                None,
            )?;

            // Check last error: AdjustTokenPrivileges can succeed but still not enable without error check
            let err = GetLastError();
            if err.0 != ERROR_SUCCESS.0 {
                CloseHandle(token).ok();
                return Err(windows::core::Error::from_win32());
            }
        }

        CloseHandle(token).ok();
    }
    Ok(())
}

pub fn start_now(_app_name: &str, exe_path: &PathBuf) -> io::Result<()> {
    let cmd = format!("\"{}\" --gui", exe_path.display());
    unsafe {
        let mut p_sessions: *mut WTS_SESSION_INFOW = null_mut();
        let mut session_count: u32 = 0;

        WTSEnumerateSessionsW(
            Some(WTS_CURRENT_SERVER_HANDLE),
            0,
            1,
            &mut p_sessions,
            &mut session_count,
        )?;

        if session_count == 0 {
            error!("No sessions found");
            return Ok(());
        }

        let sessions = std::slice::from_raw_parts(p_sessions, session_count as usize);

        for session in sessions {
            let session_id = session.SessionId;
            if let Err(error) = start_command_for_session(session_id, &cmd) {
                error!(?error, "Failed to start gui for session {}", session_id);
            }
        }
        WTSFreeMemory(p_sessions as _);
    }
    Ok(())
}

pub fn register(_app_name: &str, _exe_path: &PathBuf) -> io::Result<()> {
    Ok(())
}

pub fn deregister(_app_name: &str) -> io::Result<()> {
    Ok(())
}

pub fn start_gui_for_session(session_id: u32, install_path: &str) -> windows::core::Result<()> {
    // If instance event exists -> GUI running
    unsafe {
        let event_name = get_self_service_instance_event_name(session_id);
        if let Ok(h_instance) = OpenEventW(
            SYNCHRONIZATION_SYNCHRONIZE,
            false,
            PCWSTR(event_name.as_ptr()),
        ) {
            CloseHandle(h_instance).ok();
            return Ok(());
        }
    }

    let exe_path = PathBuf::from(install_path).join(ENDPOINTOPS_BINARY_NAME);
    let cmd = format!("\"{}\" --gui", exe_path.display());
    start_command_for_session(session_id, &cmd)
}

fn get_self_service_instance_event_name(session_id: u32) -> Vec<u16> {
    use crate::self_service::platform::wide_null;

    wide_null(format!("Global\\UnifiedService_Instance_Session_{}", session_id).as_str())
}

pub fn close_gui_instance_event(hobject: HANDLE) -> windows::core::Result<()> {
    unsafe { CloseHandle(hobject) }
}

pub fn create_self_service_instance_event() -> windows::core::Result<()> {
    use tracing::error;
    use windows::Win32::System::RemoteDesktop::WTSGetActiveConsoleSessionId;
    use windows::{core::PCWSTR, Win32::System::Threading::CreateEventW};
    let session_id = unsafe { WTSGetActiveConsoleSessionId() };
    let name = PCWSTR(get_self_service_instance_event_name(session_id).as_ptr());

    match unsafe { CreateEventW(None, true, false, Some(&name)) } {
        Ok(handle) => {
            unsafe {
                if GetLastError() == ERROR_ALREADY_EXISTS {
                    // Another GUI is running; exit
                    CloseHandle(handle).ok();
                    return Err(windows::core::Error::from_win32());
                }
            }
            debug!("Created self service instance event");
            SESSION_HANDLE_MAP
                .lock()
                .unwrap()
                .insert(session_id, handle);
            Ok(())
        }
        Err(error) => {
            error!(?error, "Failed to create self service instance event");
            return Err(error);
        }
    }
}

pub fn stop_gui_for_current_session() -> windows::core::Result<()> {
    use windows::Win32::System::RemoteDesktop::WTSGetActiveConsoleSessionId;
    let session_id = unsafe { WTSGetActiveConsoleSessionId() };
    stop_gui_for_session(session_id)
}

pub fn stop_gui_for_session(session_id: u32) -> windows::core::Result<()> {
    stop_for_session(session_id)
}
