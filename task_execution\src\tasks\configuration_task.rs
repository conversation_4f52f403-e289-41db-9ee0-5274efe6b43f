use crate::{
    has_task::HasTask, log_task::LogTask, sync_task::SyncTask,
    tasks::configuration_executor::ConfigurationExecutor, TaskExecutable,
};
use anyhow::Result;
use async_trait::async_trait;
use database::{
    data_types::TaskResult,
    models::{Task, TaskStatus},
};
use logger::{error, ModuleLogger};
use std::sync::Arc;

#[derive(Debug)]
pub struct ConfigurationTask {
    task: Task,
    logger: Arc<ModuleLogger>,
}

impl ConfigurationTask {
    pub fn new(task: Task) -> Self {
        Self {
            task,
            logger: ModuleLogger::new("configuration", None, Some("configuration".to_owned())),
        }
    }
}

impl HasTask for ConfigurationTask {
    fn get_task(&self) -> &Task {
        &self.task
    }

    fn set_task(&mut self, task: Task) {
        self.task = task;
    }
}

impl LogTask for ConfigurationTask {
    fn logger(&self) -> Arc<ModuleLogger> {
        Arc::clone(&self.logger)
    }
}

impl SyncTask for ConfigurationTask {}

#[async_trait]
impl TaskExecutable for ConfigurationTask {
    async fn execute(&mut self) -> Result<TaskResult> {
        self.write_task_log(
            format!("Initiating execution of configuration {}", self.get_name()),
            None,
        )
        .await;

        let mut task_result = TaskResult::default();

        // if no configuration found return error
        if self.task.configuration.is_none() {
            error!("No configuration found for {:?}", self.task);
            self.write_task_log("No configuration found".to_owned(), Some("ERROR"))
                .await;
            task_result.status = TaskStatus::Failed;
            task_result.output = "".to_owned();
            task_result.exit_code = 99;
            return Ok(task_result);
        }

        let configuration = self.task.configuration.as_ref().unwrap();

        Ok(ConfigurationExecutor::new(self, configuration)
            .execute()
            .await?)
    }
}
