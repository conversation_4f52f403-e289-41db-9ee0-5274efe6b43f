use battery::units::{ratio::percent, time::second};
use logger::error;
use serde::Serialize;

#[derive(Debug, Serialize, Clone)]
pub struct Battery {
    vendor: String,
    model: String,
    state: String,
    serial_no: String,
    cycle_count: u32, // time_to_full: String,
    percentage: f64,
    time_to_full: u32,
    time_to_empty: u32,
}

impl Battery {
    pub fn collect() -> Vec<Self> {
        let manager = match battery::Manager::new() {
            Ok(manager) => manager,
            Err(error) => {
                error!(?error, "Failed to get battery manager");
                return vec![];
            }
        };

        let batteries = match manager.batteries() {
            Ok(batteries) => batteries,
            Err(error) => {
                error!(?error, "Failed to get batteries from manager");
                return vec![];
            }
        };

        let mut discovered_batteries = Vec::new();

        for maybe_battery in batteries {
            if let Ok(battery) = maybe_battery {
                discovered_batteries.push(Battery {
                    vendor: battery
                        .vendor()
                        .map_or("".to_owned(), |i| i.trim().to_owned()),
                    model: battery
                        .model()
                        .map_or("".to_owned(), |i| i.trim().to_owned()),
                    serial_no: battery
                        .serial_number()
                        .map_or("".to_owned(), |i| i.trim().to_owned()),
                    state: battery.state().to_string().trim().to_string(),
                    cycle_count: battery.cycle_count().unwrap_or_default(),
                    percentage: f64::trunc(
                        (battery.energy() / battery.energy_full()).get::<percent>() as f64 * 100.0,
                    ) / 100.0,
                    time_to_full: battery
                        .time_to_full()
                        .map_or(0, |i| i.get::<second>().round() as u32),
                    time_to_empty: battery
                        .time_to_empty()
                        .map_or(0, |i| i.get::<second>().round() as u32),
                });
            }
        }

        discovered_batteries
    }
}
