[package]
name = "logger"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
utils = { path = "../utils" }
chrono = { workspace = true }
thiserror = { workspace = true }
time = { version = "0.3.41", features = [
  "local-offset",
  "formatting",
  "parsing",
] }
tracing = { version = "0.1.41", features = ["log", "std"] }
tracing-appender = "0.2.3"
logroller = { version = "0.1", features = ["xz"] }
tracing-subscriber = { version = "0.3.19", features = [
  "local-time",
  "env-filter",
  "chrono",
] }
# console-subscriber = "0.4.1"
