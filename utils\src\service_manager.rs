use anyhow::anyhow;
pub use service_manager::ServiceStatus;
use service_manager::{
    ServiceInstallCtx, ServiceManager as ServiceInstallManager, ServiceStartCtx, ServiceStatusCtx,
    ServiceStopCtx, ServiceUninstallCtx,
};
use std::{ffi::OsString, path::PathBuf};
use tracing::{debug, error, info};

use crate::constants::ENDPOINTOPS_SERVICE_NAME;

pub struct ServiceManager {
    name: String,
    program: PathBuf,
    working_dir: PathBuf,
    args: Vec<OsString>,
}

impl ServiceManager {
    pub fn new(
        name: String,
        program: PathBuf,
        working_dir: Option<PathBuf>,
        args: Vec<String>,
    ) -> Self {
        Self {
            name,
            working_dir: working_dir
                .map_or(program.clone().parent().unwrap().to_path_buf(), |item| item),
            program,
            args: args.into_iter().map(|item| OsString::from(item)).collect(),
        }
    }

    pub fn get_name(&self) -> &str {
        &self.name
    }

    pub fn get_program(&self) -> PathBuf {
        self.program.to_owned()
    }

    fn build_manager() -> Result<Box<dyn ServiceInstallManager>, anyhow::Error> {
        match <dyn ServiceInstallManager>::native() {
            Ok(manager) => Ok(manager),
            Err(error) => {
                error!(?error, "Failed to determine service platform");
                return Err(anyhow!("Failed to determine service platform"));
            }
        }
    }

    pub fn status(&self) -> Result<ServiceStatus, anyhow::Error> {
        let manager = ServiceManager::build_manager()?;

        Ok(manager.status(ServiceStatusCtx {
            label: self.name.parse().unwrap(),
        })?)
    }

    pub fn force_start(&self) -> Result<(), anyhow::Error> {
        match self.status() {
            Ok(status) => {
                if status != ServiceStatus::Running {
                    match self.start() {
                        Ok(()) => {
                            info!("Service {} has started successfully", self.get_name());
                            Ok(())
                        }
                        Err(error) => {
                            error!(?error, "Failed to start service {}", self.get_name());
                            Err(error)
                        }
                    }
                } else {
                    debug!(
                        "Service {} is already running so skipping start",
                        self.get_name()
                    );
                    Ok(())
                }
            }
            Err(error) => {
                error!(
                    ?error,
                    "Failed to determine current status of service {} so starting it forcefully",
                    self.get_name()
                );
                match self.start() {
                    Ok(()) => {
                        info!("Service {} has started successfully", self.get_name());
                        Ok(())
                    }
                    Err(error) => {
                        error!(?error, "Failed to start service {}", self.get_name());
                        Err(error)
                    }
                }
            }
        }
    }

    pub fn install(&self) -> Result<(), anyhow::Error> {
        let manager = if self.name == ENDPOINTOPS_SERVICE_NAME && cfg!(target_os = "macos") {
            let mut manager = service_manager::LaunchdServiceManager::system();

            // Update an install configuration property where installing a service
            // will NOT add the KeepAlive flag
            manager.config.install.keep_alive = false;
            Box::new(manager)
        } else if self.name == ENDPOINTOPS_SERVICE_NAME && cfg!(target_os = "linux") {
            let mut manager = service_manager::SystemdServiceManager::system();

            // Update an install configuration property where installing a service
            // will NOT add the restart flag
            manager.config.install.restart = service_manager::SystemdServiceRestartType::No;
            Box::new(manager)
        } else {
            ServiceManager::build_manager()?
        };

        // // Install our service using the underlying service management platform
        match manager.install(ServiceInstallCtx {
            label: self.name.parse().unwrap(),
            program: self.program.clone(),
            args: self.args.clone(),
            contents: None, // Optional String for system-specific service content.
            username: None, // Optional String for alternative user to run service.
            working_directory: Some(self.working_dir.clone()), // Optional String for the working directory for the service process.
            environment: None, // Optional list of environment variables to supply the service process.
            autostart: self.get_name() != ENDPOINTOPS_SERVICE_NAME, // Specify whether the service should automatically start upon OS reboot.
            disable_restart_on_failure: true, // Services restart on crash by default.
        }) {
            Ok(_) => {
                debug!("Service EndpointOps is installed successfully");
            }
            Err(error) => {
                error!(?error, "Failed to install EndpointOps service");
                return Err(anyhow!("Failed to install EndpointOps Service"));
            }
        }

        Ok(())
    }

    pub fn start(&self) -> Result<(), anyhow::Error> {
        let manager = ServiceManager::build_manager()?;
        // Start our service using the underlying service management platform
        match manager.start(ServiceStartCtx {
            label: self.name.parse().unwrap(),
        }) {
            Ok(_) => debug!("Service {} started successfully.", self.name),
            Err(error) => {
                error!(?error, "Failed to start {} Service", self.name);
                return Err(anyhow!("Failed to start {} Service", self.name));
            }
        };
        Ok(())
    }

    pub fn stop(&self) -> Result<(), anyhow::Error> {
        let manager = ServiceManager::build_manager()?;
        // Stop our service using the underlying service management platform
        match manager.stop(ServiceStopCtx {
            label: self.name.parse().unwrap(),
        }) {
            Ok(_) => debug!("Successfully stopped service {}", self.name),
            Err(error) => {
                error!(?error, "Failed to stop service {}", self.name);
                return Err(anyhow!("Failed to stop service {}", self.name));
            }
        };
        Ok(())
    }

    pub fn restart(&self) -> Result<(), anyhow::Error> {
        let manager = ServiceManager::build_manager()?;
        // Stop our service using the underlying service management platform
        match manager.stop(ServiceStopCtx {
            label: self.name.parse().unwrap(),
        }) {
            Ok(_) => debug!("Successfully stopped service {}", self.name),
            Err(error) => {
                error!(?error, "Failed to stop service {}", self.name);
                return Err(anyhow!("Failed to stop service {}", self.name));
            }
        };
        Ok(())
    }

    pub fn uninstall(&self) -> Result<(), anyhow::Error> {
        let manager = ServiceManager::build_manager()?;
        // Stop our service using the underlying service management platform
        match manager.uninstall(ServiceUninstallCtx {
            label: self.name.parse().unwrap(),
        }) {
            Ok(_) => debug!("Successfully uninstalled service {}", self.name),
            Err(error) => {
                error!(?error, "Failed to uninstalled service {}", self.name);
                return Err(anyhow!("Failed to uninstall service {}", self.name));
            }
        };
        Ok(())
    }
}
