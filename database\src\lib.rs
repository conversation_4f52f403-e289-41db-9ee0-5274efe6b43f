use std::sync::{LazyLock, OnceLock};
use surrealdb::{
    engine::local::{Db, RocksDb},
    opt::auth::Database as AuthDatabase,
    Surreal,
};
pub mod data_types;
pub mod models;
mod prelude;
mod queries;
mod traits;
use anyhow::Result;
use logger::{debug, error};
pub use prelude::*;
pub use queries::is_system_rebooted_recently;
pub use surrealdb::Uuid;
pub use traits::*;

pub(crate) type DbConnection = Surreal<Db>;
pub(crate) type DbClient = LazyLock<DbConnection>;

pub(crate) static DB: DbClient = LazyLock::new(Surreal::init);
static DB_OPTIONS: OnceLock<DbOptions> = OnceLock::new();

pub static DB_NAME_SPACE: &str = "endpointops";
pub static DB_NAME: &str = "endpointops";
pub static DB_USERNAME: &str = "endpointops";
pub static DB_PASSWORD: &str = "endpointops_ziro@ziro@2019";

pub struct Database;

impl Database {
    pub async fn disposable_connection(options: DbOptions) -> Result<Surreal<Db>, DatabaseError> {
        debug!(
            "initialise installation database with Options: {:?}",
            options
        );

        DB_OPTIONS.get_or_init(|| options);

        let options = DB_OPTIONS.get();
        if let Some(options) = options {
            let db = Surreal::new::<RocksDb>(options.path())
                .with_capacity(1)
                .await;
            if let Ok(db) = db {
                debug!("connected data with path: {}", options.path());
                if let Err(error) = db.use_ns(options.namespace()).use_db(options.db()).await {
                    error!(?error, "Surreal DB Error");
                    return Err(DatabaseError::DB(format!(
                        "Failed to initialise database with namespace {} and database {}",
                        options.namespace(),
                        options.db()
                    )));
                }

                let surql = format!(
                    "DEFINE USER {} ON DATABASE PASSWORD '{}' ROLES EDITOR;",
                    options.username(),
                    options.password()
                );

                match db.query(surql).await?.check() {
                    Ok(_) => debug!("Created Database User!"),
                    Err(_error) => {}
                };

                db.authenticate(
                    db.signin(AuthDatabase {
                        namespace: options.namespace(),
                        database: options.db(),
                        username: options.username(),
                        password: options.password(),
                    })
                    .await?,
                )
                .await?;

                debug!("Initialised database successfully.");
                Ok(db)
            } else {
                error!("Failed to connect to database at path {}", options.path());
                Err(DatabaseError::DB(format!(
                    "Failed to connect to database at path {}",
                    options.path()
                )))
            }
        } else {
            Err(DatabaseError::Uninitialised)
        }
    }

    pub async fn init(options: DbOptions) -> Result<(), DatabaseError> {
        debug!("initialise database with Options: {:?}", options);

        DB_OPTIONS.get_or_init(|| options);

        let options = DB_OPTIONS.get();
        if let Some(options) = options {
            if DB
                .connect::<RocksDb>(options.path())
                .with_capacity(1)
                .await
                .is_ok()
            {
                debug!("connected data with path: {}", options.path());
                if let Err(error) = DB.use_ns(options.namespace()).use_db(options.db()).await {
                    error!(?error, "Surreal DB Error");
                    return Err(DatabaseError::DB(format!(
                        "Failed to initialise database with namespace {} and database {}",
                        options.namespace(),
                        options.db()
                    )));
                }

                let surql = format!(
                    "DEFINE USER {} ON DATABASE PASSWORD '{}' ROLES EDITOR;",
                    options.username(),
                    options.password()
                );

                match DB.query(surql).await?.check() {
                    Ok(_) => debug!("Created Database User!"),
                    Err(_error) => {}
                };

                DB.authenticate(
                    DB.signin(AuthDatabase {
                        namespace: options.namespace(),
                        database: options.db(),
                        username: options.username(),
                        password: options.password(),
                    })
                    .await?,
                )
                .await?;

                debug!("Initialised database successfully.");
                Ok(())
            } else {
                error!("Failed to connect to database at path {}", options.path());
                Err(DatabaseError::DB(format!(
                    "Failed to connect to database at path {}",
                    options.path()
                )))
            }
        } else {
            Err(DatabaseError::Uninitialised)
        }
    }
}
