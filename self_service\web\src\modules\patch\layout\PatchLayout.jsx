import { Suspense } from 'react';
import { Row, Col } from 'antd';
import { Outlet } from 'react-router-dom';
import Loading from '../../../components/Loading';
import PageHeading from '../../../components/PageHeading';
// import SettingsMenu from '../components/SettingsMenu';

export default function SoftwareLayout() {
  return (
    <div className="h-full flex flex-col">
      <PageHeading icon="inventory" title="Softwares" />
      <div className="flex-1 min-h-0 flex flex-col h-full">
        <Row className="h-full">
          <Col span={24} className="h-full">
            <Suspense fallback={<Loading />}>
              <Outlet />
            </Suspense>
          </Col>
        </Row>
      </div>
    </div>
  );
}
