[workspace.package]
description = "An Agent to handle all EndpointOps Operations"
edition = "2021"
version = "5.0.12"
authors = ["<PERSON><PERSON> <<EMAIL>>"]

[workspace]
members = [
  "api",
  "database",
  "endpointops",
  "logger",
  "utils",
  "agent_manager",
  "shell",
  "agents",
  "windows_patch_xml_checker",
  "windows_driver_handler",
  "windows_registry",
  "playground",
  "task_execution",
  "data_collection",
  "fim",
  "auth_event",
  "ipc",
  "linux_ebpf",
  "self_service",
]
default-members = ["endpointops"]
resolver = "2"


[profile.dev]
opt-level = 0

[profile.release]
lto = true
strip = true
opt-level = 3
panic = 'abort'
codegen-units = 1

[workspace.dependencies]
serde = { version = "1.0.226", features = ["alloc", "derive", "rc"] }
anyhow = { version = "1.0.100", features = ["backtrace"] }
async-trait = "0.1.89"
tokio = { version = "1.47", features = ["full", "tracing"] }
serde_json = "1.0.145"
cfg-if = "1.0.3"
thiserror = "2.0.16"
sysinfo = "0.37.0"
tokio-stream = "0.1.17"
chrono = { version = "0.4.42", features = ["alloc"] }
futures-util = "0.3.31"
tokio-util = "0.7.16"
rayon = "1.11.0"
regex = "1.11.1"
async-speed-limit = { version = "0.4.2", features = ["tokio"] }
