use ipc::{server::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SELF_SERVICE_IPC_SOCK_NAME};
use logger::{debug, error, info, ModuleLogger};
use serde::Deserialize;
use serde_json::json;
use std::{
    path::PathBuf,
    sync::{Arc, Mutex},
};
use tauri::{
    async_runtime::<PERSON><PERSON><PERSON><PERSON><PERSON>,
    plugin::{<PERSON><PERSON><PERSON>, TauriPlugin},
    <PERSON><PERSON><PERSON>, Listener, Manager, RunEvent, Runtime, WebviewWindowBuilder,
};
use tokio::sync::oneshot;
use utils::shutdown::trigger_shutdown;

#[derive(Debug, Deserialize)]
struct TaskNotification {
    message: String,
    title: String,
}

impl TaskNotification {
    fn show_notification(&self) {
        #[cfg(windows)]
        {
            use tauri_winrt_notification::{IconCrop, Toast};
            use utils::dir::get_current_dir;
            if let Err(error) = Toast::new(Toast::POWERSHELL_APP_ID)
                .icon(
                    get_current_dir().join("icon.png").as_path(),
                    IconCrop::Circular,
                    "EndpointOps",
                )
                .title(&self.title)
                .text1(&self.message)
                .show()
            {
                error!(?error, "Failed to show notification {self:?}");
            }
        }
        #[cfg(not(windows))]
        {
            use notify_rust::Notification;
            use utils::dir::get_current_dir;

            let mut notification = Notification::new();

            notification
                .summary(&self.title)
                .body(&self.message)
                .icon(format!("file://{}", get_current_dir().join("icon.png").display()).as_str());

            if let Err(error) = notification.show() {
                error!(?error, "Failed to show notification {self:?}");
            }
        }
    }
}

async fn ask_credentials<R: Runtime>(
    app_handle: tauri::AppHandle<R>,
    payload: &Option<serde_json::Value>,
) -> Result<String, anyhow::Error> {
    // Create a standalone credentials window with the HTML file
    let window = WebviewWindowBuilder::new(
        &app_handle,
        "credentials",
        tauri::WebviewUrl::App(PathBuf::from("/credentials")),
    )
    .title(
        payload
            .as_ref()
            .unwrap()
            .get("title")
            .unwrap()
            .as_str()
            .unwrap(),
    )
    .resizable(false)
    .center()
    .decorations(false)
    .always_on_top(true)
    .visible(true)
    .build()?;

    let (tx, rx) = oneshot::channel::<String>();

    let window_clone = window.clone();
    window.once("password", move |event| {
        debug!("Received password event: {:?}", event.payload());
        let password = event.payload().trim_matches('"').to_string();
        match tx.send(password) {
            Ok(()) => {
                // Close the window after receiving the password
                if let Err(error) = window_clone.close() {
                    error!(?error, "Failed to close credentials window");
                }
            }
            Err(error) => {
                error!(?error, "Failed to send password");
            }
        };
    });

    match rx.await {
        Ok(password) => {
            debug!("Successfully received password");
            Ok(password)
        }
        Err(error) => {
            error!(?error, "Failed to ask credentials");
            Err(error.into())
        }
    }
}

struct IpcState {
    handle: Option<JoinHandle<()>>,
    shutdown: Option<oneshot::Sender<()>>,
}

pub fn init<R: Runtime>() -> TauriPlugin<R> {
    Builder::new("ipc")
        .setup(|app_handle, _api| {
            let (shutdown, mut shutdown_receiver) = oneshot::channel::<()>();

            let (ipc_tx, mut ipc_rx) = tokio::sync::mpsc::channel(10);

            let mut ipc_server = IpcServer::new(SELF_SERVICE_IPC_SOCK_NAME.to_owned(), ipc_tx);

            let app_handle_ = app_handle.clone();

            let logger = ModuleLogger::new(
            "ipc",
            Some(dirs::config_dir().unwrap().join("endpointops")),
            Some(format!("ipc-{}", SELF_SERVICE_IPC_SOCK_NAME))
            );

            let handle = tauri::async_runtime::spawn(async move {
                match ipc_server.start(logger) {
                    Err(error) => {
                        error!(?error, "Failed to start IPC Server");
                    }
                    _ => {}
                };

                // Listen for global shutdown signals (Ctrl+C)
                // let mut global_shutdown = get_shutdown_signal();

                loop {
                    tokio::select! {
                        biased;

                        _ = &mut shutdown_receiver => {
                            info!("Shutting down IPC Server (plugin shutdown)");
                            if let Err(error) = ipc_server.stop().await {
                                error!(?error, "Failed to stop IPC Server");
                            }
                            break;
                        },

                        message = ipc_rx.recv() => {
                            if let Some((message, response_channel)) = message {
                                let mut response = message.clone();
                                response.ack = true;
                                debug!("Received message: {:?}", message);
                                if message.r#type == "shutdown" {
                                    trigger_shutdown();
                                } else if message.r#type == "provisioned" {
                                    app_handle_.emit("provisioned", json!({})).unwrap();
                                } else if message.r#type == "notification" {
                                    if let Some(ref payload) = message.payload {
                                        let notification = TaskNotification::deserialize(payload).unwrap();
                                        notification.show_notification();
                                    }
                                } else if message.r#type == "ask_credentials" {
                                    match ask_credentials(app_handle_.clone(), &message.payload).await {
                                        Ok(password) => {
                                            response.payload = Some(json!(password));
                                        }
                                        Err(error) => {
                                            error!(?error, "Failed to ask credentials");
                                        }
                                    }
                                }

                                match response_channel.send(response).ok() {
                                    Some(_) => {
                                        debug!("Sent response of {:?} to client", message);
                                    }
                                    None => {
                                        error!("Failed to send response for {:?} to client", message);
                                    }
                                };
                            }
                        }
                    }
                }
            });

            let state = Arc::new(Mutex::new(IpcState {
                handle: Some(handle),
                shutdown: Some(shutdown),
            }));

            app_handle.manage(state);

            Ok(())
        })
        .on_event(|app, event| {
            match event {
                RunEvent::Exit => {
                    info!("App exit requested, shutting down IPC Server");
                    if let Some(state) = app.try_state::<Arc<Mutex<IpcState>>>() {
                        let mut state_guard = state.lock().unwrap();
                        if let Some(shutdown) = state_guard.shutdown.take() {
                            let _ = shutdown.send(());
                        }
                        if let Some(handle) = state_guard.handle.take() {
                            // Spawn a task to wait for the handle without blocking
                            tauri::async_runtime::spawn(async move {
                                if let Err(e) = handle.await {
                                    error!(?e, "Error waiting for IPC server task to complete");
                                }
                            });
                        }
                    }
                }
                _ => {}
            }
        })
        .on_drop(|app| {
            info!("Plugin on_drop called, ensuring IPC Server shutdown");
            if let Some(state) = app.try_state::<Arc<Mutex<IpcState>>>() {
                let mut state_guard = state.lock().unwrap();
                if let Some(shutdown) = state_guard.shutdown.take() {
                    let _ = shutdown.send(());
                }
                // Don't block on the handle in on_drop as it can cause deadlocks
                if let Some(handle) = state_guard.handle.take() {
                    handle.abort(); // Force abort the task if it's still running
                }
            }
        })
        .build()
}
