use super::TaskStatus;
use crate::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>};
use async_trait::async_trait;
use logger::error;
use serde::{Deserialize, Serialize};
use surrealdb::Uuid;

#[derive(Debug, Deserialize, Serialize, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct PatchScanHistory {
    id: PrimaryKey,
    last_scaned_at: i64,
    scan_status: TaskStatus,
}

impl PatchScanHistory {
    pub fn new(scan_status: TaskStatus) -> Self {
        Self {
            scan_status,
            last_scaned_at: chrono::Utc::now().timestamp(),
            id: PrimaryKey::LocalId(Uuid::new_v4().to_string()),
        }
    }

    pub fn last_scanned_at(&self) -> i64 {
        self.last_scaned_at
    }

    pub async fn get_last_scanned_record() -> Option<Self> {
        let default_history = Self::default();
        match DB
            .query(format!(
                "select * from {} order by last_scaned_at desc limit 1",
                default_history.table_name()
            ))
            .await
        {
            Ok(mut response) => match response.take(0) {
                Ok(history) => history,
                Err(error) => {
                    error!(?error, "Failed to unwrap response from database");
                    None
                }
            },
            Err(error) => {
                error!(?error, "Failed to get scheduled task from database");
                None
            }
        }
    }
}

impl HasPrimaryKey for PatchScanHistory {
    fn table_name(&self) -> &str {
        "patch_scan_history"
    }

    fn get_primary_key(&self) -> &PrimaryKey {
        &self.id
    }
}

#[async_trait]
impl Model for PatchScanHistory {}
