use chrono::{Datelike, Local, TimeZone, Timelike, Utc, Weekday};
use logger::debug;
use std::time::Duration;
use sysinfo::System;

pub fn is_system_rebooted_recently() -> bool {
    // if system up time is less than 15 min then only it will return true
    let time = Duration::from_secs_f64(System::uptime() as f64);

    debug!(
        "Current system up time is {:?} minutes",
        time.as_secs() / 60
    );

    time.as_secs() < 15 * 60
}

pub(crate) fn build_reboot_required_task_query() -> String {
    "select * from tasks where status in ['reboot_required']".to_owned()
}

pub(crate) fn build_pending_task_query() -> String {
    let current_unix = Local::now();
    let day = match current_unix.weekday() {
        Weekday::Mon => "monday",
        Weekday::Tue => "tuesday",
        Weekday::Wed => "wednesday",
        Weekday::Thu => "thursday",
        Weekday::Fri => "friday",
        Weekday::Sat => "saturday",
        Weekday::Sun => "sunday",
    };
    let ms = Utc
        .with_ymd_and_hms(
            1970,
            1,
            1,
            current_unix.hour(),
            current_unix.minute(),
            current_unix.second(),
        )
        .unwrap()
        .timestamp_millis();

    let next_reboot_clause = if is_system_rebooted_recently() {
        " or policy.initiateDeploymentOn = 'on_system_start_up'".to_owned()
    } else {
        "".to_owned()
    };

    format!("select * from tasks where status in ['failed', 'ready_to_deploy', 'initiated'] and (deploymentId = 0 or policy.policyType = 'instant' or policy.initiateDeploymentOn = 'recurring' or ((policy.initiateDeploymentOn = 'on_next_cycle' and policy.deploymentDays contains '{}' and policy.fromDatetime <= {} and policy.toDatetime >= {}){}))", day, ms, ms, next_reboot_clause)
}
