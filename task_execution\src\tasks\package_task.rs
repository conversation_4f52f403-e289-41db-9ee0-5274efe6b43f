use super::attachment_downloader::AttachmentDownloader;
use super::command_executor::CommandExecutor;
use crate::has_commands::HasCommands;
use crate::tasks::package_executor::ZipProcessor;
use crate::{
    has_task::HasTask, log_task::LogTask, sync_task::SyncTask, TaskExecutable, TaskExecutionError,
};
use anyhow::Error;
use anyhow::{anyhow, Result};
use async_trait::async_trait;
use database::models::Package;
use database::{
    data_types::TaskResult,
    models::{DeploymentType, FileAttachment, PackageLocation, PackageType, Task, TaskStatus},
};
use logger::{debug, error, ModuleLogger};
use shell::ShellOutput;
use std::path::PathBuf;
use std::sync::Arc;

#[derive(Debug)]
pub struct PackageTask {
    task: Task,
    logger: Arc<ModuleLogger>,
}

impl HasCommands for Package {
    fn get_install_command(&self, _attachment: &FileAttachment) -> Option<String> {
        self.install_command.clone()
    }

    fn get_upgrade_command(&self, _attachment: &FileAttachment) -> Option<String> {
        self.upgrade_command.clone()
    }

    fn get_uninstall_command(&self, _attachment: &FileAttachment) -> Option<String> {
        self.uninstall_command.clone()
    }

    fn get_install_args(&self, _attachment: &FileAttachment) -> Option<String> {
        None
    }

    fn get_uninstall_args(&self, _attachment: &FileAttachment) -> Option<String> {
        None
    }
}

impl PackageTask {
    pub fn new(task: Task) -> Self {
        Self {
            task,
            logger: ModuleLogger::new("package", None, Some("package".to_owned())),
        }
    }

    async fn process_zip(&self, operation_type: DeploymentType) -> Result<ShellOutput, Error> {
        let package = self.task.package.as_ref().unwrap();

        let zip = ZipProcessor::new(package, Box::new(self));

        match operation_type {
            DeploymentType::Install => zip.install().await,
            DeploymentType::Upgrade => zip.upgrade().await,
            DeploymentType::Uninstall => zip.uninstall().await,
        }
    }

    async fn process_script(&self, operation_type: DeploymentType) -> Result<ShellOutput, Error> {
        let package = self.task.package.as_ref().unwrap();

        let command = match operation_type {
            DeploymentType::Install => package.install_command.as_ref().map(|i| i.to_owned()),
            DeploymentType::Uninstall => package.uninstall_command.as_ref().map(|i| i.to_owned()),
            DeploymentType::Upgrade => package.upgrade_command.as_ref().map(|i| i.to_owned()),
        }
        .unwrap_or(package.pkg_file_path.path_to_file_str());

        let command_executor = CommandExecutor::new_script(PathBuf::from(command), Box::new(self));

        let output = command_executor.execute().await?;

        Ok(output)
    }

    #[cfg(target_os = "macos")]
    async fn process_dmg(&self, operation_type: DeploymentType) -> Result<ShellOutput, Error> {
        use crate::tasks::package_executor::Dmg;

        let package = self.task.package.as_ref().unwrap();

        let dmg = Dmg::new(
            Box::new(package.clone()) as Box<dyn HasCommands>,
            &package.pkg_file_path,
            Box::new(self),
        );

        match operation_type {
            DeploymentType::Install => dmg.install().await,
            DeploymentType::Upgrade => dmg.upgrade().await,
            DeploymentType::Uninstall => dmg.uninstall().await,
        }
    }

    #[cfg(target_os = "macos")]
    async fn process_pkg(&self, operation_type: DeploymentType) -> Result<ShellOutput, Error> {
        use crate::tasks::package_executor::Pkg;

        let package = self.task.package.as_ref().unwrap();

        let pkg = Pkg::new(
            Box::new(package.clone()) as Box<dyn HasCommands>,
            &package.pkg_file_path,
            Box::new(self),
        );

        match operation_type {
            DeploymentType::Install => pkg.install().await,
            DeploymentType::Upgrade => pkg.upgrade().await,
            DeploymentType::Uninstall => pkg.uninstall().await,
        }
    }

    #[cfg(target_os = "linux")]
    async fn process_deb(&self, operation_type: DeploymentType) -> Result<ShellOutput, Error> {
        use crate::tasks::package_executor::Deb;

        let package = self.task.package.as_ref().unwrap();

        let deb = Deb::new(
            Box::new(package.clone()) as Box<dyn HasCommands>,
            &package.pkg_file_path,
            Box::new(self),
        );

        match operation_type {
            DeploymentType::Install => deb.install().await,
            DeploymentType::Upgrade => deb.upgrade().await,
            DeploymentType::Uninstall => deb.uninstall().await,
        }
    }

    #[cfg(target_os = "linux")]
    async fn process_rpm(&self, operation_type: DeploymentType) -> Result<ShellOutput, Error> {
        use crate::tasks::package_executor::Rpm;

        let package = self.task.package.as_ref().unwrap();

        let rpm = Rpm::new(
            Box::new(package.clone()) as Box<dyn HasCommands>,
            &package.pkg_file_path,
            Box::new(self),
        );

        match operation_type {
            DeploymentType::Install => rpm.install().await,
            DeploymentType::Upgrade => rpm.upgrade().await,
            DeploymentType::Uninstall => rpm.uninstall().await,
        }
    }

    #[cfg(windows)]
    async fn process_windows(&self, operation_type: DeploymentType) -> Result<ShellOutput, Error> {
        use crate::tasks::package_executor::Msi;

        let package = self.task.package.as_ref().unwrap();

        let msi = Msi::new(
            Box::new(package.clone()) as Box<dyn HasCommands>,
            &package.pkg_file_path,
            Box::new(self),
        );

        match operation_type {
            DeploymentType::Install => msi.install().await,
            DeploymentType::Upgrade => msi.upgrade().await,
            DeploymentType::Uninstall => msi.uninstall().await,
        }
    }

    async fn process(&self, operation_type: DeploymentType) -> Result<TaskResult> {
        let mut task_result = TaskResult::default();
        let package = self.task.package.as_ref().unwrap();

        self.write_task_log(
            format!(
                "Starting to {} {}",
                operation_type, package.pkg_file_path.real_name
            ),
            None,
        )
        .await;

        let extension = package.pkg_file_path.extension();

        let output = match package.pkg_type {
            PackageType::Script => self.process_script(operation_type).await,
            PackageType::Application => match extension {
                "zip" => self.process_zip(operation_type).await,
                #[cfg(target_os = "macos")]
                "dmg" => self.process_dmg(operation_type).await,
                #[cfg(target_os = "macos")]
                "pkg" => self.process_pkg(operation_type).await,
                #[cfg(target_os = "linux")]
                "deb" => self.process_deb(operation_type).await,
                #[cfg(target_os = "linux")]
                "rpm" => self.process_rpm(operation_type).await,
                _ => {
                    return Err(anyhow!(TaskExecutionError::UnsupportedFileExtension(
                        extension.to_owned()
                    )))
                }
            },
            #[cfg(windows)]
            PackageType::Msi | PackageType::Exe => self.process_windows(operation_type).await,
            #[cfg(not(windows))]
            PackageType::Msi | PackageType::Exe => {
                error!("MSI/EXE type is not supported for current platform");
                Ok(ShellOutput {
                    output: "MSI/EXE type is not supported for current platform".to_owned(),
                    exit_code: 99,
                })
            }
            PackageType::Zip => self.process_zip(operation_type).await,
        }?;

        if output.failed() {
            task_result.exit_code = output.exit_code;
            task_result.status = TaskStatus::Failed
        } else {
            task_result.exit_code = output.exit_code;
            task_result.status = TaskStatus::Success;
        }

        Ok(task_result)
    }

    async fn download(&self) -> Result<FileAttachment, TaskExecutionError> {
        let package = self.task.package.as_ref().unwrap();

        let mut attachment = package.pkg_file_path.clone();

        match package.pkg_location {
            PackageLocation::PublicURL => {
                if attachment.url.as_ref().is_some_and(|v| !v.is_empty()) {
                    let url = attachment.url.as_ref().unwrap();
                    // when file server url is given
                    if url.contains("/api/file-server/download") {
                        attachment.public_url = attachment.url.clone();
                        // if original url is given in ref name we use it as last fallback url
                        attachment.url = Some(attachment.ref_name.clone());
                        attachment.ref_name = "".to_owned();
                    } else {
                        // if ref_name is given a url
                        if attachment.ref_name.contains("/") {
                            attachment.public_url = Some(attachment.ref_name.clone());
                            attachment.ref_name = "".to_owned();
                        }
                    }
                } else {
                    attachment.public_url = Some(attachment.ref_name.clone());
                    attachment.ref_name = "".to_owned();
                }
            }
            PackageLocation::SharedDir => {
                attachment.is_network_share = Some(true);
            }
            _ => {}
        }

        AttachmentDownloader::new(attachment, Box::new(self), None)
            .download()
            .await
    }
}

impl HasTask for PackageTask {
    fn get_task(&self) -> &Task {
        &self.task
    }

    fn set_task(&mut self, task: Task) {
        self.task = task;
    }
}

impl LogTask for PackageTask {
    fn logger(&self) -> Arc<ModuleLogger> {
        Arc::clone(&self.logger)
    }
}

impl SyncTask for PackageTask {}

#[async_trait]
impl TaskExecutable for PackageTask {
    async fn execute(&mut self) -> Result<TaskResult> {
        self.write_task_log(
            format!("Initiating execution of Package {}", self.get_name()),
            None,
        )
        .await;

        let mut task_result = TaskResult::default();

        // if no package found return error
        if self.task.package.is_none() {
            error!("No package found for {:?}", self.task);
            self.write_task_log("No package found".to_owned(), Some("ERROR"))
                .await;
            task_result.status = TaskStatus::Failed;
            task_result.output = "".to_owned();
            task_result.exit_code = 99;
            return Ok(task_result);
        }

        // if no deployment found return error
        if self.task.deployment.is_none() {
            error!("No deployment found for {:?}", self.task);
            self.write_task_log("No deployment found".to_owned(), Some("ERROR"))
                .await;
            task_result.status = TaskStatus::Failed;
            task_result.output = "".to_owned();
            task_result.exit_code = 99;
            return Ok(task_result);
        }

        let package = self.task.package.as_ref().unwrap();

        let deployment = self.task.deployment.as_ref().unwrap();

        self.write_task_log(
            format!(
                "{} of {} has started processing",
                deployment.deployment_type,
                package
                    .display_name
                    .as_ref()
                    .unwrap_or(&"Unknown Package".to_owned())
            ),
            None,
        )
        .await;

        match self.download().await {
            Err(error) => {
                error!(?error, "Failed to download package {:?}", package);
                return Err(error.into());
            }
            Ok(file_attachment) => {
                let package = self.task.package.as_mut().unwrap();
                package.pkg_file_path = file_attachment;
                debug!("Package downloaded successfully");
            }
        };

        self.process(deployment.deployment_type.to_owned()).await
    }
}
