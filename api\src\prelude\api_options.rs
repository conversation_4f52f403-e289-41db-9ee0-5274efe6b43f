use std::fmt::Debug;

use serde_json::{json, Value};

#[derive(Clone)]
pub struct ApiOptions {
    host: String,
    timeout: u64,
    retry: u32,
    credentials: (String, String),
}

impl Debug for ApiOptions {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("ApiOptions")
            .field("host", &self.host)
            .field("timeout", &self.timeout)
            .field("retry", &self.retry)
            .finish()
    }
}

impl ApiOptions {
    pub fn new(host: String) -> Self {
        Self {
            host,
            ..Default::default()
        }
    }

    pub fn with_credential(&mut self, username: String, password: String) {
        self.credentials = (username, password);
    }

    pub fn timeout(&self) -> u64 {
        self.timeout
    }

    pub fn retry(&self) -> u32 {
        self.retry
    }

    pub fn host(&self) -> &str {
        &self.host
    }

    pub fn credentials(&self) -> Value {
        json!({
            "username": self.credentials.0,
            "password": self.credentials.1
        })
    }
}

impl Default for ApiOptions {
    fn default() -> Self {
        Self {
            host: Default::default(),
            retry: 3,
            timeout: 60,
            credentials: ("".to_owned(), "".to_owned()),
        }
    }
}
