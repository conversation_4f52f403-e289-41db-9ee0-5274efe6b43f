use libc::{sysconf, uname, utsname, _SC_PAGESIZE, _SC_PHYS_PAGES};
use logger::error;
use smbioslib::{table_load_from_device, SMBiosBaseboardInformation, SMBiosSystemInformation};
use std::{
    collections::HashMap,
    ffi::CStr,
    fs::{self, File},
    io::{BufRead, BufReader},
    mem::MaybeUninit,
};

fn read_os_release() -> HashMap<String, String> {
    let reader = match File::open("/etc/os-release") {
        Ok(file) => BufReader::new(file),
        Err(error) => {
            error!(?error, "Failed to read os-release");
            return HashMap::new();
        }
    };

    let mut key_map = HashMap::new();

    key_map.insert("NAME", "name");
    key_map.insert("VERSION", "version");
    key_map.insert("BUILD_ID", "build");
    key_map.insert("ID", "platform");
    key_map.insert("ID_LIKE", "platform_like");
    key_map.insert("VERSION_CODENAME", "code_name");
    key_map.insert("VERSION_ID", "_id");

    let mut value_map = HashMap::new();

    reader
        .lines()
        .into_iter()
        .filter_map(Result::ok)
        .filter(|line| line.split("=").count() == 2)
        .for_each(|line| {
            let parts = line
                .split("=")
                .map(|i| i.to_owned())
                .collect::<Vec<String>>();

            let mut value = parts.last().unwrap().chars();
            if parts.last().unwrap().starts_with("\"") {
                // cleanup double quote
                value.next();
                value.next_back();
            }

            let value = value.as_str().to_string();

            if let Some(key) = key_map.remove(parts.first().unwrap().as_str()) {
                value_map.insert(key.to_owned(), value);
            }
        });

    if let Some(version_id) = value_map.remove("_id") {
        let parts = version_id.split(".").collect::<Vec<&str>>();
        value_map.insert("major".to_owned(), parts[0].to_owned());
        if parts.len() > 1 {
            value_map.insert("minor".to_owned(), parts[1].to_owned());
        }
        if parts.len() > 2 {
            value_map.insert("patch".to_owned(), parts[2].to_owned());
        }
    }

    value_map
}

fn get_rhel_release() -> HashMap<String, String> {
    let content = match fs::read_to_string("/etc/redhat-release") {
        Ok(content) => content,
        Err(_) => {
            return HashMap::new();
        }
    };

    let mut value_map = HashMap::new();

    value_map.insert("version".to_owned(), content);
    value_map.insert("platform".to_owned(), "rhel".to_owned());
    value_map.insert("platform_like".to_owned(), "rhel".to_owned());

    value_map
}

fn get_oracle_linux_release() -> HashMap<String, String> {
    let content = match fs::read_to_string("/etc/oracle-release") {
        Ok(content) => content,
        Err(_) => {
            return HashMap::new();
        }
    };

    let mut value_map = HashMap::new();

    value_map.insert("version".to_owned(), content);
    value_map.insert("platform".to_owned(), "oracle".to_owned());
    value_map.insert("platform_like".to_owned(), "oracle".to_owned());

    value_map
}

fn get_gentoo_release() -> HashMap<String, String> {
    let content = match fs::read_to_string("/etc/gentoo-release") {
        Ok(content) => content,
        Err(_) => {
            return HashMap::new();
        }
    };

    let mut value_map = HashMap::new();

    value_map.insert("version".to_owned(), content);
    value_map.insert("platform".to_owned(), "gentoo".to_owned());
    value_map.insert("platform_like".to_owned(), "gentoo".to_owned());

    value_map
}

pub fn get_os_version() -> HashMap<String, String> {
    //name,version,major,minor,patch,build, platform,platformlike

    let mut value_map = HashMap::new();

    value_map.extend(read_os_release());

    value_map.extend(get_rhel_release());

    value_map.extend(get_oracle_linux_release());

    value_map.extend(get_gentoo_release());

    if let Some(name) = value_map.get("name") {
        if name.to_lowercase().contains("centos") {
            if value_map
                .get("platform")
                .is_some_and(|value| value.is_empty())
            {
                value_map.insert("platform".to_owned(), "centos".to_owned());
            }
        }
    }

    value_map
}

fn cpu_details() -> HashMap<String, String> {
    let reader = match File::open("/proc/cpuinfo") {
        Ok(file) => BufReader::new(file),
        Err(error) => {
            error!(?error, "Failed to read proc/cpuinfo");
            return HashMap::new();
        }
    };

    let mut value_map = HashMap::new();

    reader
        .lines()
        .into_iter()
        .filter_map(Result::ok)
        .filter(|line| line.contains("model\t"))
        .filter(|line| line.split(":").count() == 2)
        .for_each(|line| {
            value_map.insert(
                "cpu_subtype".to_owned(),
                line.split(":").last().unwrap().trim().to_string(),
            );
        });

    value_map
}

fn get_smbios() -> HashMap<String, String> {
    let bios = match table_load_from_device() {
        Ok(data) => data,
        Err(error) => {
            error!(?error, "Failed to get SMBIOS data");
            return HashMap::new();
        }
    };

    let mut value_map = HashMap::new();

    if let Some(sys_info) = bios.first::<SMBiosSystemInformation>() {
        value_map.insert(
            "hardware_vendor".to_string(),
            sys_info.manufacturer().to_utf8_lossy().unwrap_or_default(),
        );
        value_map.insert(
            "hardware_model".to_string(),
            sys_info.product_name().to_utf8_lossy().unwrap_or_default(),
        );
        value_map.insert(
            "hardware_version".to_string(),
            sys_info.version().to_utf8_lossy().unwrap_or_default(),
        );
        value_map.insert(
            "hardware_serial".to_string(),
            sys_info.serial_number().to_utf8_lossy().unwrap_or_default(),
        );
    }

    if let Some(board_info) = bios.first::<SMBiosBaseboardInformation>() {
        value_map.insert(
            "platform_vendor".to_string(),
            board_info
                .manufacturer()
                .to_utf8_lossy()
                .unwrap_or_default(),
        );
    }

    value_map
}

pub fn get_system_info() -> HashMap<String, String> {
    let mut value_map = HashMap::new();

    let mut uname_data: MaybeUninit<utsname> = MaybeUninit::uninit();

    unsafe {
        if uname(uname_data.as_mut_ptr()) == -1 {
            error!("Failed to read uname from libc");
        } else {
            let uname = uname_data.assume_init();
            value_map.insert(
                "cpu_type".to_owned(),
                CStr::from_ptr(uname.machine.as_ptr())
                    .to_string_lossy()
                    .to_string(),
            );
        }
    }

    value_map.extend(cpu_details());

    value_map.extend(get_smbios());

    unsafe {
        let pages = sysconf(_SC_PHYS_PAGES);
        let page_size = sysconf(_SC_PAGESIZE);

        let memory_size = pages * page_size;

        value_map.insert("physical_memory".to_owned(), memory_size.to_string());
    }

    value_map
}
