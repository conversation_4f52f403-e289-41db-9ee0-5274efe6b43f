use anyhow::Result;
use logger::{debug, info, ModuleLogger};
use std::{
    sync::{Arc, Mutex},
    time::Duration,
};
use tauri::{tray::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Manager};
use utils::shutdown::{get_shutdown_signal, start_shutdown_listener};
mod app_handlers;
mod app_state;
mod ipc_plugin;
mod patch_handlers;
mod software_handlers;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() -> Result<()> {
    let module_loger = ModuleLogger::new(
        "global",
        Some(dirs::config_dir().unwrap().join("endpointops")),
        Some("self_service".to_owned()),
    );

    let _guard = module_loger.guard();

    module_loger.set_global()?;

    info!("starting self service agent");
    info!("Agent Version: {}", env!("CARGO_PKG_VERSION"));
    info!(
        "Git Version: {} | {}",
        env!("GIT_BRANCH_OR_TAG"),
        env!("GIT_COMMIT_HASH")
    );

    let app_state = tauri::async_runtime::block_on(async move {
        // Start listening for Ctrl+C signals
        start_shutdown_listener();

        let app_state = app_state::AppState::init().await.unwrap_or_default();
        Ok::<_, anyhow::Error>(app_state)
    })?;

    // let (ipc_tx, _ipc_rx) = tokio::sync::mpsc::channel(10);

    debug!("Starting Application with state {app_state:?}");

    #[allow(unused_variables)]
    tauri::Builder::default()
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_os::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_opener::init())
        .plugin(ipc_plugin::init())
        .on_window_event(|window, event| {
            #[cfg(not(target_os = "linux"))]
            {
                match event {
                    tauri::WindowEvent::CloseRequested { api, .. } => {
                        if window.label() == "main" {
                            window.hide().unwrap(); // Hide the window instead of closing it
                            api.prevent_close(); // Prevent the default close behavior
                        }
                    }
                    _ => {} // Do nothing for other events
                }
            }
        })
        .setup(|app| {
            #[cfg(debug_assertions)]
            app.get_webview_window("main").unwrap().open_devtools();

            let window = app.get_webview_window("main").unwrap();

            #[cfg(not(target_os = "linux"))]
            window.set_skip_taskbar(true)?;

            // Setup Ctrl+C signal handling
            let app_handle = app.handle().clone();
            let mut shutdown_receiver = get_shutdown_signal();
            tauri::async_runtime::spawn(async move {
                if let Ok(_) = shutdown_receiver.recv().await {
                    info!("Received Shutdown signal, shutting down application gracefully");
                    app_handle.exit(0);
                    tokio::time::sleep(Duration::from_secs(1)).await;
                }
            });

            TrayIconBuilder::new()
                .icon(app.default_window_icon().unwrap().clone())
                .on_tray_icon_event(move |_, event| match event {
                    tauri::tray::TrayIconEvent::Click { .. } => {
                        if window.is_visible().unwrap() {
                            window.set_focus().unwrap();
                            return;
                        }
                        window.show().unwrap();
                    }
                    _ => {}
                })
                .icon_as_template(true)
                .tooltip("EndpointOps Self Service")
                .build(app)?;

            // 🔹 macOS: remove dock + task switcher
            #[cfg(target_os = "macos")]
            {
                use objc2::MainThreadMarker;
                use objc2_app_kit::{NSApplication, NSApplicationActivationPolicy};
                let mtm: MainThreadMarker = MainThreadMarker::new().unwrap();

                let app = NSApplication::sharedApplication(mtm);
                // remove Dock + Cmd+Tab presence
                app.setActivationPolicy(NSApplicationActivationPolicy::Accessory);
            }

            Ok(())
        })
        .manage(Arc::new(Mutex::new(app_state)))
        .invoke_handler(tauri::generate_handler![
            app_handlers::get_current_state,
            app_handlers::get_branding_image,
            software_handlers::get_softwares,
            software_handlers::trigger_software_installation,
            patch_handlers::get_missing_patches,
            patch_handlers::trigger_patch_installation,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");

    info!("Self service application finished");

    Ok(())
}
