use crate::WindowsUpdate;
use anyhow::Error as AnyhowError;
use quick_xml::DeError as XmlDecodeError;
use shell::ShellError;
use std::io::Error;
use thiserror::Error;
use wmi::WMIError;

#[derive(Error, Debug)]
pub enum WindowsXmlCheckerError {
    #[error("Windows XML Checker Error: Unable to convert {0}")]
    UnableToConvert(String),

    #[error("Windows XML Checker Error: File path not set for windows update to parse {0:?}")]
    FilePathNotSetForParsing(WindowsUpdate),

    #[error("Windows XML Checker Error: XML Parsing error {0:?}")]
    XmlParsingError(#[from] XmlDecodeError),

    #[error("Windows XML Checker Error: Unable to find file {0}")]
    FileNotFoundError(String),

    #[error("Windows XML Checker Error: No logger defined for parser {0}, please provide logger with logger() method")]
    NoLoggerFound(String),

    #[error("Windows XML Checker Error: I/O Error occured {0:?}")]
    IoError(#[from] Error),

    #[error("Windows XML Checker Error: WMI Error occured {0:?}")]
    WmiError(#[from] WMIError),

    #[error("Windows XML Checker Error: Shell Command Execution Error {0:?}")]
    ShellExecutionError(#[from] ShellError),

    #[error("Actor Error: Task execution error {0:?}")]
    Unknown(#[from] AnyhowError),
}
