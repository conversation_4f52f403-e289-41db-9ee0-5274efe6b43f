use super::command_executor::CommandExecutor;
use crate::{
    has_task::HasTask, log_task::LogTask, sync_task::SyncTask, TaskExecutable, TaskExecutionError,
};
use anyhow::{<PERSON>rro<PERSON>, Result};
use async_trait::async_trait;
use data_collection::{DataCollectionExtension, OsServices, Processes, Users};
use database::{
    data_types::TaskResult,
    models::{AgentMetadata, SystemActionCommand, Task, TaskStatus},
};
use logger::{debug, error, ModuleLogger};
use std::{path::Path, sync::Arc};
use tokio::sync::RwLock;
use utils::dir::get_current_dir;

#[derive(Debug)]
pub struct SystemActionTask {
    task: Task,
    logger: Arc<ModuleLogger>,
    agent_metadata: AgentMetadata,
    output: RwLock<String>,
}

impl SystemActionTask {
    pub fn new(task: Task, agent_metadata: AgentMetadata) -> Self {
        Self {
            task,
            output: RwLock::new(String::new()),
            logger: ModuleLogger::new("system_action", None, Some("system_action".to_owned())),
            agent_metadata,
        }
    }
}

impl HasTask for SystemActionTask {
    fn get_task(&self) -> &Task {
        &self.task
    }

    fn set_task(&mut self, task: Task) {
        self.task = task;
    }
}

#[async_trait]
impl LogTask for SystemActionTask {
    fn logger(&self) -> Arc<ModuleLogger> {
        Arc::clone(&self.logger)
    }

    fn get_task_dir(&self) -> Box<Path> {
        get_current_dir()
    }

    async fn create_task_dir(&self) -> Result<(), TaskExecutionError> {
        debug!("System Action task doesn't need to create directory");
        Ok(())
    }

    async fn remove_task_resources(&self) -> Result<(), TaskExecutionError> {
        debug!("System Action task doesn't have any resources");
        Ok(())
    }

    async fn write_task_log(&self, message: String, level: Option<&str>) {
        let msg = self.build_log_message(level, message);

        let mut value = self.output.write().await;
        *value = format!("{}{}\n", *value, msg);
    }

    async fn read_output(&self) -> String {
        let value = self.output.read().await;

        value.to_owned()
    }
}

impl SyncTask for SystemActionTask {}

#[async_trait]
impl TaskExecutable for SystemActionTask {
    async fn execute(&mut self) -> Result<TaskResult, Error> {
        self.write_task_log(
            format!("Initiating execution of system action {}", self.get_name()),
            None,
        )
        .await;

        let mut task_result = TaskResult::default();

        // if no compliance found return error
        if self.task.custom_task_details.is_none() {
            error!("No SystemAction detail found for {:?}", self.task);
            self.write_task_log("No SystemAction detail found".to_owned(), Some("ERROR"))
                .await;
            task_result.status = TaskStatus::Failed;
            task_result.output = "".to_owned();
            task_result.exit_code = 99;
            return Ok(task_result);
        }

        let system_action_command: SystemActionCommand =
            self.task.custom_task_details.as_ref().unwrap().into();

        for command_detail in system_action_command.command {
            let mut command_executor =
                CommandExecutor::new_command(&command_detail.command, Box::new(self));

            // if create user/ change password action we skip logging command
            if self.get_task().resource_id == 11 || self.get_task().resource_id == 12 {
                command_executor = command_executor.skip_task_log();
            }

            let output = command_executor.execute().await;
            if let Err(error) = output {
                error!(
                    ?error,
                    "Failed to execute command {}", command_detail.command
                );
                task_result.exit_code = 99;
                task_result.status = TaskStatus::Failed;
                break;
            } else {
                let output = output.unwrap();
                debug!(
                    "command {} executed with exit code {}",
                    command_detail.command, output.exit_code
                );
                // if create user/ change password action we log exit code only without command as command has password in it
                if self.get_task().resource_id == 11 || self.get_task().resource_id == 12 {
                    self.write_task_log(
                        format!("\ncommand executed with exit code {}", output.exit_code),
                        if output.succeeded() {
                            None
                        } else {
                            Some("ERROR")
                        },
                    )
                    .await;
                }
                task_result.exit_code = output.exit_code;
                task_result.status = if output.succeeded() {
                    TaskStatus::Success
                } else {
                    TaskStatus::Failed
                };
                // if command is failed remove it
                if output.failed() {
                    break;
                }
            }
        }

        if task_result.status == TaskStatus::Success {
            debug!("Checking if we need to reload services, processes, remote connections, listening ports");
            if self.get_task().resource_id == 8 || self.get_task().resource_id == 9 {
                debug!("Reloading services");
                // reload services as start or stop service
                let mut extension = OsServices::new(&self.agent_metadata);
                extension.collect_and_send(None).await.ok();
            } else if self.get_task().resource_id == 1
                || self.get_task().resource_id == 6
                || self.get_task().resource_id == 5
            {
                debug!("Reloading processes, remote connections and listening ports");
                // reload processes, remote connections, listening ports
                let mut extension = Processes::new(&self.agent_metadata);
                extension.collect_and_send(None).await.ok();
            } else if self.get_task().resource_id == 11 || self.get_task().resource_id == 12 {
                debug!("Reloading users");
                // reload users
                let mut extension = Users::new(&self.agent_metadata);
                extension.collect_and_send(None).await.ok();
            }
        }

        Ok(task_result)
    }
}
