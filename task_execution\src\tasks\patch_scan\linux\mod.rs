use async_trait::async_trait;
use serde_json::Value;
use shell::ShellCommand;

use crate::{
    tasks::patch_scan::linux::{deb::DebPatchScanner, rpm::RpmPatchScanner},
    TaskExecutable, TaskExecutionError,
};

mod deb;
mod rpm;

#[async_trait]
pub trait LinuxPatchScanner: Send + Sync {
    async fn scan(&mut self) -> anyhow::Result<()>;
}

pub async fn resolve_linux_patch_scanner<'a>(
    endpoint_id: i64,
    installed_version: &Value,
    task: Box<&'a dyn TaskExecutable>,
) -> Result<Box<dyn LinuxPatchScanner + 'a>, TaskExecutionError> {
    let output = ShellCommand::new("rpm --version").run().await?;

    Ok(if output.succeeded() {
        Box::new(RpmPatchScanner::new(endpoint_id, task))
    } else {
        Box::new(DebPatchScanner::new(endpoint_id, installed_version, task))
    })
}
