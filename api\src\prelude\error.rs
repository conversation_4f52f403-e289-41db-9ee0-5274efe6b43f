use database::models::FileAttachment;
use reqwest::<PERSON>rro<PERSON> as ReqwestError;
use reqwest_middleware::Error as ReqwestMiddlewareError;
use serde_json::Error as SerdeError;
use shell::ShellError;
use std::io::Error;
use thiserror::Error;
use tokio::task::JoinError;

use crate::client::Token;

#[derive(Error, Debug)]
pub enum ApiError {
    #[error("Api Error: Failed to set api client as once lock")]
    NotSetOnce,

    #[error("Api Error: URL Parse Error {0}")]
    UrlParseError(String),

    #[error("Api Error: URL File name decoding Error {0}")]
    UrlDecodeError(String),

    #[error("Api Error: Timeout Error while receiving chunk {0}")]
    StreamChunkTimeoutError(u64),

    #[error("Api Error: Unable to determine file name from public URL {0}")]
    UnableToGetFileName(String),

    #[error("Api Error: Unable to mount shared drive {0}")]
    UnableToMountSharedDrive(String),

    #[error("Api Error: Downloading file without where to save local path {0:?}")]
    AttachmentLocationNotSet(FileAttachment),

    #[error("Api Error: Unable to determined from where to download attachment {0:?}")]
    InvalidAttachment(FileAttachment),

    #[error("Api Error: Failed to join tokio access token refresh task {0:?}")]
    TokioJoinError(#[from] JoinError),

    #[error("Api Error: Failed to write bytes to file stream {0:?}")]
    IoError(#[from] Error),

    #[error("Api Error: token error {0:?}")]
    TokenError(Token),

    #[error("Api Error: Error occured in api client {0:?}")]
    RequestError(#[from] ReqwestError),

    #[error("Api Error: Shell command Error occured in api client {0:?}")]
    NetworkDriveDownloadError(#[from] ShellError),

    #[error("Api Error: Failed to save file from network drive {0}")]
    UnableToSaveFileFromNetwork(String),

    #[error("Api Error: Error occured in api middleware client {0:?}")]
    RequestMiddleWareError(#[from] ReqwestMiddlewareError),

    #[error("Api Error: Error occured in serde serialisation/deserialisation {0:?}")]
    DecodeError(#[from] SerdeError),

    #[error("Api Error: Failed to login with given credentials")]
    LoginFailed,

    #[error("Api Error: Failed to read all bytes {0}")]
    BytesError(String),

    #[error("Api Error: Failed to decode json {0} response is {1}")]
    JsonDecodeError(String, String),

    #[error("Api Error: Request {0} Failed with status {1} response is {2}")]
    StatusError(String, String, String),

    #[error("Please initialise api with ApiOptions using Api::init(option) method")]
    Uninitialised,
}
