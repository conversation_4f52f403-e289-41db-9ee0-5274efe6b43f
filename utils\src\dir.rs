use std::{
    env::{current_dir, current_exe},
    path::Path,
};

#[inline]
pub fn get_current_dir() -> Box<Path> {
    if cfg!(debug_assertions) {
        Box::from(current_dir().unwrap().as_path().to_owned())
    } else {
        let dir = current_exe();

        if dir.is_err() {
            panic!("Failed to get current directory");
        }

        let mut dir = dir.unwrap();

        dir.pop();

        Box::from(dir.as_path().to_owned())
    }
}

#[inline]
pub fn get_patch_dir() -> Box<Path> {
    Box::from(get_current_dir().join("patches").to_owned())
}

#[inline]
pub fn get_patch_xml_dir() -> Box<Path> {
    Box::from(get_current_dir().join("patches").join("xmls").to_owned())
}

#[inline]
pub fn get_patch_cab_dir() -> Box<Path> {
    Box::from(get_current_dir().join("cabs").to_owned())
}

#[inline]
pub fn get_log_dir() -> Box<Path> {
    Box::from(get_current_dir().join("logs").to_owned())
}

#[inline]
pub fn get_db_dir() -> Box<Path> {
    Box::from(get_current_dir().join("data").to_owned())
}

#[inline]
pub fn get_task_execution_dir(task_id: impl Into<String>) -> Box<Path> {
    Box::from(
        get_current_dir()
            .join("tasks")
            .join(task_id.into())
            .as_path()
            .to_owned(),
    )
}
