use crate::{
    fim_event::FIM<PERSON><PERSON>, glob_paths::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Confi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ricFI<PERSON><PERSON>,
    Watcher,
};
use api::data_collection::send_fim_data;
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use database::{
    models::{FIMConfig, FIMConfigType},
    Model, Uuid,
};
use logger::{debug, error, info, trace, ModuleLogger, WithSubscriber};
use notify_debouncer_full::{
    new_debouncer, notify::RecursiveMode, DebounceEventResult, DebouncedEvent,
};
use serde_json::json;
use std::{
    collections::{HashMap, HashSet},
    path::PathBuf,
    sync::Arc,
    time::Duration,
};
use tokio::{
    fs::File,
    sync::{
        broadcast::Receiver as BroadcastReceiver,
        mpsc::{channel, Receiver, Sender},
        Mutex,
    },
    task::Jo<PERSON><PERSON><PERSON><PERSON>,
};
use tokio_stream::{wrappers::ReceiverStream, StreamExt};
use utils::{dir::get_log_dir, shutdown::is_system_running};

pub struct FIMWatcher {
    logger: Arc<ModuleLogger>,
    stop_signal_receiver: Arc<Mutex<BroadcastReceiver<bool>>>,
    config: Arc<Vec<FIMConfig>>,
    endpoint_id: i64,
}

impl FIMWatcher {
    pub fn new(
        config: Vec<FIMConfig>,
        endpoint_id: i64,
        stop_signal_receiver: BroadcastReceiver<bool>,
    ) -> Self {
        Self {
            logger: ModuleLogger::new(
                "fim",
                Some(get_log_dir().join("fim")),
                Some("notify-watcher".to_owned()),
            ),
            endpoint_id,
            stop_signal_receiver: Arc::new(Mutex::new(stop_signal_receiver)),
            config: Arc::new(config),
        }
    }

    fn start_event_receiver(
        &self,
        rx: Receiver<Vec<DebouncedEvent>>,
        event_processor: Sender<(FIMConfig, FIMEvent)>,
        paths_to_watch: HashMap<i64, (GlobPaths, GlobPaths, FIMConfig)>,
    ) -> JoinHandle<()> {
        tokio::task::spawn(
            async move {
                let mut stream = ReceiverStream::new(rx);

                while let Some(events) = stream.next().await.take_if(|_| is_system_running()) {
                    let system_events = events
                        .into_iter()
                        .take_while(|_| is_system_running())
                        .filter_map(|item| {
                            if item.event.paths.len() == 0 {
                                None
                            } else {
                                match item.event.paths.first() {
                                    Some(path) => {
                                        let related_configs: HashMap<
                                            &i64,
                                            &(GlobPaths, GlobPaths, FIMConfig),
                                        > = paths_to_watch
                                            .iter()
                                            .filter(|(_, item)| {
                                                debug!(
                                                    "include match {}",
                                                    item.0.matches(path.as_path())
                                                );
                                                debug!(
                                                    "exclude match {}",
                                                    item.1.matches(path.as_path())
                                                );
                                                // should match include path and not match exclude path
                                                item.0.matches(path.as_path())
                                                    && item.1.matches(path.as_path()) == false
                                            })
                                            .collect();
                                        if let Some((_, config)) = related_configs.iter().next() {
                                            Some((item, &config.2))
                                        } else {
                                            None
                                        }
                                    }
                                    None => None,
                                }
                            }
                        })
                        .map(|(event, config)| GenericFIMEvent::new(event.event, config.clone()))
                        .collect::<Vec<GenericFIMEvent>>();

                    let mut events = HashSet::new();

                    for system_event in system_events {
                        let fim_event = system_event.build_fim_event().await;
                        if fim_event.should_skip() == false {
                            events.insert(fim_event);
                        }
                    }

                    if is_system_running() {
                        for event in events {
                            let (_, _, config) = paths_to_watch.get(event.config_id()).unwrap();
                            if let Err(error) = event_processor.send((config.clone(), event)).await
                            {
                                error!(?error, "Failed to send event to processor task");
                            }
                        }
                    }
                }

                info!("Event Loop exited");
            }
            .with_subscriber(self.logger.subscriber()),
        )
    }

    fn start_event_processor(&self, mut rx: Receiver<(FIMConfig, FIMEvent)>) -> JoinHandle<()> {
        tokio::task::spawn(
            async move {
                while let Some((config, fim_event)) = rx.recv().await {
                    debug!("FIM Event: {:?}", fim_event);
                    if fim_event.config_type() == &FIMConfigType::ConfigFile {
                        ConfigFileHelper::sync_event(&config, fim_event).await;
                    } else {
                        match fim_event.persist().await {
                            Err(error) => {
                                error!(?error, "Failed to persist event {:?}", fim_event);
                            }
                            _ => {}
                        };
                    }
                }
                debug!("Finished Generic Event Processor Task");
            }
            .with_subscriber(self.logger.subscriber()),
        )
    }

    async fn config_file_initialization(&self, path: &PathBuf, config: &FIMConfig) {
        let mut event = FIMEvent::default();
        event.set_target_path(path.to_string_lossy().to_string());
        event.generate_hash().await;

        if ConfigFileHelper::should_send_file(&event).await {
            if let Some(file) = ConfigFileHelper::send_file(&event).await {
                let event_time = match File::open(path).await {
                    Ok(f) => match f.metadata().await {
                        Ok(metadata) => {
                            Into::<DateTime<Utc>>::into(metadata.created().unwrap()).timestamp()
                        }
                        Err(error) => {
                            error!(?error, "Failed to open file metadata {}", path.display());
                            chrono::Utc::now().timestamp()
                        }
                    },
                    Err(error) => {
                        error!(?error, "Failed to open file {}", path.display());
                        chrono::Utc::now().timestamp()
                    }
                };
                // here send first time file change api
                match send_fim_data(json!({
                    "asset_id" : self.endpoint_id,
                    "data" : {
                        "fim_config_file" : {
                            "file_name" : file.file_name,
                            "file_path" : path.to_string_lossy().to_string(),
                            "event_id" : Uuid::new_v4().to_string(),
                            "event_time" : event_time,
                            "config_id" : config.id(),
                            "ref_name" : file.file_ref
                    }
                  }
                }))
                .await
                {
                    Ok(_) => debug!("Sent first time file change api"),
                    Err(error) => error!(?error, "Failed to send first time file change api"),
                };
            }
        }
    }
}

#[async_trait]
impl Watcher for FIMWatcher {
    async fn watch(&self) -> anyhow::Result<(), FIMError> {
        async {
            let mut shutdown_receiver = self.stop_signal_receiver.lock().await;

            let mut paths_to_watch = HashMap::new();

            let mut all_root_paths = vec![];

            for config in self.config.iter() {
                if config.config_type == FIMConfigType::ConfigFile {
                    if let Some(path) = config.paths().first() {
                        if !path.exists() {
                            error!(
                                "Config file not found at path {}",
                                path.to_string_lossy().to_string()
                            );
                        } else if config.config_type == FIMConfigType::Registry {
                            error!(
                                "Registry config type is not supported in generic watcher {:?}",
                                config
                            );
                        } else {
                            paths_to_watch.insert(
                                config.id(),
                                (
                                    GlobPaths::new(config.paths().clone()),
                                    GlobPaths::new(config.excluded_paths().clone()),
                                    config.clone(),
                                ),
                            );
                            all_root_paths.extend(config.paths().clone());
                            self.config_file_initialization(path, config).await;
                        }
                    } else {
                        error!("No Config file path is given {:?}", config);
                    }
                } else {
                    let included_glob_path = GlobPaths::new(config.paths().clone());
                    let excluded_glob_path = GlobPaths::new(config.excluded_paths().clone());
                    if included_glob_path.is_empty() {
                        error!("No include path is found for config {:?}", config);
                    } else {
                        all_root_paths.extend(included_glob_path.get_root_paths());
                        paths_to_watch.insert(
                            config.id(),
                            (included_glob_path, excluded_glob_path, config.clone()),
                        );
                    }
                }
            }

            debug!("collected paths {:?}", paths_to_watch);
            if paths_to_watch.len() == 0 {
                info!("No config is found to watch");
                shutdown_receiver.recv().await.ok();
                return Ok(());
            }
            let (tx, rx) = channel(10);

            let (event_processor_tx, event_processor_rx) = channel(10);

            let mut event_receiver_task =
                self.start_event_receiver(rx, event_processor_tx, paths_to_watch);
            let mut event_processor_task = self.start_event_processor(event_processor_rx);

            let mut watcher = match new_debouncer(
                Duration::from_secs(1),
                None,
                move |res: DebounceEventResult| match res {
                    Ok(events) => {
                        match tx.blocking_send(events) {
                            Ok(_) => trace!("Send Event to tokio notifier"),
                            Err(error) => {
                                error!(?error, "Failed to publish event to tokio notifier");
                            }
                        };
                    }

                    Err(e) => error!("Notify Errors: {:?}", e),
                },
            ) {
                Ok(watcher) => watcher,
                Err(error) => {
                    error!(
                        ?error,
                        "Failed to initialise watcher for config {:?}", self.config
                    );
                    shutdown_receiver.recv().await.ok();
                    return Err(error.into());
                }
            };

            for path in all_root_paths.iter() {
                debug!("Watching path {}", path.display());
                match watcher.watch(path, RecursiveMode::Recursive) {
                    Ok(_) => {
                        debug!("Started watching path {}", path.display());
                    }
                    Err(error) => {
                        error!(?error, "Failed to watch path {}", path.display());
                    }
                }
            }

            tokio::select! {
                biased;

                _ = shutdown_receiver.recv() => {
                    info!("Shutting down file watcher");
                },

                result = &mut event_receiver_task => {
                    match result {
                        Ok(()) => {
                            debug!("Generic event receiver has finished execution");
                        }
                        Err(error) => {
                            error!(
                                ?error,
                                "Failed to join Generic event receiver thread"
                            );
                            shutdown_receiver.recv().await.ok();
                        }
                    }
                },

                result = &mut event_processor_task => {
                    match result {
                        Ok(()) => {
                            debug!("Generic event processor task has finished execution");
                        }
                        Err(error) => {
                            error!(
                                ?error,
                                "Failed to join Generic event processor task"
                            );
                            shutdown_receiver.recv().await.ok();
                        }
                    }
                }
            };

            debug!("Stopped Generic FIM Watcher");

            Ok(())
        }
        .with_subscriber(self.logger.subscriber())
        .await
    }
}
