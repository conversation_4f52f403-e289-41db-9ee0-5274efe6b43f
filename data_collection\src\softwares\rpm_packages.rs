use super::software::{PackageType, Software};
use logger::error;
use serde::Deserialize;
use serde_json::json;
use shell::ShellCommand;
use std::collections::HashSet;
use utils::{runtime::create_new_runtime, shutdown::is_system_running};

#[derive(Debug, Deserialize)]
pub struct RpmPackage {
    name: String,
    version: String,
    release: String,
    arch: String,
    description: String,
    size: String,
    sha1: String,
    source: String,
    vendor: String,
    install_time: u64,
}

impl From<RpmPackage> for Software {
    fn from(value: RpmPackage) -> Self {
        Self {
            name: value.name,
            version: value.version,
            r#type: PackageType::RpmPackages,
            vendor: value.vendor,
            release: value.release,
            arch: value.arch,
            properties: json!({
                "description": value.description,
                "size": value.size,
                "sha1": value.sha1,
                "source": value.source,
                "install_time": value.install_time
            }),
            ..Default::default()
        }
    }
}

impl RpmPackage {
    pub fn collect() -> HashSet<Software> {
        let runtime = create_new_runtime();
        match runtime.block_on(async { ShellCommand::new("rpm --version").run().await }) {
            Ok(output) => {
                if output.failed() {
                    return HashSet::new();
                }
            }
            Err(error) => {
                error!(?error, "Failed to verify rpm");
                return HashSet::new();
            }
        };
        let command = runtime.block_on(async { ShellCommand::new(r#"rpm -qa --queryformat '"%{NAME}","%{VERSION}","%{RELEASE}","%{ARCH}","%{SUMMARY}","%{SIZE}","%{SHA1HEADER}","%{SOURCERPM}","%{VENDOR}","%{INSTALLTIME}"\n'"#).run().await });
        if let Err(error) = command {
            error!(?error, "Failed to execute rpm command");
            return HashSet::new();
        }

        let output = command.unwrap().output;
        let mut byte_reader = csv::ReaderBuilder::new()
            .has_headers(false)
            .double_quote(true)
            .from_reader(output.as_bytes());

        byte_reader
            .deserialize::<RpmPackage>()
            .into_iter()
            .take_while(|_| is_system_running())
            .filter_map(Result::ok)
            .map(|p| p.into())
            .collect()
    }
}
