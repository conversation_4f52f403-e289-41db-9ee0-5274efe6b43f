import mime from 'mime-types';
import { downloadDir, join } from '@tauri-apps/api/path';
import { save } from '@tauri-apps/plugin-dialog';
import { writeFile } from '@tauri-apps/plugin-fs';

const downloadDirPath = await downloadDir();

function splitFilename(filename) {
  const parts = filename.split('.');
  const extension = parts.pop(); // Removes and returns the last element (extension)
  const baseName = parts.join('.'); // Joins the remaining parts to form the filename

  return { baseName, extension };
}

export async function downloadFile(blob, url, name) {
  let { baseName, extension } = splitFilename(name);
  const path = await join(downloadDirPath, name);
  // 1. Ask user where to save
  const filePath = await save({
    defaultPath: path,
    filters: [{ name: baseName, extensions: [extension] }]
  });

  if (!filePath) return; // user canceled

  // 2. Convert Blob -> ArrayBuffer -> Uint8Array
  const arrayBuffer = await blob.arrayBuffer();
  const buffer = new Uint8Array(arrayBuffer);

  // 3. Write file to disk
  await writeFile(filePath, buffer);
}

export function blobToBase64(data, fileName) {
  return new Promise((resolve, _) => {
    let mimeType = fileName ? mime.lookup(fileName) : data.type;
    let updatedBlob = new Blob([data], { type: mimeType });
    const reader = new FileReader();
    reader.onloadend = () => resolve(reader.result);
    reader.readAsDataURL(updatedBlob);
  });
}
