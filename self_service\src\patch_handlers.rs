use std::sync::{Arc, Mutex};

use api::{
    self_service::{create_deployment, get_missing_patches as get_missing_patch_api},
    ApiResponseWithCount,
};
use logger::debug;
use serde_json::{json, Value};
use tauri::State;

use crate::app_state::AppState;

#[tauri::command]
pub async fn get_missing_patches(payload: Value) -> Result<ApiResponseWithCount<Value>, String> {
    debug!("Received Payload {payload:?}");

    let patches = get_missing_patch_api::<Value>(payload)
        .await
        .map_err(|e| e.to_string())?;

    debug!("Got missing patches {:?}", patches);

    Ok(patches)
}

#[tauri::command]
pub async fn trigger_patch_installation(
    state: State<'_, Arc<Mutex<AppState>>>,
    patch: Value,
) -> Result<(), String> {
    debug!("Received install request for {patch:?}");

    let patch_obj = patch.as_object().unwrap();

    let app_state = state.lock().unwrap().clone();

    let title = patch_obj
        .get("title")
        .and_then(|v| v.as_str())
        .unwrap_or_default();

    let response = create_deployment(json!({
        "IsPkgSelectAsBundle": false,
        "refModel": "Patch",
        "displayName": format!("SS: Install Patch {}", title),
        "description": format!("SS: Install Patch {}", title),
        "deploymentType": "install",
        "deploymentStage": "initiated",
        "refIds": [patch_obj.get("id").unwrap_or_default()],
        "scope": 2,
        "assets": [app_state.agent.get_endpoint_id()],
        "deploymentPolicyId": 1,
        "notifyEmailIds": [],
        "retryCount": 1
    }))
    .await
    .map_err(|e| e.to_string())?;

    debug!("Got response of deployment creation {response:?}");

    Ok(())
}
