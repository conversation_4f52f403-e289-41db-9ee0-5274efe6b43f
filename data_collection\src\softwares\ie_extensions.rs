use super::software::{PackageType, Software};
use logger::error;
use serde_json::json;
use std::{
    collections::HashSet,
    ffi::OsStr,
    os::windows::ffi::OsStrExt,
    path::{Path, PathBuf},
};
use utils::shutdown::is_system_running;
use windows::{
    core::PCWSTR,
    Win32::Storage::FileSystem::{
        GetFileVersionInfoSizeW, GetFileVersionInfoW, VerQueryValueW, VS_FIXEDFILEINFO,
    },
};
use windows_registry::WinRegistry;

#[derive(Debug, Hash, PartialEq, Eq, Default)]
pub struct IEExtension {
    name: String,
    version: String,
    path: String,
    registry_path: String,
}

impl From<IEExtension> for Software {
    fn from(value: IEExtension) -> Self {
        Self {
            name: value.name,
            version: value.version,
            r#type: PackageType::IEExtension,
            properties: json!({
                "path": value.path,
                "registry_path": value.registry_path
            }),
            ..Default::default()
        }
    }
}

impl IEExtension {
    fn user_keys() -> Vec<String> {
        WinRegistry::get_users_key()
    }

    fn file_version(path: &PathBuf) -> Option<String> {
        unsafe {
            // Path to the file
            let file_path = OsStr::new(path.to_str().unwrap())
                .encode_wide()
                .chain(Some(0).into_iter())
                .collect::<Vec<_>>();

            // Get the size of the version info
            let size = GetFileVersionInfoSizeW(PCWSTR(file_path.as_ptr()), None);
            if size == 0 {
                error!("Failed to get version info size");
                return None;
            }

            // Allocate buffer for version info
            let mut buffer = vec![0u8; size as usize];

            // Get the version info
            if GetFileVersionInfoW(
                PCWSTR(file_path.as_ptr()),
                None,
                size,
                buffer.as_mut_ptr() as *mut _,
            )
            .is_err()
            {
                error!("Failed to get version info");
                return None;
            }

            // Query the version value
            let mut ffi: *mut VS_FIXEDFILEINFO = std::ptr::null_mut();
            let mut len: u32 = 0;
            if VerQueryValueW(
                buffer.as_ptr() as *const _,
                PCWSTR("\\\0".encode_utf16().collect::<Vec<_>>().as_ptr()),
                &mut ffi as *mut _ as *mut _,
                &mut len,
            )
            .as_bool()
                == false
            {
                error!("Failed to query version value");
                return None;
            }

            // Print version info
            if ffi.is_null() {
                error!("FFI call received null");
                return None;
            }
            let ffi = &*ffi;
            let file_version = (
                (ffi.dwFileVersionMS >> 16) & 0xffff,
                (ffi.dwFileVersionMS >> 0) & 0xffff,
                (ffi.dwFileVersionLS >> 16) & 0xffff,
                (ffi.dwFileVersionLS >> 0) & 0xffff,
            );
            Some(format!(
                "{}.{}.{}.{}",
                file_version.0, file_version.1, file_version.2, file_version.3
            ))
        }
    }

    fn get_registry_keys(user_keys: &Vec<String>) -> HashSet<String> {
        [
            "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Browser Helper Objects".to_owned(),
            "HKEY_LOCAL_MACHINE\\SOFTWARE\\WOW6432Node\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Browser Helper Objects".to_owned()
        ].into_iter().chain(
            user_keys.into_iter()
                .flat_map(|id| [
                    format!("HKEY_USERS\\{}\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Browser Helper Objects", id),
                    format!("HKEY_USERS\\{}\\SOFTWARE\\WOW6432Node\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Browser Helper Objects", id),
                    format!("HKEY_USERS\\{}\\SOFTWARE\\Microsoft\\Internet Explorer\\URLSearchHooks", id),
                ])
        ).collect()
    }

    fn get_class_executable_paths(class_id: &str, user_keys: &Vec<String>) -> HashSet<String> {
        ["HKLM\\SOFTWARE\\Classes\\CLSID".to_owned()]
            .into_iter()
            .chain(
                user_keys
                    .into_iter()
                    .map(|key| format!("HKU\\{}\\SOFTWARE\\Classes\\CLSID", key)),
            )
            .map(|item| format!("{}\\{}", item, class_id))
            .flat_map(|path| {
                [
                    format!("{}\\InProcServer", path),
                    format!("{}\\InProcHandler", path),
                    format!("{}\\LocalServer", path),
                ]
            })
            .collect()
    }

    fn collect_class_paths(path: &str, user_keys: &Vec<String>) -> HashSet<PathBuf> {
        match WinRegistry::new(PathBuf::from(&path)) {
            Ok(registry) => {
                let values = registry
                    .values(None)
                    .keys()
                    .map(|key| key.to_owned())
                    .collect::<Vec<String>>();
                registry
                    .keys()
                    .into_iter()
                    .chain(values.into_iter())
                    .filter(|value| value.is_empty() == false)
                    .flat_map(|key| IEExtension::get_class_executable_paths(&key, user_keys))
                    .map(|i| PathBuf::from(i))
                    .collect()
            }
            Err(_) => return HashSet::new(),
        }
    }

    fn collect_from_registry(registry_path: &str, path: &Path) -> Option<IEExtension> {
        let reg = match WinRegistry::new(&path.parent().unwrap()) {
            Ok(reg) => reg,
            Err(_) => return None,
        };

        let name_key: String = reg.get_value("".to_owned());

        let last_path = path.file_stem().unwrap().to_string_lossy().to_string();

        let available_sub_keys: Vec<String> = reg
            .keys()
            .into_iter()
            .filter(|i| {
                i.to_lowercase()
                    .starts_with(last_path.to_lowercase().as_str())
            })
            .collect();

        if available_sub_keys.len() == 0 {
            return None;
        }
        let file_path: String = match WinRegistry::new(
            &path
                .parent()
                .unwrap()
                .join(available_sub_keys.first().unwrap()),
        ) {
            Ok(reg) => reg.get_value("".to_owned()),
            Err(_) => return None,
        };

        if name_key.is_empty() || file_path.is_empty() {
            return None;
        }
        if let Some(version) = IEExtension::file_version(&PathBuf::from(&file_path)) {
            let mut ie_extension = IEExtension::default();
            ie_extension.name = name_key;
            ie_extension.registry_path = registry_path.to_owned();
            ie_extension.path = file_path;
            ie_extension.version = version;
            Some(ie_extension)
        } else {
            error!("Failed to get version for file {}", file_path);
            None
        }
    }

    pub fn collect() -> HashSet<Software> {
        let user_keys = IEExtension::user_keys();
        let mut softwares = HashSet::new();
        for registry_path in IEExtension::get_registry_keys(&user_keys) {
            softwares.extend(
                IEExtension::collect_class_paths(&registry_path, &user_keys)
                    .into_iter()
                    .take_while(|_| is_system_running())
                    .map(|path| IEExtension::collect_from_registry(&registry_path, path.as_path()))
                    .filter(|i| i.is_some())
                    .map(|i| i.unwrap())
                    .map(|i| i.into())
                    .collect::<HashSet<Software>>(),
            );
        }

        softwares
    }
}
