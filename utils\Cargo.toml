[package]
name = "utils"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[features]
default = []
self_service = []

[dependencies]
serde = { workspace = true }
service-manager = "0.8.0"
anyhow = { workspace = true }
tokio = { workspace = true }
serde_json = { workspace = true }
tracing = { version = "0.1.41", features = ["log", "std"] }
bincode = { version = "2.0.1", features = ["serde"] }
interprocess = { version = "2.2.3", features = ["tokio"] }
read_until_slice = "0.1.13"

[target.'cfg(windows)'.dependencies]
encoding_rs_io = "0.1.7"
encoding_rs = "0.8.35"
windows = { version = "0.61.3", features = [
  # Core APIs
  "Win32_Foundation",
  "Win32_Security",
  "Win32_System_Threading",
  "Win32_System_Environment",
  "Win32_System_WindowsProgramming",
  "Win32_System_RemoteDesktop",
  "Win32_System_SystemServices",
  "Win32_System_StationsAndDesktops",
  "Win32_System_LibraryLoader",
  "Win32_UI",
  "Win32_UI_WindowsAndMessaging",
  "Win32_Graphics_Gdi",
] }
