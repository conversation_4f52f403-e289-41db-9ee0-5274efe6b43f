use crate::app_state::AppState;
use api::self_service::get_branding;
use logger::debug;
use std::{
    path::PathBuf,
    sync::{Arc, Mutex},
};
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, State};
use tokio::fs;

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
pub fn get_current_state(state: State<'_, Arc<Mutex<AppState>>>) -> AppState {
    state.lock().unwrap().clone()
}

async fn write_default_logo(path: &PathBuf) -> Result<PathBuf, String> {
    let full_path = path.join("brand-logo.png");
    fs::write(&full_path, include_bytes!("../brand-logo.png"))
        .await
        .map_err(|e| e.to_string())?;
    debug!("Wrote default logo image at path {}", full_path.display());
    Ok(full_path)
}

#[tauri::command]
pub async fn get_branding_image(handle: AppHandle) -> Result<PathBuf, String> {
    let branding = get_branding().await.map_err(|e| e.to_string())?;

    let path = handle
        .path()
        .app_cache_dir()
        .map_err(|e| e.to_string())?
        .join("images");

    fs::create_dir_all(&path).await.map_err(|e| e.to_string())?;

    let value = branding
        .as_object()
        .and_then(|value| value.get("branding_image_ref"))
        .and_then(|value| value.as_str())
        .and_then(|value| {
            debug!("Got ref as {value}");
            let full_path = path.join(value);
            // fs::write(&full_path, include_bytes!("../brand-logo.png"))
            //     .await
            //     .map_err(|e| e.to_string())?;
            debug!("Wrote branding image at path {}", full_path.display());
            Some(full_path)
        });

    match value {
        Some(value) => Ok(value),
        None => write_default_logo(&path).await,
    }
}
