use crate::{ProcessEvent, WinDriverHandlerError};
use tracing::{error, trace};
use windows::{
    core::PWSTR,
    Win32::{
        Foundation::{CloseHandle, MAX_PATH},
        Security::{LookupAccountSidW, PSID, SID_NAME_USE},
        System::{
            RemoteDesktop::{
                WTSDomainName, WTSEnumerateProcessesW, WTSFreeMemory, WTSQuerySessionInformationW,
                WTSUserName, WTS_CURRENT_SERVER_HANDLE, WTS_INFO_CLASS, WTS_PROCESS_INFOW,
            },
            Threading::{
                OpenProcess, QueryFullProcessImageNameW, PROCESS_NAME_FORMAT,
                PROCESS_QUERY_LIMITED_INFORMATION,
            },
        },
    },
};

#[derive(Debug, Default, <PERSON>lone, PartialEq, Eq)]
pub struct ProcOwner {
    session_id: i32,
    pid: u32,
    proc_name: String,
    username: String,
    computer_name: String,
}
impl TryFrom<ProcessEvent> for ProcOwner {
    type Error = WinDriverHandlerError;

    fn try_from(value: ProcessEvent) -> Result<Self, Self::Error> {
        match ProcOwner::build_from_session_id(value.session_id, value.pid) {
            Some(mut proc_map) => {
                proc_map.proc_name = value.name.to_owned().unwrap_or_default();
                Ok(proc_map)
            }
            None => {
                error!(
                    "Failed to create process data from process_event {:?}",
                    value
                );
                Err(WinDriverHandlerError::FailedToRetriveProcessOwner(
                    value.to_owned(),
                ))
            }
        }
    }
}

impl ProcOwner {
    pub fn get_pid(&self) -> u32 {
        self.pid
    }

    pub fn get_user_name(&self) -> &str {
        &self.username
    }

    pub fn get_name(&self) -> &str {
        &self.proc_name
    }

    fn build_from_session_id(session_id: i32, pid: u32) -> Option<ProcOwner> {
        let username = ProcOwner::query_session_detail(session_id, WTSUserName);
        let computer_name = ProcOwner::query_session_detail(session_id, WTSDomainName);

        if username.is_some() && computer_name.is_some() {
            Some(ProcOwner {
                session_id,
                pid,
                username: username.unwrap_or_default(),
                computer_name: computer_name.unwrap_or_default(),
                ..Default::default()
            })
        } else {
            None
        }
    }

    fn query_session_detail(session_id: i32, info_class: WTS_INFO_CLASS) -> Option<String> {
        let mut buffer: PWSTR = PWSTR::null();
        let mut bytes_returned = 0u32;

        if let Err(error) = unsafe {
            WTSQuerySessionInformationW(
                Some(WTS_CURRENT_SERVER_HANDLE),
                session_id as u32,
                info_class,
                &mut buffer,
                &mut bytes_returned,
            )
        } {
            error!(
                ?error,
                "Failed to query session info for session {}", session_id
            );
            None
        } else {
            let str_slice =
                unsafe { std::slice::from_raw_parts(buffer.0, (bytes_returned / 2) as usize) };
            let result = String::from_utf16_lossy(str_slice);
            unsafe { WTSFreeMemory(buffer.0 as _) };
            Some(result.trim_end_matches('\0').to_string())
        }
    }

    pub fn get_all_running() -> Vec<ProcOwner> {
        let mut pp_process_info: *mut WTS_PROCESS_INFOW = std::ptr::null_mut();
        let mut count: u32 = 0;

        if let Err(error) = unsafe {
            WTSEnumerateProcessesW(
                Some(WTS_CURRENT_SERVER_HANDLE),
                0,
                1,
                &mut pp_process_info,
                &mut count,
            )
        } {
            error!(?error, "Failed to get all running processes");
            return vec![];
        }

        let mut data = vec![];

        let slice = unsafe { std::slice::from_raw_parts(pp_process_info, count as usize) };

        for process in slice {
            let mut proc_owner = ProcOwner {
                pid: process.ProcessId,
                session_id: process.SessionId as i32,
                proc_name: if !process.pProcessName.is_null() {
                    unsafe { process.pProcessName.to_string() }.unwrap_or_default()
                } else {
                    "".to_string()
                },
                ..Default::default()
            };
            trace!(
                "Got Process pid: {} name: {}",
                process.ProcessId,
                proc_owner.proc_name
            );
            if let Some((username, domain)) = ProcOwner::resolve_sid(process.pUserSid) {
                proc_owner.username = username;
                proc_owner.computer_name = domain;
            }

            if proc_owner.proc_name.is_empty() == false {
                proc_owner.proc_name = ProcOwner::get_proc_name_full_path(proc_owner.pid);
            }

            data.push(proc_owner);
        }

        unsafe { WTSFreeMemory(pp_process_info as _) };

        data
    }

    fn get_proc_name_full_path(pid: u32) -> String {
        let handle = unsafe { OpenProcess(PROCESS_QUERY_LIMITED_INFORMATION, false, pid) };

        if let Ok(handle) = handle {
            let mut buffer = [0u16; MAX_PATH as usize];
            let proc_name = PWSTR(buffer.as_mut_ptr());
            let mut size = buffer.len() as u32;

            if let Err(error) = unsafe {
                QueryFullProcessImageNameW(handle, PROCESS_NAME_FORMAT(0), proc_name, &mut size)
            } {
                error!(?error, "Error Getting process image name for pid {}", pid);
                return "".to_string();
            }
            unsafe {
                match CloseHandle(handle) {
                    Err(error) => {
                        error!(?error, "Failed to close handle for process image open");
                    }
                    _ => {}
                }
            };
            unsafe { proc_name.to_string() }.unwrap_or_default()
        } else {
            "".to_string()
        }
    }

    fn resolve_sid(sid: PSID) -> Option<(String, String)> {
        let mut name = [0u16; 256];
        let mut domain = [0u16; 256];
        let mut name_len = name.len() as u32;
        let mut domain_len = domain.len() as u32;
        let mut sid_name_use = SID_NAME_USE(0);

        if let Err(error) = unsafe {
            LookupAccountSidW(
                None, // local system
                sid,
                Some(PWSTR(name.as_mut_ptr())),
                &mut name_len,
                Some(PWSTR(domain.as_mut_ptr())),
                &mut domain_len,
                &mut sid_name_use,
            )
        } {
            error!(?error, "Failed to look up sid");
            return None;
        }

        let username = String::from_utf16_lossy(&name[..name_len as usize]);
        let domain_str = String::from_utf16_lossy(&domain[..domain_len as usize]);
        Some((username, domain_str))
    }
}
