use logger::error;
use serde::{Deserialize, Serialize};
use std::fmt::Display;
use surrealdb::sql::Thing;

// Use a different approach that avoids enum deserialization issues
#[derive(Debug, <PERSON><PERSON>, <PERSON>h, PartialEq, Eq)]
pub enum PrimaryKey {
    ZirozenId(i64),
    LocalId(String),
    Db(Thing),
}

// Custom Serialize implementation that handles both server and SurrealDB contexts
impl Serialize for PrimaryKey {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        match self {
            // For numeric IDs, always serialize as u64 for server compatibility
            PrimaryKey::ZirozenId(id) => {
                // Convert i64 to u64 for server compatibility
                let server_id = if *id < 0 { 0u64 } else { *id as u64 };
                server_id.serialize(serializer)
            }
            // For local IDs, try to parse as number first, otherwise serialize as string
            PrimaryKey::LocalId(id) => {
                if let Ok(numeric_id) = id.parse::<u64>() {
                    numeric_id.serialize(serializer)
                } else {
                    id.serialize(serializer)
                }
            }
            // For SurrealDB Thing objects, serialize the Thing directly
            // This will work for SurrealDB storage but may need conversion for server
            PrimaryKey::Db(thing) => {
                // Try to extract numeric ID from Thing if possible
                if let Ok(id_str) = thing.id.to_raw().parse::<u64>() {
                    id_str.serialize(serializer)
                } else {
                    // Fallback to serializing the entire Thing for SurrealDB compatibility
                    thing.serialize(serializer)
                }
            }
        }
    }
}

// Custom Deserialize implementation that works with SurrealDB
impl<'de> Deserialize<'de> for PrimaryKey {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: serde::Deserializer<'de>,
    {
        use serde::de::{self, Visitor};
        use std::fmt;

        struct PrimaryKeyVisitor;

        impl<'de> Visitor<'de> for PrimaryKeyVisitor {
            type Value = PrimaryKey;

            fn expecting(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
                formatter.write_str("a SurrealDB Thing, number, or string")
            }

            // Handle direct integer values (from server or JSON)
            fn visit_i64<E>(self, value: i64) -> Result<Self::Value, E>
            where
                E: de::Error,
            {
                Ok(PrimaryKey::ZirozenId(value))
            }

            fn visit_u64<E>(self, value: u64) -> Result<Self::Value, E>
            where
                E: de::Error,
            {
                Ok(PrimaryKey::ZirozenId(value as i64))
            }

            // Handle string values
            fn visit_str<E>(self, value: &str) -> Result<Self::Value, E>
            where
                E: de::Error,
            {
                // Check if it's a SurrealDB Thing string format (tableName:id)
                if value.contains(':') {
                    // Try to parse as SurrealDB Thing
                    match value.parse::<Thing>() {
                        Ok(thing) => Ok(PrimaryKey::Db(thing)),
                        Err(_) => {
                            // If Thing parsing fails, treat as LocalId
                            Ok(PrimaryKey::LocalId(value.to_string()))
                        }
                    }
                } else if let Ok(id) = value.parse::<i64>() {
                    // Pure numeric string - treat as server ID
                    Ok(PrimaryKey::ZirozenId(id))
                } else {
                    // Non-numeric string without colon - treat as local ID
                    Ok(PrimaryKey::LocalId(value.to_string()))
                }
            }

            fn visit_string<E>(self, value: String) -> Result<Self::Value, E>
            where
                E: de::Error,
            {
                // Check if it's a SurrealDB Thing string format (tableName:id)
                if value.contains(':') {
                    // Try to parse as SurrealDB Thing
                    match value.parse::<Thing>() {
                        Ok(thing) => Ok(PrimaryKey::Db(thing)),
                        Err(_) => {
                            // If Thing parsing fails, treat as LocalId
                            Ok(PrimaryKey::LocalId(value))
                        }
                    }
                } else if let Ok(id) = value.parse::<i64>() {
                    // Pure numeric string - treat as server ID
                    Ok(PrimaryKey::ZirozenId(id))
                } else {
                    // Non-numeric string without colon - treat as local ID
                    Ok(PrimaryKey::LocalId(value))
                }
            }

            // Handle SurrealDB Thing objects (maps/structs)
            fn visit_map<M>(self, map: M) -> Result<Self::Value, M::Error>
            where
                M: de::MapAccess<'de>,
            {
                use serde::de::value::MapAccessDeserializer;

                // Try to deserialize as Thing
                match Thing::deserialize(MapAccessDeserializer::new(map)) {
                    Ok(thing) => Ok(PrimaryKey::Db(thing)),
                    Err(e) => Err(de::Error::custom(format!(
                        "Failed to deserialize Thing: {}",
                        e
                    ))),
                }
            }
        }

        deserializer.deserialize_any(PrimaryKeyVisitor)
    }
}

// Alternative: Use a simple struct that can handle SurrealDB's serialization
#[derive(Debug, Clone, Serialize, Deserialize, Hash, PartialEq, Eq)]
#[serde(transparent)]
pub struct SurrealDbId(Thing);

impl From<SurrealDbId> for PrimaryKey {
    fn from(surreal_id: SurrealDbId) -> Self {
        PrimaryKey::Db(surreal_id.0)
    }
}

impl From<PrimaryKey> for SurrealDbId {
    fn from(pk: PrimaryKey) -> Self {
        match pk {
            PrimaryKey::Db(thing) => SurrealDbId(thing),
            PrimaryKey::ZirozenId(id) => {
                // Create a Thing from the numeric ID
                let thing_str = format!("unknown:{}", id);
                let thing: Thing = thing_str.parse().unwrap_or_else(|_| {
                    // Fallback if parsing fails
                    format!("fallback:{}", id).parse().unwrap()
                });
                SurrealDbId(thing)
            }
            PrimaryKey::LocalId(id) => {
                // Create a Thing from the string ID
                let thing_str = format!("local:{}", id);
                let thing: Thing = thing_str.parse().unwrap_or_else(|_| {
                    // Fallback if parsing fails
                    format!("fallback:{}", id).parse().unwrap()
                });
                SurrealDbId(thing)
            }
        }
    }
}

// Create a wrapper that can be used in models to avoid SurrealDB enum issues
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
#[serde(transparent)]
pub struct SurrealDbPrimaryKey(serde_json::Value);

impl From<SurrealDbPrimaryKey> for PrimaryKey {
    fn from(wrapper: SurrealDbPrimaryKey) -> Self {
        let value = wrapper.0;

        // Try to deserialize as Thing first
        if let Ok(thing) = serde_json::from_value::<Thing>(value.clone()) {
            return PrimaryKey::Db(thing);
        }

        // Try as i64
        if let Ok(id) = serde_json::from_value::<i64>(value.clone()) {
            return PrimaryKey::ZirozenId(id);
        }

        // Try as u64
        if let Ok(id) = serde_json::from_value::<u64>(value.clone()) {
            return PrimaryKey::ZirozenId(id as i64);
        }

        // Try as String
        if let Ok(s) = serde_json::from_value::<String>(value.clone()) {
            if let Ok(id) = s.parse::<i64>() {
                return PrimaryKey::ZirozenId(id);
            } else {
                return PrimaryKey::LocalId(s);
            }
        }

        // Fallback to default
        PrimaryKey::ZirozenId(0)
    }
}

impl From<PrimaryKey> for SurrealDbPrimaryKey {
    fn from(pk: PrimaryKey) -> Self {
        let value = match pk {
            PrimaryKey::ZirozenId(id) => serde_json::Value::Number(serde_json::Number::from(id)),
            PrimaryKey::LocalId(id) => serde_json::Value::String(id),
            PrimaryKey::Db(thing) => serde_json::to_value(thing).unwrap_or(serde_json::Value::Null),
        };
        SurrealDbPrimaryKey(value)
    }
}

impl PrimaryKey {
    pub fn is_persisted(&self) -> bool {
        match self {
            PrimaryKey::Db(_) => true,
            PrimaryKey::LocalId(_) => true,
            PrimaryKey::ZirozenId(_) => false,
        }
    }

    pub fn to_i64(&self) -> i64 {
        self.into()
    }
}

impl Default for PrimaryKey {
    fn default() -> Self {
        PrimaryKey::ZirozenId(0)
    }
}

impl Into<String> for PrimaryKey {
    fn into(self) -> String {
        match self {
            PrimaryKey::ZirozenId(id) => id.to_string(),
            PrimaryKey::LocalId(id) => id,
            PrimaryKey::Db(thing) => thing.id.to_string(),
        }
    }
}

impl Into<String> for &PrimaryKey {
    fn into(self) -> String {
        match self {
            PrimaryKey::ZirozenId(id) => id.to_string(),
            PrimaryKey::LocalId(id) => id.to_owned(),
            PrimaryKey::Db(thing) => thing.id.to_string(),
        }
    }
}

impl From<u32> for PrimaryKey {
    fn from(value: u32) -> Self {
        PrimaryKey::ZirozenId(value.into())
    }
}

impl From<u64> for PrimaryKey {
    fn from(value: u64) -> Self {
        PrimaryKey::ZirozenId(value as i64)
    }
}

impl From<&str> for PrimaryKey {
    fn from(value: &str) -> Self {
        PrimaryKey::LocalId(value.to_owned())
    }
}

impl From<String> for PrimaryKey {
    fn from(value: String) -> Self {
        PrimaryKey::LocalId(value)
    }
}

impl From<i64> for PrimaryKey {
    fn from(value: i64) -> Self {
        PrimaryKey::ZirozenId(value)
    }
}

impl From<&PrimaryKey> for i64 {
    fn from(value: &PrimaryKey) -> Self {
        match value {
            PrimaryKey::ZirozenId(id) => (*id).into(),
            PrimaryKey::LocalId(id) => {
                error!("Unable to convert local id {} to i64", id);
                0
            }
            PrimaryKey::Db(thing) => thing
                .id
                .to_raw()
                .parse()
                .expect(format!("Failed to convert {} to i64", thing.id).as_str()),
        }
    }
}

impl Display for PrimaryKey {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "{}",
            match self {
                PrimaryKey::ZirozenId(id) => id.to_string(),
                PrimaryKey::LocalId(id) => id.to_owned(),
                PrimaryKey::Db(thing) => thing.id.to_raw(),
            }
        )
    }
}

impl Into<i64> for PrimaryKey {
    fn into(self) -> i64 {
        match self {
            PrimaryKey::Db(thing) => thing
                .id
                .to_raw()
                .parse::<i64>()
                .expect("Failed to get u64 id"),
            PrimaryKey::ZirozenId(id) => id,
            PrimaryKey::LocalId(id) => {
                error!("Local id {} can not be converted to i64", id);
                0
            }
        }
    }
}
