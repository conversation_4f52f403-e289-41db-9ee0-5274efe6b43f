{"core": {"default_permission": {"identifier": "default", "description": "Default core plugins set.", "permissions": ["core:path:default", "core:event:default", "core:window:default", "core:webview:default", "core:app:default", "core:image:default", "core:resources:default", "core:menu:default", "core:tray:default"]}, "permissions": {}, "permission_sets": {}, "global_scope_schema": null}, "core:app": {"default_permission": {"identifier": "default", "description": "Default permissions for the plugin.", "permissions": ["allow-version", "allow-name", "allow-tauri-version", "allow-identifier", "allow-bundle-type"]}, "permissions": {"allow-app-hide": {"identifier": "allow-app-hide", "description": "Enables the app_hide command without any pre-configured scope.", "commands": {"allow": ["app_hide"], "deny": []}}, "allow-app-show": {"identifier": "allow-app-show", "description": "Enables the app_show command without any pre-configured scope.", "commands": {"allow": ["app_show"], "deny": []}}, "allow-bundle-type": {"identifier": "allow-bundle-type", "description": "Enables the bundle_type command without any pre-configured scope.", "commands": {"allow": ["bundle_type"], "deny": []}}, "allow-default-window-icon": {"identifier": "allow-default-window-icon", "description": "Enables the default_window_icon command without any pre-configured scope.", "commands": {"allow": ["default_window_icon"], "deny": []}}, "allow-fetch-data-store-identifiers": {"identifier": "allow-fetch-data-store-identifiers", "description": "Enables the fetch_data_store_identifiers command without any pre-configured scope.", "commands": {"allow": ["fetch_data_store_identifiers"], "deny": []}}, "allow-identifier": {"identifier": "allow-identifier", "description": "Enables the identifier command without any pre-configured scope.", "commands": {"allow": ["identifier"], "deny": []}}, "allow-name": {"identifier": "allow-name", "description": "Enables the name command without any pre-configured scope.", "commands": {"allow": ["name"], "deny": []}}, "allow-remove-data-store": {"identifier": "allow-remove-data-store", "description": "Enables the remove_data_store command without any pre-configured scope.", "commands": {"allow": ["remove_data_store"], "deny": []}}, "allow-set-app-theme": {"identifier": "allow-set-app-theme", "description": "Enables the set_app_theme command without any pre-configured scope.", "commands": {"allow": ["set_app_theme"], "deny": []}}, "allow-set-dock-visibility": {"identifier": "allow-set-dock-visibility", "description": "Enables the set_dock_visibility command without any pre-configured scope.", "commands": {"allow": ["set_dock_visibility"], "deny": []}}, "allow-tauri-version": {"identifier": "allow-tauri-version", "description": "Enables the tauri_version command without any pre-configured scope.", "commands": {"allow": ["tauri_version"], "deny": []}}, "allow-version": {"identifier": "allow-version", "description": "Enables the version command without any pre-configured scope.", "commands": {"allow": ["version"], "deny": []}}, "deny-app-hide": {"identifier": "deny-app-hide", "description": "Denies the app_hide command without any pre-configured scope.", "commands": {"allow": [], "deny": ["app_hide"]}}, "deny-app-show": {"identifier": "deny-app-show", "description": "Denies the app_show command without any pre-configured scope.", "commands": {"allow": [], "deny": ["app_show"]}}, "deny-bundle-type": {"identifier": "deny-bundle-type", "description": "Denies the bundle_type command without any pre-configured scope.", "commands": {"allow": [], "deny": ["bundle_type"]}}, "deny-default-window-icon": {"identifier": "deny-default-window-icon", "description": "Denies the default_window_icon command without any pre-configured scope.", "commands": {"allow": [], "deny": ["default_window_icon"]}}, "deny-fetch-data-store-identifiers": {"identifier": "deny-fetch-data-store-identifiers", "description": "Denies the fetch_data_store_identifiers command without any pre-configured scope.", "commands": {"allow": [], "deny": ["fetch_data_store_identifiers"]}}, "deny-identifier": {"identifier": "deny-identifier", "description": "Denies the identifier command without any pre-configured scope.", "commands": {"allow": [], "deny": ["identifier"]}}, "deny-name": {"identifier": "deny-name", "description": "Denies the name command without any pre-configured scope.", "commands": {"allow": [], "deny": ["name"]}}, "deny-remove-data-store": {"identifier": "deny-remove-data-store", "description": "Denies the remove_data_store command without any pre-configured scope.", "commands": {"allow": [], "deny": ["remove_data_store"]}}, "deny-set-app-theme": {"identifier": "deny-set-app-theme", "description": "Denies the set_app_theme command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_app_theme"]}}, "deny-set-dock-visibility": {"identifier": "deny-set-dock-visibility", "description": "Denies the set_dock_visibility command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_dock_visibility"]}}, "deny-tauri-version": {"identifier": "deny-tauri-version", "description": "Denies the tauri_version command without any pre-configured scope.", "commands": {"allow": [], "deny": ["tauri_version"]}}, "deny-version": {"identifier": "deny-version", "description": "Denies the version command without any pre-configured scope.", "commands": {"allow": [], "deny": ["version"]}}}, "permission_sets": {}, "global_scope_schema": null}, "core:event": {"default_permission": {"identifier": "default", "description": "Default permissions for the plugin, which enables all commands.", "permissions": ["allow-listen", "allow-unlisten", "allow-emit", "allow-emit-to"]}, "permissions": {"allow-emit": {"identifier": "allow-emit", "description": "Enables the emit command without any pre-configured scope.", "commands": {"allow": ["emit"], "deny": []}}, "allow-emit-to": {"identifier": "allow-emit-to", "description": "Enables the emit_to command without any pre-configured scope.", "commands": {"allow": ["emit_to"], "deny": []}}, "allow-listen": {"identifier": "allow-listen", "description": "Enables the listen command without any pre-configured scope.", "commands": {"allow": ["listen"], "deny": []}}, "allow-unlisten": {"identifier": "allow-unlisten", "description": "Enables the unlisten command without any pre-configured scope.", "commands": {"allow": ["unlisten"], "deny": []}}, "deny-emit": {"identifier": "deny-emit", "description": "Denies the emit command without any pre-configured scope.", "commands": {"allow": [], "deny": ["emit"]}}, "deny-emit-to": {"identifier": "deny-emit-to", "description": "Denies the emit_to command without any pre-configured scope.", "commands": {"allow": [], "deny": ["emit_to"]}}, "deny-listen": {"identifier": "deny-listen", "description": "Denies the listen command without any pre-configured scope.", "commands": {"allow": [], "deny": ["listen"]}}, "deny-unlisten": {"identifier": "deny-unlisten", "description": "Denies the unlisten command without any pre-configured scope.", "commands": {"allow": [], "deny": ["unlisten"]}}}, "permission_sets": {}, "global_scope_schema": null}, "core:image": {"default_permission": {"identifier": "default", "description": "Default permissions for the plugin, which enables all commands.", "permissions": ["allow-new", "allow-from-bytes", "allow-from-path", "allow-rgba", "allow-size"]}, "permissions": {"allow-from-bytes": {"identifier": "allow-from-bytes", "description": "Enables the from_bytes command without any pre-configured scope.", "commands": {"allow": ["from_bytes"], "deny": []}}, "allow-from-path": {"identifier": "allow-from-path", "description": "Enables the from_path command without any pre-configured scope.", "commands": {"allow": ["from_path"], "deny": []}}, "allow-new": {"identifier": "allow-new", "description": "Enables the new command without any pre-configured scope.", "commands": {"allow": ["new"], "deny": []}}, "allow-rgba": {"identifier": "allow-rgba", "description": "Enables the rgba command without any pre-configured scope.", "commands": {"allow": ["rgba"], "deny": []}}, "allow-size": {"identifier": "allow-size", "description": "Enables the size command without any pre-configured scope.", "commands": {"allow": ["size"], "deny": []}}, "deny-from-bytes": {"identifier": "deny-from-bytes", "description": "Denies the from_bytes command without any pre-configured scope.", "commands": {"allow": [], "deny": ["from_bytes"]}}, "deny-from-path": {"identifier": "deny-from-path", "description": "Denies the from_path command without any pre-configured scope.", "commands": {"allow": [], "deny": ["from_path"]}}, "deny-new": {"identifier": "deny-new", "description": "Denies the new command without any pre-configured scope.", "commands": {"allow": [], "deny": ["new"]}}, "deny-rgba": {"identifier": "deny-rgba", "description": "Denies the rgba command without any pre-configured scope.", "commands": {"allow": [], "deny": ["rgba"]}}, "deny-size": {"identifier": "deny-size", "description": "Denies the size command without any pre-configured scope.", "commands": {"allow": [], "deny": ["size"]}}}, "permission_sets": {}, "global_scope_schema": null}, "core:menu": {"default_permission": {"identifier": "default", "description": "Default permissions for the plugin, which enables all commands.", "permissions": ["allow-new", "allow-append", "allow-prepend", "allow-insert", "allow-remove", "allow-remove-at", "allow-items", "allow-get", "allow-popup", "allow-create-default", "allow-set-as-app-menu", "allow-set-as-window-menu", "allow-text", "allow-set-text", "allow-is-enabled", "allow-set-enabled", "allow-set-accelerator", "allow-set-as-windows-menu-for-nsapp", "allow-set-as-help-menu-for-nsapp", "allow-is-checked", "allow-set-checked", "allow-set-icon"]}, "permissions": {"allow-append": {"identifier": "allow-append", "description": "Enables the append command without any pre-configured scope.", "commands": {"allow": ["append"], "deny": []}}, "allow-create-default": {"identifier": "allow-create-default", "description": "Enables the create_default command without any pre-configured scope.", "commands": {"allow": ["create_default"], "deny": []}}, "allow-get": {"identifier": "allow-get", "description": "Enables the get command without any pre-configured scope.", "commands": {"allow": ["get"], "deny": []}}, "allow-insert": {"identifier": "allow-insert", "description": "Enables the insert command without any pre-configured scope.", "commands": {"allow": ["insert"], "deny": []}}, "allow-is-checked": {"identifier": "allow-is-checked", "description": "Enables the is_checked command without any pre-configured scope.", "commands": {"allow": ["is_checked"], "deny": []}}, "allow-is-enabled": {"identifier": "allow-is-enabled", "description": "Enables the is_enabled command without any pre-configured scope.", "commands": {"allow": ["is_enabled"], "deny": []}}, "allow-items": {"identifier": "allow-items", "description": "Enables the items command without any pre-configured scope.", "commands": {"allow": ["items"], "deny": []}}, "allow-new": {"identifier": "allow-new", "description": "Enables the new command without any pre-configured scope.", "commands": {"allow": ["new"], "deny": []}}, "allow-popup": {"identifier": "allow-popup", "description": "Enables the popup command without any pre-configured scope.", "commands": {"allow": ["popup"], "deny": []}}, "allow-prepend": {"identifier": "allow-prepend", "description": "Enables the prepend command without any pre-configured scope.", "commands": {"allow": ["prepend"], "deny": []}}, "allow-remove": {"identifier": "allow-remove", "description": "Enables the remove command without any pre-configured scope.", "commands": {"allow": ["remove"], "deny": []}}, "allow-remove-at": {"identifier": "allow-remove-at", "description": "Enables the remove_at command without any pre-configured scope.", "commands": {"allow": ["remove_at"], "deny": []}}, "allow-set-accelerator": {"identifier": "allow-set-accelerator", "description": "Enables the set_accelerator command without any pre-configured scope.", "commands": {"allow": ["set_accelerator"], "deny": []}}, "allow-set-as-app-menu": {"identifier": "allow-set-as-app-menu", "description": "Enables the set_as_app_menu command without any pre-configured scope.", "commands": {"allow": ["set_as_app_menu"], "deny": []}}, "allow-set-as-help-menu-for-nsapp": {"identifier": "allow-set-as-help-menu-for-nsapp", "description": "Enables the set_as_help_menu_for_nsapp command without any pre-configured scope.", "commands": {"allow": ["set_as_help_menu_for_nsapp"], "deny": []}}, "allow-set-as-window-menu": {"identifier": "allow-set-as-window-menu", "description": "Enables the set_as_window_menu command without any pre-configured scope.", "commands": {"allow": ["set_as_window_menu"], "deny": []}}, "allow-set-as-windows-menu-for-nsapp": {"identifier": "allow-set-as-windows-menu-for-nsapp", "description": "Enables the set_as_windows_menu_for_nsapp command without any pre-configured scope.", "commands": {"allow": ["set_as_windows_menu_for_nsapp"], "deny": []}}, "allow-set-checked": {"identifier": "allow-set-checked", "description": "Enables the set_checked command without any pre-configured scope.", "commands": {"allow": ["set_checked"], "deny": []}}, "allow-set-enabled": {"identifier": "allow-set-enabled", "description": "Enables the set_enabled command without any pre-configured scope.", "commands": {"allow": ["set_enabled"], "deny": []}}, "allow-set-icon": {"identifier": "allow-set-icon", "description": "Enables the set_icon command without any pre-configured scope.", "commands": {"allow": ["set_icon"], "deny": []}}, "allow-set-text": {"identifier": "allow-set-text", "description": "Enables the set_text command without any pre-configured scope.", "commands": {"allow": ["set_text"], "deny": []}}, "allow-text": {"identifier": "allow-text", "description": "Enables the text command without any pre-configured scope.", "commands": {"allow": ["text"], "deny": []}}, "deny-append": {"identifier": "deny-append", "description": "Denies the append command without any pre-configured scope.", "commands": {"allow": [], "deny": ["append"]}}, "deny-create-default": {"identifier": "deny-create-default", "description": "Denies the create_default command without any pre-configured scope.", "commands": {"allow": [], "deny": ["create_default"]}}, "deny-get": {"identifier": "deny-get", "description": "Denies the get command without any pre-configured scope.", "commands": {"allow": [], "deny": ["get"]}}, "deny-insert": {"identifier": "deny-insert", "description": "Denies the insert command without any pre-configured scope.", "commands": {"allow": [], "deny": ["insert"]}}, "deny-is-checked": {"identifier": "deny-is-checked", "description": "Denies the is_checked command without any pre-configured scope.", "commands": {"allow": [], "deny": ["is_checked"]}}, "deny-is-enabled": {"identifier": "deny-is-enabled", "description": "Denies the is_enabled command without any pre-configured scope.", "commands": {"allow": [], "deny": ["is_enabled"]}}, "deny-items": {"identifier": "deny-items", "description": "Denies the items command without any pre-configured scope.", "commands": {"allow": [], "deny": ["items"]}}, "deny-new": {"identifier": "deny-new", "description": "Denies the new command without any pre-configured scope.", "commands": {"allow": [], "deny": ["new"]}}, "deny-popup": {"identifier": "deny-popup", "description": "Denies the popup command without any pre-configured scope.", "commands": {"allow": [], "deny": ["popup"]}}, "deny-prepend": {"identifier": "deny-prepend", "description": "Denies the prepend command without any pre-configured scope.", "commands": {"allow": [], "deny": ["prepend"]}}, "deny-remove": {"identifier": "deny-remove", "description": "Denies the remove command without any pre-configured scope.", "commands": {"allow": [], "deny": ["remove"]}}, "deny-remove-at": {"identifier": "deny-remove-at", "description": "Denies the remove_at command without any pre-configured scope.", "commands": {"allow": [], "deny": ["remove_at"]}}, "deny-set-accelerator": {"identifier": "deny-set-accelerator", "description": "Denies the set_accelerator command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_accelerator"]}}, "deny-set-as-app-menu": {"identifier": "deny-set-as-app-menu", "description": "Denies the set_as_app_menu command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_as_app_menu"]}}, "deny-set-as-help-menu-for-nsapp": {"identifier": "deny-set-as-help-menu-for-nsapp", "description": "Denies the set_as_help_menu_for_nsapp command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_as_help_menu_for_nsapp"]}}, "deny-set-as-window-menu": {"identifier": "deny-set-as-window-menu", "description": "Denies the set_as_window_menu command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_as_window_menu"]}}, "deny-set-as-windows-menu-for-nsapp": {"identifier": "deny-set-as-windows-menu-for-nsapp", "description": "Denies the set_as_windows_menu_for_nsapp command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_as_windows_menu_for_nsapp"]}}, "deny-set-checked": {"identifier": "deny-set-checked", "description": "Denies the set_checked command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_checked"]}}, "deny-set-enabled": {"identifier": "deny-set-enabled", "description": "Denies the set_enabled command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_enabled"]}}, "deny-set-icon": {"identifier": "deny-set-icon", "description": "Denies the set_icon command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_icon"]}}, "deny-set-text": {"identifier": "deny-set-text", "description": "Denies the set_text command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_text"]}}, "deny-text": {"identifier": "deny-text", "description": "Denies the text command without any pre-configured scope.", "commands": {"allow": [], "deny": ["text"]}}}, "permission_sets": {}, "global_scope_schema": null}, "core:path": {"default_permission": {"identifier": "default", "description": "Default permissions for the plugin, which enables all commands.", "permissions": ["allow-resolve-directory", "allow-resolve", "allow-normalize", "allow-join", "allow-dirname", "allow-extname", "allow-basename", "allow-is-absolute"]}, "permissions": {"allow-basename": {"identifier": "allow-basename", "description": "Enables the basename command without any pre-configured scope.", "commands": {"allow": ["basename"], "deny": []}}, "allow-dirname": {"identifier": "allow-dirname", "description": "Enables the dirname command without any pre-configured scope.", "commands": {"allow": ["dirname"], "deny": []}}, "allow-extname": {"identifier": "allow-extname", "description": "Enables the extname command without any pre-configured scope.", "commands": {"allow": ["extname"], "deny": []}}, "allow-is-absolute": {"identifier": "allow-is-absolute", "description": "Enables the is_absolute command without any pre-configured scope.", "commands": {"allow": ["is_absolute"], "deny": []}}, "allow-join": {"identifier": "allow-join", "description": "Enables the join command without any pre-configured scope.", "commands": {"allow": ["join"], "deny": []}}, "allow-normalize": {"identifier": "allow-normalize", "description": "Enables the normalize command without any pre-configured scope.", "commands": {"allow": ["normalize"], "deny": []}}, "allow-resolve": {"identifier": "allow-resolve", "description": "Enables the resolve command without any pre-configured scope.", "commands": {"allow": ["resolve"], "deny": []}}, "allow-resolve-directory": {"identifier": "allow-resolve-directory", "description": "Enables the resolve_directory command without any pre-configured scope.", "commands": {"allow": ["resolve_directory"], "deny": []}}, "deny-basename": {"identifier": "deny-basename", "description": "Denies the basename command without any pre-configured scope.", "commands": {"allow": [], "deny": ["basename"]}}, "deny-dirname": {"identifier": "deny-dirname", "description": "Denies the dirname command without any pre-configured scope.", "commands": {"allow": [], "deny": ["dirname"]}}, "deny-extname": {"identifier": "deny-extname", "description": "Denies the extname command without any pre-configured scope.", "commands": {"allow": [], "deny": ["extname"]}}, "deny-is-absolute": {"identifier": "deny-is-absolute", "description": "Denies the is_absolute command without any pre-configured scope.", "commands": {"allow": [], "deny": ["is_absolute"]}}, "deny-join": {"identifier": "deny-join", "description": "Denies the join command without any pre-configured scope.", "commands": {"allow": [], "deny": ["join"]}}, "deny-normalize": {"identifier": "deny-normalize", "description": "Denies the normalize command without any pre-configured scope.", "commands": {"allow": [], "deny": ["normalize"]}}, "deny-resolve": {"identifier": "deny-resolve", "description": "Denies the resolve command without any pre-configured scope.", "commands": {"allow": [], "deny": ["resolve"]}}, "deny-resolve-directory": {"identifier": "deny-resolve-directory", "description": "Denies the resolve_directory command without any pre-configured scope.", "commands": {"allow": [], "deny": ["resolve_directory"]}}}, "permission_sets": {}, "global_scope_schema": null}, "core:resources": {"default_permission": {"identifier": "default", "description": "Default permissions for the plugin, which enables all commands.", "permissions": ["allow-close"]}, "permissions": {"allow-close": {"identifier": "allow-close", "description": "Enables the close command without any pre-configured scope.", "commands": {"allow": ["close"], "deny": []}}, "deny-close": {"identifier": "deny-close", "description": "Denies the close command without any pre-configured scope.", "commands": {"allow": [], "deny": ["close"]}}}, "permission_sets": {}, "global_scope_schema": null}, "core:tray": {"default_permission": {"identifier": "default", "description": "Default permissions for the plugin, which enables all commands.", "permissions": ["allow-new", "allow-get-by-id", "allow-remove-by-id", "allow-set-icon", "allow-set-menu", "allow-set-tooltip", "allow-set-title", "allow-set-visible", "allow-set-temp-dir-path", "allow-set-icon-as-template", "allow-set-show-menu-on-left-click"]}, "permissions": {"allow-get-by-id": {"identifier": "allow-get-by-id", "description": "Enables the get_by_id command without any pre-configured scope.", "commands": {"allow": ["get_by_id"], "deny": []}}, "allow-new": {"identifier": "allow-new", "description": "Enables the new command without any pre-configured scope.", "commands": {"allow": ["new"], "deny": []}}, "allow-remove-by-id": {"identifier": "allow-remove-by-id", "description": "Enables the remove_by_id command without any pre-configured scope.", "commands": {"allow": ["remove_by_id"], "deny": []}}, "allow-set-icon": {"identifier": "allow-set-icon", "description": "Enables the set_icon command without any pre-configured scope.", "commands": {"allow": ["set_icon"], "deny": []}}, "allow-set-icon-as-template": {"identifier": "allow-set-icon-as-template", "description": "Enables the set_icon_as_template command without any pre-configured scope.", "commands": {"allow": ["set_icon_as_template"], "deny": []}}, "allow-set-menu": {"identifier": "allow-set-menu", "description": "Enables the set_menu command without any pre-configured scope.", "commands": {"allow": ["set_menu"], "deny": []}}, "allow-set-show-menu-on-left-click": {"identifier": "allow-set-show-menu-on-left-click", "description": "Enables the set_show_menu_on_left_click command without any pre-configured scope.", "commands": {"allow": ["set_show_menu_on_left_click"], "deny": []}}, "allow-set-temp-dir-path": {"identifier": "allow-set-temp-dir-path", "description": "Enables the set_temp_dir_path command without any pre-configured scope.", "commands": {"allow": ["set_temp_dir_path"], "deny": []}}, "allow-set-title": {"identifier": "allow-set-title", "description": "Enables the set_title command without any pre-configured scope.", "commands": {"allow": ["set_title"], "deny": []}}, "allow-set-tooltip": {"identifier": "allow-set-tooltip", "description": "Enables the set_tooltip command without any pre-configured scope.", "commands": {"allow": ["set_tooltip"], "deny": []}}, "allow-set-visible": {"identifier": "allow-set-visible", "description": "Enables the set_visible command without any pre-configured scope.", "commands": {"allow": ["set_visible"], "deny": []}}, "deny-get-by-id": {"identifier": "deny-get-by-id", "description": "Denies the get_by_id command without any pre-configured scope.", "commands": {"allow": [], "deny": ["get_by_id"]}}, "deny-new": {"identifier": "deny-new", "description": "Denies the new command without any pre-configured scope.", "commands": {"allow": [], "deny": ["new"]}}, "deny-remove-by-id": {"identifier": "deny-remove-by-id", "description": "Denies the remove_by_id command without any pre-configured scope.", "commands": {"allow": [], "deny": ["remove_by_id"]}}, "deny-set-icon": {"identifier": "deny-set-icon", "description": "Denies the set_icon command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_icon"]}}, "deny-set-icon-as-template": {"identifier": "deny-set-icon-as-template", "description": "Denies the set_icon_as_template command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_icon_as_template"]}}, "deny-set-menu": {"identifier": "deny-set-menu", "description": "Denies the set_menu command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_menu"]}}, "deny-set-show-menu-on-left-click": {"identifier": "deny-set-show-menu-on-left-click", "description": "Denies the set_show_menu_on_left_click command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_show_menu_on_left_click"]}}, "deny-set-temp-dir-path": {"identifier": "deny-set-temp-dir-path", "description": "Denies the set_temp_dir_path command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_temp_dir_path"]}}, "deny-set-title": {"identifier": "deny-set-title", "description": "Denies the set_title command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_title"]}}, "deny-set-tooltip": {"identifier": "deny-set-tooltip", "description": "Denies the set_tooltip command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_tooltip"]}}, "deny-set-visible": {"identifier": "deny-set-visible", "description": "Denies the set_visible command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_visible"]}}}, "permission_sets": {}, "global_scope_schema": null}, "core:webview": {"default_permission": {"identifier": "default", "description": "Default permissions for the plugin.", "permissions": ["allow-get-all-webviews", "allow-webview-position", "allow-webview-size", "allow-internal-toggle-devtools"]}, "permissions": {"allow-clear-all-browsing-data": {"identifier": "allow-clear-all-browsing-data", "description": "Enables the clear_all_browsing_data command without any pre-configured scope.", "commands": {"allow": ["clear_all_browsing_data"], "deny": []}}, "allow-create-webview": {"identifier": "allow-create-webview", "description": "Enables the create_webview command without any pre-configured scope.", "commands": {"allow": ["create_webview"], "deny": []}}, "allow-create-webview-window": {"identifier": "allow-create-webview-window", "description": "Enables the create_webview_window command without any pre-configured scope.", "commands": {"allow": ["create_webview_window"], "deny": []}}, "allow-get-all-webviews": {"identifier": "allow-get-all-webviews", "description": "Enables the get_all_webviews command without any pre-configured scope.", "commands": {"allow": ["get_all_webviews"], "deny": []}}, "allow-internal-toggle-devtools": {"identifier": "allow-internal-toggle-devtools", "description": "Enables the internal_toggle_devtools command without any pre-configured scope.", "commands": {"allow": ["internal_toggle_devtools"], "deny": []}}, "allow-print": {"identifier": "allow-print", "description": "Enables the print command without any pre-configured scope.", "commands": {"allow": ["print"], "deny": []}}, "allow-reparent": {"identifier": "allow-reparent", "description": "Enables the reparent command without any pre-configured scope.", "commands": {"allow": ["reparent"], "deny": []}}, "allow-set-webview-auto-resize": {"identifier": "allow-set-webview-auto-resize", "description": "Enables the set_webview_auto_resize command without any pre-configured scope.", "commands": {"allow": ["set_webview_auto_resize"], "deny": []}}, "allow-set-webview-background-color": {"identifier": "allow-set-webview-background-color", "description": "Enables the set_webview_background_color command without any pre-configured scope.", "commands": {"allow": ["set_webview_background_color"], "deny": []}}, "allow-set-webview-focus": {"identifier": "allow-set-webview-focus", "description": "Enables the set_webview_focus command without any pre-configured scope.", "commands": {"allow": ["set_webview_focus"], "deny": []}}, "allow-set-webview-position": {"identifier": "allow-set-webview-position", "description": "Enables the set_webview_position command without any pre-configured scope.", "commands": {"allow": ["set_webview_position"], "deny": []}}, "allow-set-webview-size": {"identifier": "allow-set-webview-size", "description": "Enables the set_webview_size command without any pre-configured scope.", "commands": {"allow": ["set_webview_size"], "deny": []}}, "allow-set-webview-zoom": {"identifier": "allow-set-webview-zoom", "description": "Enables the set_webview_zoom command without any pre-configured scope.", "commands": {"allow": ["set_webview_zoom"], "deny": []}}, "allow-webview-close": {"identifier": "allow-webview-close", "description": "Enables the webview_close command without any pre-configured scope.", "commands": {"allow": ["webview_close"], "deny": []}}, "allow-webview-hide": {"identifier": "allow-webview-hide", "description": "Enables the webview_hide command without any pre-configured scope.", "commands": {"allow": ["webview_hide"], "deny": []}}, "allow-webview-position": {"identifier": "allow-webview-position", "description": "Enables the webview_position command without any pre-configured scope.", "commands": {"allow": ["webview_position"], "deny": []}}, "allow-webview-show": {"identifier": "allow-webview-show", "description": "Enables the webview_show command without any pre-configured scope.", "commands": {"allow": ["webview_show"], "deny": []}}, "allow-webview-size": {"identifier": "allow-webview-size", "description": "Enables the webview_size command without any pre-configured scope.", "commands": {"allow": ["webview_size"], "deny": []}}, "deny-clear-all-browsing-data": {"identifier": "deny-clear-all-browsing-data", "description": "Denies the clear_all_browsing_data command without any pre-configured scope.", "commands": {"allow": [], "deny": ["clear_all_browsing_data"]}}, "deny-create-webview": {"identifier": "deny-create-webview", "description": "Denies the create_webview command without any pre-configured scope.", "commands": {"allow": [], "deny": ["create_webview"]}}, "deny-create-webview-window": {"identifier": "deny-create-webview-window", "description": "Denies the create_webview_window command without any pre-configured scope.", "commands": {"allow": [], "deny": ["create_webview_window"]}}, "deny-get-all-webviews": {"identifier": "deny-get-all-webviews", "description": "Denies the get_all_webviews command without any pre-configured scope.", "commands": {"allow": [], "deny": ["get_all_webviews"]}}, "deny-internal-toggle-devtools": {"identifier": "deny-internal-toggle-devtools", "description": "Denies the internal_toggle_devtools command without any pre-configured scope.", "commands": {"allow": [], "deny": ["internal_toggle_devtools"]}}, "deny-print": {"identifier": "deny-print", "description": "Denies the print command without any pre-configured scope.", "commands": {"allow": [], "deny": ["print"]}}, "deny-reparent": {"identifier": "deny-reparent", "description": "Denies the reparent command without any pre-configured scope.", "commands": {"allow": [], "deny": ["reparent"]}}, "deny-set-webview-auto-resize": {"identifier": "deny-set-webview-auto-resize", "description": "Denies the set_webview_auto_resize command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_webview_auto_resize"]}}, "deny-set-webview-background-color": {"identifier": "deny-set-webview-background-color", "description": "Denies the set_webview_background_color command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_webview_background_color"]}}, "deny-set-webview-focus": {"identifier": "deny-set-webview-focus", "description": "Denies the set_webview_focus command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_webview_focus"]}}, "deny-set-webview-position": {"identifier": "deny-set-webview-position", "description": "Denies the set_webview_position command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_webview_position"]}}, "deny-set-webview-size": {"identifier": "deny-set-webview-size", "description": "Denies the set_webview_size command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_webview_size"]}}, "deny-set-webview-zoom": {"identifier": "deny-set-webview-zoom", "description": "Denies the set_webview_zoom command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_webview_zoom"]}}, "deny-webview-close": {"identifier": "deny-webview-close", "description": "Denies the webview_close command without any pre-configured scope.", "commands": {"allow": [], "deny": ["webview_close"]}}, "deny-webview-hide": {"identifier": "deny-webview-hide", "description": "Denies the webview_hide command without any pre-configured scope.", "commands": {"allow": [], "deny": ["webview_hide"]}}, "deny-webview-position": {"identifier": "deny-webview-position", "description": "Denies the webview_position command without any pre-configured scope.", "commands": {"allow": [], "deny": ["webview_position"]}}, "deny-webview-show": {"identifier": "deny-webview-show", "description": "Denies the webview_show command without any pre-configured scope.", "commands": {"allow": [], "deny": ["webview_show"]}}, "deny-webview-size": {"identifier": "deny-webview-size", "description": "Denies the webview_size command without any pre-configured scope.", "commands": {"allow": [], "deny": ["webview_size"]}}}, "permission_sets": {}, "global_scope_schema": null}, "core:window": {"default_permission": {"identifier": "default", "description": "Default permissions for the plugin.", "permissions": ["allow-get-all-windows", "allow-scale-factor", "allow-inner-position", "allow-outer-position", "allow-inner-size", "allow-outer-size", "allow-is-fullscreen", "allow-is-minimized", "allow-is-maximized", "allow-is-focused", "allow-is-decorated", "allow-is-resizable", "allow-is-maximizable", "allow-is-minimizable", "allow-is-closable", "allow-is-visible", "allow-is-enabled", "allow-title", "allow-current-monitor", "allow-primary-monitor", "allow-monitor-from-point", "allow-available-monitors", "allow-cursor-position", "allow-theme", "allow-is-always-on-top", "allow-internal-toggle-maximize"]}, "permissions": {"allow-available-monitors": {"identifier": "allow-available-monitors", "description": "Enables the available_monitors command without any pre-configured scope.", "commands": {"allow": ["available_monitors"], "deny": []}}, "allow-center": {"identifier": "allow-center", "description": "Enables the center command without any pre-configured scope.", "commands": {"allow": ["center"], "deny": []}}, "allow-close": {"identifier": "allow-close", "description": "Enables the close command without any pre-configured scope.", "commands": {"allow": ["close"], "deny": []}}, "allow-create": {"identifier": "allow-create", "description": "Enables the create command without any pre-configured scope.", "commands": {"allow": ["create"], "deny": []}}, "allow-current-monitor": {"identifier": "allow-current-monitor", "description": "Enables the current_monitor command without any pre-configured scope.", "commands": {"allow": ["current_monitor"], "deny": []}}, "allow-cursor-position": {"identifier": "allow-cursor-position", "description": "Enables the cursor_position command without any pre-configured scope.", "commands": {"allow": ["cursor_position"], "deny": []}}, "allow-destroy": {"identifier": "allow-destroy", "description": "Enables the destroy command without any pre-configured scope.", "commands": {"allow": ["destroy"], "deny": []}}, "allow-get-all-windows": {"identifier": "allow-get-all-windows", "description": "Enables the get_all_windows command without any pre-configured scope.", "commands": {"allow": ["get_all_windows"], "deny": []}}, "allow-hide": {"identifier": "allow-hide", "description": "Enables the hide command without any pre-configured scope.", "commands": {"allow": ["hide"], "deny": []}}, "allow-inner-position": {"identifier": "allow-inner-position", "description": "Enables the inner_position command without any pre-configured scope.", "commands": {"allow": ["inner_position"], "deny": []}}, "allow-inner-size": {"identifier": "allow-inner-size", "description": "Enables the inner_size command without any pre-configured scope.", "commands": {"allow": ["inner_size"], "deny": []}}, "allow-internal-toggle-maximize": {"identifier": "allow-internal-toggle-maximize", "description": "Enables the internal_toggle_maximize command without any pre-configured scope.", "commands": {"allow": ["internal_toggle_maximize"], "deny": []}}, "allow-is-always-on-top": {"identifier": "allow-is-always-on-top", "description": "Enables the is_always_on_top command without any pre-configured scope.", "commands": {"allow": ["is_always_on_top"], "deny": []}}, "allow-is-closable": {"identifier": "allow-is-closable", "description": "Enables the is_closable command without any pre-configured scope.", "commands": {"allow": ["is_closable"], "deny": []}}, "allow-is-decorated": {"identifier": "allow-is-decorated", "description": "Enables the is_decorated command without any pre-configured scope.", "commands": {"allow": ["is_decorated"], "deny": []}}, "allow-is-enabled": {"identifier": "allow-is-enabled", "description": "Enables the is_enabled command without any pre-configured scope.", "commands": {"allow": ["is_enabled"], "deny": []}}, "allow-is-focused": {"identifier": "allow-is-focused", "description": "Enables the is_focused command without any pre-configured scope.", "commands": {"allow": ["is_focused"], "deny": []}}, "allow-is-fullscreen": {"identifier": "allow-is-fullscreen", "description": "Enables the is_fullscreen command without any pre-configured scope.", "commands": {"allow": ["is_fullscreen"], "deny": []}}, "allow-is-maximizable": {"identifier": "allow-is-maximizable", "description": "Enables the is_maximizable command without any pre-configured scope.", "commands": {"allow": ["is_maximizable"], "deny": []}}, "allow-is-maximized": {"identifier": "allow-is-maximized", "description": "Enables the is_maximized command without any pre-configured scope.", "commands": {"allow": ["is_maximized"], "deny": []}}, "allow-is-minimizable": {"identifier": "allow-is-minimizable", "description": "Enables the is_minimizable command without any pre-configured scope.", "commands": {"allow": ["is_minimizable"], "deny": []}}, "allow-is-minimized": {"identifier": "allow-is-minimized", "description": "Enables the is_minimized command without any pre-configured scope.", "commands": {"allow": ["is_minimized"], "deny": []}}, "allow-is-resizable": {"identifier": "allow-is-resizable", "description": "Enables the is_resizable command without any pre-configured scope.", "commands": {"allow": ["is_resizable"], "deny": []}}, "allow-is-visible": {"identifier": "allow-is-visible", "description": "Enables the is_visible command without any pre-configured scope.", "commands": {"allow": ["is_visible"], "deny": []}}, "allow-maximize": {"identifier": "allow-maximize", "description": "Enables the maximize command without any pre-configured scope.", "commands": {"allow": ["maximize"], "deny": []}}, "allow-minimize": {"identifier": "allow-minimize", "description": "Enables the minimize command without any pre-configured scope.", "commands": {"allow": ["minimize"], "deny": []}}, "allow-monitor-from-point": {"identifier": "allow-monitor-from-point", "description": "Enables the monitor_from_point command without any pre-configured scope.", "commands": {"allow": ["monitor_from_point"], "deny": []}}, "allow-outer-position": {"identifier": "allow-outer-position", "description": "Enables the outer_position command without any pre-configured scope.", "commands": {"allow": ["outer_position"], "deny": []}}, "allow-outer-size": {"identifier": "allow-outer-size", "description": "Enables the outer_size command without any pre-configured scope.", "commands": {"allow": ["outer_size"], "deny": []}}, "allow-primary-monitor": {"identifier": "allow-primary-monitor", "description": "Enables the primary_monitor command without any pre-configured scope.", "commands": {"allow": ["primary_monitor"], "deny": []}}, "allow-request-user-attention": {"identifier": "allow-request-user-attention", "description": "Enables the request_user_attention command without any pre-configured scope.", "commands": {"allow": ["request_user_attention"], "deny": []}}, "allow-scale-factor": {"identifier": "allow-scale-factor", "description": "Enables the scale_factor command without any pre-configured scope.", "commands": {"allow": ["scale_factor"], "deny": []}}, "allow-set-always-on-bottom": {"identifier": "allow-set-always-on-bottom", "description": "Enables the set_always_on_bottom command without any pre-configured scope.", "commands": {"allow": ["set_always_on_bottom"], "deny": []}}, "allow-set-always-on-top": {"identifier": "allow-set-always-on-top", "description": "Enables the set_always_on_top command without any pre-configured scope.", "commands": {"allow": ["set_always_on_top"], "deny": []}}, "allow-set-background-color": {"identifier": "allow-set-background-color", "description": "Enables the set_background_color command without any pre-configured scope.", "commands": {"allow": ["set_background_color"], "deny": []}}, "allow-set-badge-count": {"identifier": "allow-set-badge-count", "description": "Enables the set_badge_count command without any pre-configured scope.", "commands": {"allow": ["set_badge_count"], "deny": []}}, "allow-set-badge-label": {"identifier": "allow-set-badge-label", "description": "Enables the set_badge_label command without any pre-configured scope.", "commands": {"allow": ["set_badge_label"], "deny": []}}, "allow-set-closable": {"identifier": "allow-set-closable", "description": "Enables the set_closable command without any pre-configured scope.", "commands": {"allow": ["set_closable"], "deny": []}}, "allow-set-content-protected": {"identifier": "allow-set-content-protected", "description": "Enables the set_content_protected command without any pre-configured scope.", "commands": {"allow": ["set_content_protected"], "deny": []}}, "allow-set-cursor-grab": {"identifier": "allow-set-cursor-grab", "description": "Enables the set_cursor_grab command without any pre-configured scope.", "commands": {"allow": ["set_cursor_grab"], "deny": []}}, "allow-set-cursor-icon": {"identifier": "allow-set-cursor-icon", "description": "Enables the set_cursor_icon command without any pre-configured scope.", "commands": {"allow": ["set_cursor_icon"], "deny": []}}, "allow-set-cursor-position": {"identifier": "allow-set-cursor-position", "description": "Enables the set_cursor_position command without any pre-configured scope.", "commands": {"allow": ["set_cursor_position"], "deny": []}}, "allow-set-cursor-visible": {"identifier": "allow-set-cursor-visible", "description": "Enables the set_cursor_visible command without any pre-configured scope.", "commands": {"allow": ["set_cursor_visible"], "deny": []}}, "allow-set-decorations": {"identifier": "allow-set-decorations", "description": "Enables the set_decorations command without any pre-configured scope.", "commands": {"allow": ["set_decorations"], "deny": []}}, "allow-set-effects": {"identifier": "allow-set-effects", "description": "Enables the set_effects command without any pre-configured scope.", "commands": {"allow": ["set_effects"], "deny": []}}, "allow-set-enabled": {"identifier": "allow-set-enabled", "description": "Enables the set_enabled command without any pre-configured scope.", "commands": {"allow": ["set_enabled"], "deny": []}}, "allow-set-focus": {"identifier": "allow-set-focus", "description": "Enables the set_focus command without any pre-configured scope.", "commands": {"allow": ["set_focus"], "deny": []}}, "allow-set-focusable": {"identifier": "allow-set-focusable", "description": "Enables the set_focusable command without any pre-configured scope.", "commands": {"allow": ["set_focusable"], "deny": []}}, "allow-set-fullscreen": {"identifier": "allow-set-fullscreen", "description": "Enables the set_fullscreen command without any pre-configured scope.", "commands": {"allow": ["set_fullscreen"], "deny": []}}, "allow-set-icon": {"identifier": "allow-set-icon", "description": "Enables the set_icon command without any pre-configured scope.", "commands": {"allow": ["set_icon"], "deny": []}}, "allow-set-ignore-cursor-events": {"identifier": "allow-set-ignore-cursor-events", "description": "Enables the set_ignore_cursor_events command without any pre-configured scope.", "commands": {"allow": ["set_ignore_cursor_events"], "deny": []}}, "allow-set-max-size": {"identifier": "allow-set-max-size", "description": "Enables the set_max_size command without any pre-configured scope.", "commands": {"allow": ["set_max_size"], "deny": []}}, "allow-set-maximizable": {"identifier": "allow-set-maximizable", "description": "Enables the set_maximizable command without any pre-configured scope.", "commands": {"allow": ["set_maximizable"], "deny": []}}, "allow-set-min-size": {"identifier": "allow-set-min-size", "description": "Enables the set_min_size command without any pre-configured scope.", "commands": {"allow": ["set_min_size"], "deny": []}}, "allow-set-minimizable": {"identifier": "allow-set-minimizable", "description": "Enables the set_minimizable command without any pre-configured scope.", "commands": {"allow": ["set_minimizable"], "deny": []}}, "allow-set-overlay-icon": {"identifier": "allow-set-overlay-icon", "description": "Enables the set_overlay_icon command without any pre-configured scope.", "commands": {"allow": ["set_overlay_icon"], "deny": []}}, "allow-set-position": {"identifier": "allow-set-position", "description": "Enables the set_position command without any pre-configured scope.", "commands": {"allow": ["set_position"], "deny": []}}, "allow-set-progress-bar": {"identifier": "allow-set-progress-bar", "description": "Enables the set_progress_bar command without any pre-configured scope.", "commands": {"allow": ["set_progress_bar"], "deny": []}}, "allow-set-resizable": {"identifier": "allow-set-resizable", "description": "Enables the set_resizable command without any pre-configured scope.", "commands": {"allow": ["set_resizable"], "deny": []}}, "allow-set-shadow": {"identifier": "allow-set-shadow", "description": "Enables the set_shadow command without any pre-configured scope.", "commands": {"allow": ["set_shadow"], "deny": []}}, "allow-set-simple-fullscreen": {"identifier": "allow-set-simple-fullscreen", "description": "Enables the set_simple_fullscreen command without any pre-configured scope.", "commands": {"allow": ["set_simple_fullscreen"], "deny": []}}, "allow-set-size": {"identifier": "allow-set-size", "description": "Enables the set_size command without any pre-configured scope.", "commands": {"allow": ["set_size"], "deny": []}}, "allow-set-size-constraints": {"identifier": "allow-set-size-constraints", "description": "Enables the set_size_constraints command without any pre-configured scope.", "commands": {"allow": ["set_size_constraints"], "deny": []}}, "allow-set-skip-taskbar": {"identifier": "allow-set-skip-taskbar", "description": "Enables the set_skip_taskbar command without any pre-configured scope.", "commands": {"allow": ["set_skip_taskbar"], "deny": []}}, "allow-set-theme": {"identifier": "allow-set-theme", "description": "Enables the set_theme command without any pre-configured scope.", "commands": {"allow": ["set_theme"], "deny": []}}, "allow-set-title": {"identifier": "allow-set-title", "description": "Enables the set_title command without any pre-configured scope.", "commands": {"allow": ["set_title"], "deny": []}}, "allow-set-title-bar-style": {"identifier": "allow-set-title-bar-style", "description": "Enables the set_title_bar_style command without any pre-configured scope.", "commands": {"allow": ["set_title_bar_style"], "deny": []}}, "allow-set-visible-on-all-workspaces": {"identifier": "allow-set-visible-on-all-workspaces", "description": "Enables the set_visible_on_all_workspaces command without any pre-configured scope.", "commands": {"allow": ["set_visible_on_all_workspaces"], "deny": []}}, "allow-show": {"identifier": "allow-show", "description": "Enables the show command without any pre-configured scope.", "commands": {"allow": ["show"], "deny": []}}, "allow-start-dragging": {"identifier": "allow-start-dragging", "description": "Enables the start_dragging command without any pre-configured scope.", "commands": {"allow": ["start_dragging"], "deny": []}}, "allow-start-resize-dragging": {"identifier": "allow-start-resize-dragging", "description": "Enables the start_resize_dragging command without any pre-configured scope.", "commands": {"allow": ["start_resize_dragging"], "deny": []}}, "allow-theme": {"identifier": "allow-theme", "description": "Enables the theme command without any pre-configured scope.", "commands": {"allow": ["theme"], "deny": []}}, "allow-title": {"identifier": "allow-title", "description": "Enables the title command without any pre-configured scope.", "commands": {"allow": ["title"], "deny": []}}, "allow-toggle-maximize": {"identifier": "allow-toggle-maximize", "description": "Enables the toggle_maximize command without any pre-configured scope.", "commands": {"allow": ["toggle_maximize"], "deny": []}}, "allow-unmaximize": {"identifier": "allow-unmaximize", "description": "Enables the unmaximize command without any pre-configured scope.", "commands": {"allow": ["unmaximize"], "deny": []}}, "allow-unminimize": {"identifier": "allow-unminimize", "description": "Enables the unminimize command without any pre-configured scope.", "commands": {"allow": ["unminimize"], "deny": []}}, "deny-available-monitors": {"identifier": "deny-available-monitors", "description": "Denies the available_monitors command without any pre-configured scope.", "commands": {"allow": [], "deny": ["available_monitors"]}}, "deny-center": {"identifier": "deny-center", "description": "Denies the center command without any pre-configured scope.", "commands": {"allow": [], "deny": ["center"]}}, "deny-close": {"identifier": "deny-close", "description": "Denies the close command without any pre-configured scope.", "commands": {"allow": [], "deny": ["close"]}}, "deny-create": {"identifier": "deny-create", "description": "Denies the create command without any pre-configured scope.", "commands": {"allow": [], "deny": ["create"]}}, "deny-current-monitor": {"identifier": "deny-current-monitor", "description": "Denies the current_monitor command without any pre-configured scope.", "commands": {"allow": [], "deny": ["current_monitor"]}}, "deny-cursor-position": {"identifier": "deny-cursor-position", "description": "Denies the cursor_position command without any pre-configured scope.", "commands": {"allow": [], "deny": ["cursor_position"]}}, "deny-destroy": {"identifier": "deny-destroy", "description": "Denies the destroy command without any pre-configured scope.", "commands": {"allow": [], "deny": ["destroy"]}}, "deny-get-all-windows": {"identifier": "deny-get-all-windows", "description": "Denies the get_all_windows command without any pre-configured scope.", "commands": {"allow": [], "deny": ["get_all_windows"]}}, "deny-hide": {"identifier": "deny-hide", "description": "Denies the hide command without any pre-configured scope.", "commands": {"allow": [], "deny": ["hide"]}}, "deny-inner-position": {"identifier": "deny-inner-position", "description": "Denies the inner_position command without any pre-configured scope.", "commands": {"allow": [], "deny": ["inner_position"]}}, "deny-inner-size": {"identifier": "deny-inner-size", "description": "Denies the inner_size command without any pre-configured scope.", "commands": {"allow": [], "deny": ["inner_size"]}}, "deny-internal-toggle-maximize": {"identifier": "deny-internal-toggle-maximize", "description": "Denies the internal_toggle_maximize command without any pre-configured scope.", "commands": {"allow": [], "deny": ["internal_toggle_maximize"]}}, "deny-is-always-on-top": {"identifier": "deny-is-always-on-top", "description": "Denies the is_always_on_top command without any pre-configured scope.", "commands": {"allow": [], "deny": ["is_always_on_top"]}}, "deny-is-closable": {"identifier": "deny-is-closable", "description": "Denies the is_closable command without any pre-configured scope.", "commands": {"allow": [], "deny": ["is_closable"]}}, "deny-is-decorated": {"identifier": "deny-is-decorated", "description": "Denies the is_decorated command without any pre-configured scope.", "commands": {"allow": [], "deny": ["is_decorated"]}}, "deny-is-enabled": {"identifier": "deny-is-enabled", "description": "Denies the is_enabled command without any pre-configured scope.", "commands": {"allow": [], "deny": ["is_enabled"]}}, "deny-is-focused": {"identifier": "deny-is-focused", "description": "Denies the is_focused command without any pre-configured scope.", "commands": {"allow": [], "deny": ["is_focused"]}}, "deny-is-fullscreen": {"identifier": "deny-is-fullscreen", "description": "Denies the is_fullscreen command without any pre-configured scope.", "commands": {"allow": [], "deny": ["is_fullscreen"]}}, "deny-is-maximizable": {"identifier": "deny-is-maximizable", "description": "Denies the is_maximizable command without any pre-configured scope.", "commands": {"allow": [], "deny": ["is_maximizable"]}}, "deny-is-maximized": {"identifier": "deny-is-maximized", "description": "Denies the is_maximized command without any pre-configured scope.", "commands": {"allow": [], "deny": ["is_maximized"]}}, "deny-is-minimizable": {"identifier": "deny-is-minimizable", "description": "Denies the is_minimizable command without any pre-configured scope.", "commands": {"allow": [], "deny": ["is_minimizable"]}}, "deny-is-minimized": {"identifier": "deny-is-minimized", "description": "Denies the is_minimized command without any pre-configured scope.", "commands": {"allow": [], "deny": ["is_minimized"]}}, "deny-is-resizable": {"identifier": "deny-is-resizable", "description": "Denies the is_resizable command without any pre-configured scope.", "commands": {"allow": [], "deny": ["is_resizable"]}}, "deny-is-visible": {"identifier": "deny-is-visible", "description": "Denies the is_visible command without any pre-configured scope.", "commands": {"allow": [], "deny": ["is_visible"]}}, "deny-maximize": {"identifier": "deny-maximize", "description": "Denies the maximize command without any pre-configured scope.", "commands": {"allow": [], "deny": ["maximize"]}}, "deny-minimize": {"identifier": "deny-minimize", "description": "Denies the minimize command without any pre-configured scope.", "commands": {"allow": [], "deny": ["minimize"]}}, "deny-monitor-from-point": {"identifier": "deny-monitor-from-point", "description": "Denies the monitor_from_point command without any pre-configured scope.", "commands": {"allow": [], "deny": ["monitor_from_point"]}}, "deny-outer-position": {"identifier": "deny-outer-position", "description": "Denies the outer_position command without any pre-configured scope.", "commands": {"allow": [], "deny": ["outer_position"]}}, "deny-outer-size": {"identifier": "deny-outer-size", "description": "Denies the outer_size command without any pre-configured scope.", "commands": {"allow": [], "deny": ["outer_size"]}}, "deny-primary-monitor": {"identifier": "deny-primary-monitor", "description": "Denies the primary_monitor command without any pre-configured scope.", "commands": {"allow": [], "deny": ["primary_monitor"]}}, "deny-request-user-attention": {"identifier": "deny-request-user-attention", "description": "Denies the request_user_attention command without any pre-configured scope.", "commands": {"allow": [], "deny": ["request_user_attention"]}}, "deny-scale-factor": {"identifier": "deny-scale-factor", "description": "Denies the scale_factor command without any pre-configured scope.", "commands": {"allow": [], "deny": ["scale_factor"]}}, "deny-set-always-on-bottom": {"identifier": "deny-set-always-on-bottom", "description": "Denies the set_always_on_bottom command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_always_on_bottom"]}}, "deny-set-always-on-top": {"identifier": "deny-set-always-on-top", "description": "Denies the set_always_on_top command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_always_on_top"]}}, "deny-set-background-color": {"identifier": "deny-set-background-color", "description": "Denies the set_background_color command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_background_color"]}}, "deny-set-badge-count": {"identifier": "deny-set-badge-count", "description": "Denies the set_badge_count command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_badge_count"]}}, "deny-set-badge-label": {"identifier": "deny-set-badge-label", "description": "Denies the set_badge_label command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_badge_label"]}}, "deny-set-closable": {"identifier": "deny-set-closable", "description": "Denies the set_closable command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_closable"]}}, "deny-set-content-protected": {"identifier": "deny-set-content-protected", "description": "Denies the set_content_protected command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_content_protected"]}}, "deny-set-cursor-grab": {"identifier": "deny-set-cursor-grab", "description": "Denies the set_cursor_grab command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_cursor_grab"]}}, "deny-set-cursor-icon": {"identifier": "deny-set-cursor-icon", "description": "Denies the set_cursor_icon command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_cursor_icon"]}}, "deny-set-cursor-position": {"identifier": "deny-set-cursor-position", "description": "Denies the set_cursor_position command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_cursor_position"]}}, "deny-set-cursor-visible": {"identifier": "deny-set-cursor-visible", "description": "Denies the set_cursor_visible command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_cursor_visible"]}}, "deny-set-decorations": {"identifier": "deny-set-decorations", "description": "Denies the set_decorations command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_decorations"]}}, "deny-set-effects": {"identifier": "deny-set-effects", "description": "Denies the set_effects command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_effects"]}}, "deny-set-enabled": {"identifier": "deny-set-enabled", "description": "Denies the set_enabled command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_enabled"]}}, "deny-set-focus": {"identifier": "deny-set-focus", "description": "Denies the set_focus command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_focus"]}}, "deny-set-focusable": {"identifier": "deny-set-focusable", "description": "Denies the set_focusable command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_focusable"]}}, "deny-set-fullscreen": {"identifier": "deny-set-fullscreen", "description": "Denies the set_fullscreen command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_fullscreen"]}}, "deny-set-icon": {"identifier": "deny-set-icon", "description": "Denies the set_icon command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_icon"]}}, "deny-set-ignore-cursor-events": {"identifier": "deny-set-ignore-cursor-events", "description": "Denies the set_ignore_cursor_events command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_ignore_cursor_events"]}}, "deny-set-max-size": {"identifier": "deny-set-max-size", "description": "Denies the set_max_size command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_max_size"]}}, "deny-set-maximizable": {"identifier": "deny-set-maximizable", "description": "Denies the set_maximizable command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_maximizable"]}}, "deny-set-min-size": {"identifier": "deny-set-min-size", "description": "Denies the set_min_size command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_min_size"]}}, "deny-set-minimizable": {"identifier": "deny-set-minimizable", "description": "Denies the set_minimizable command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_minimizable"]}}, "deny-set-overlay-icon": {"identifier": "deny-set-overlay-icon", "description": "Denies the set_overlay_icon command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_overlay_icon"]}}, "deny-set-position": {"identifier": "deny-set-position", "description": "Denies the set_position command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_position"]}}, "deny-set-progress-bar": {"identifier": "deny-set-progress-bar", "description": "Denies the set_progress_bar command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_progress_bar"]}}, "deny-set-resizable": {"identifier": "deny-set-resizable", "description": "Denies the set_resizable command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_resizable"]}}, "deny-set-shadow": {"identifier": "deny-set-shadow", "description": "Denies the set_shadow command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_shadow"]}}, "deny-set-simple-fullscreen": {"identifier": "deny-set-simple-fullscreen", "description": "Denies the set_simple_fullscreen command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_simple_fullscreen"]}}, "deny-set-size": {"identifier": "deny-set-size", "description": "Denies the set_size command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_size"]}}, "deny-set-size-constraints": {"identifier": "deny-set-size-constraints", "description": "Denies the set_size_constraints command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_size_constraints"]}}, "deny-set-skip-taskbar": {"identifier": "deny-set-skip-taskbar", "description": "Denies the set_skip_taskbar command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_skip_taskbar"]}}, "deny-set-theme": {"identifier": "deny-set-theme", "description": "Denies the set_theme command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_theme"]}}, "deny-set-title": {"identifier": "deny-set-title", "description": "Denies the set_title command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_title"]}}, "deny-set-title-bar-style": {"identifier": "deny-set-title-bar-style", "description": "Denies the set_title_bar_style command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_title_bar_style"]}}, "deny-set-visible-on-all-workspaces": {"identifier": "deny-set-visible-on-all-workspaces", "description": "Denies the set_visible_on_all_workspaces command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_visible_on_all_workspaces"]}}, "deny-show": {"identifier": "deny-show", "description": "Denies the show command without any pre-configured scope.", "commands": {"allow": [], "deny": ["show"]}}, "deny-start-dragging": {"identifier": "deny-start-dragging", "description": "Denies the start_dragging command without any pre-configured scope.", "commands": {"allow": [], "deny": ["start_dragging"]}}, "deny-start-resize-dragging": {"identifier": "deny-start-resize-dragging", "description": "Denies the start_resize_dragging command without any pre-configured scope.", "commands": {"allow": [], "deny": ["start_resize_dragging"]}}, "deny-theme": {"identifier": "deny-theme", "description": "Denies the theme command without any pre-configured scope.", "commands": {"allow": [], "deny": ["theme"]}}, "deny-title": {"identifier": "deny-title", "description": "Denies the title command without any pre-configured scope.", "commands": {"allow": [], "deny": ["title"]}}, "deny-toggle-maximize": {"identifier": "deny-toggle-maximize", "description": "Denies the toggle_maximize command without any pre-configured scope.", "commands": {"allow": [], "deny": ["toggle_maximize"]}}, "deny-unmaximize": {"identifier": "deny-unmaximize", "description": "Denies the unmaximize command without any pre-configured scope.", "commands": {"allow": [], "deny": ["unmaximize"]}}, "deny-unminimize": {"identifier": "deny-unminimize", "description": "Denies the unminimize command without any pre-configured scope.", "commands": {"allow": [], "deny": ["unminimize"]}}}, "permission_sets": {}, "global_scope_schema": null}, "dialog": {"default_permission": {"identifier": "default", "description": "This permission set configures the types of dialogs\navailable from the dialog plugin.\n\n#### Granted Permissions\n\nAll dialog types are enabled.\n\n\n", "permissions": ["allow-ask", "allow-confirm", "allow-message", "allow-save", "allow-open"]}, "permissions": {"allow-ask": {"identifier": "allow-ask", "description": "Enables the ask command without any pre-configured scope.", "commands": {"allow": ["ask"], "deny": []}}, "allow-confirm": {"identifier": "allow-confirm", "description": "Enables the confirm command without any pre-configured scope.", "commands": {"allow": ["confirm"], "deny": []}}, "allow-message": {"identifier": "allow-message", "description": "Enables the message command without any pre-configured scope.", "commands": {"allow": ["message"], "deny": []}}, "allow-open": {"identifier": "allow-open", "description": "Enables the open command without any pre-configured scope.", "commands": {"allow": ["open"], "deny": []}}, "allow-save": {"identifier": "allow-save", "description": "Enables the save command without any pre-configured scope.", "commands": {"allow": ["save"], "deny": []}}, "deny-ask": {"identifier": "deny-ask", "description": "Denies the ask command without any pre-configured scope.", "commands": {"allow": [], "deny": ["ask"]}}, "deny-confirm": {"identifier": "deny-confirm", "description": "Denies the confirm command without any pre-configured scope.", "commands": {"allow": [], "deny": ["confirm"]}}, "deny-message": {"identifier": "deny-message", "description": "Denies the message command without any pre-configured scope.", "commands": {"allow": [], "deny": ["message"]}}, "deny-open": {"identifier": "deny-open", "description": "Denies the open command without any pre-configured scope.", "commands": {"allow": [], "deny": ["open"]}}, "deny-save": {"identifier": "deny-save", "description": "Denies the save command without any pre-configured scope.", "commands": {"allow": [], "deny": ["save"]}}}, "permission_sets": {}, "global_scope_schema": null}, "fs": {"default_permission": {"identifier": "default", "description": "This set of permissions describes the what kind of\nfile system access the `fs` plugin has enabled or denied by default.\n\n#### Granted Permissions\n\nThis default permission set enables read access to the\napplication specific directories (AppConfig, AppData, AppLocalData, AppCache,\nAppLog) and all files and sub directories created in it.\nThe location of these directories depends on the operating system,\nwhere the application is run.\n\nIn general these directories need to be manually created\nby the application at runtime, before accessing files or folders\nin it is possible.\n\nTherefore, it is also allowed to create all of these folders via\nthe `mkdir` command.\n\n#### Denied Permissions\n\nThis default permission set prevents access to critical components\nof the Tauri application by default.\nOn Windows the webview data folder access is denied.\n", "permissions": ["create-app-specific-dirs", "read-app-specific-dirs-recursive", "deny-default"]}, "permissions": {"allow-copy-file": {"identifier": "allow-copy-file", "description": "Enables the copy_file command without any pre-configured scope.", "commands": {"allow": ["copy_file"], "deny": []}}, "allow-create": {"identifier": "allow-create", "description": "Enables the create command without any pre-configured scope.", "commands": {"allow": ["create"], "deny": []}}, "allow-exists": {"identifier": "allow-exists", "description": "Enables the exists command without any pre-configured scope.", "commands": {"allow": ["exists"], "deny": []}}, "allow-fstat": {"identifier": "allow-fstat", "description": "Enables the fstat command without any pre-configured scope.", "commands": {"allow": ["fstat"], "deny": []}}, "allow-ftruncate": {"identifier": "allow-ftruncate", "description": "Enables the ftruncate command without any pre-configured scope.", "commands": {"allow": ["ftrun<PERSON>"], "deny": []}}, "allow-lstat": {"identifier": "allow-lstat", "description": "Enables the lstat command without any pre-configured scope.", "commands": {"allow": ["lstat"], "deny": []}}, "allow-mkdir": {"identifier": "allow-mkdir", "description": "Enables the mkdir command without any pre-configured scope.", "commands": {"allow": ["mkdir"], "deny": []}}, "allow-open": {"identifier": "allow-open", "description": "Enables the open command without any pre-configured scope.", "commands": {"allow": ["open"], "deny": []}}, "allow-read": {"identifier": "allow-read", "description": "Enables the read command without any pre-configured scope.", "commands": {"allow": ["read"], "deny": []}}, "allow-read-dir": {"identifier": "allow-read-dir", "description": "Enables the read_dir command without any pre-configured scope.", "commands": {"allow": ["read_dir"], "deny": []}}, "allow-read-file": {"identifier": "allow-read-file", "description": "Enables the read_file command without any pre-configured scope.", "commands": {"allow": ["read_file"], "deny": []}}, "allow-read-text-file": {"identifier": "allow-read-text-file", "description": "Enables the read_text_file command without any pre-configured scope.", "commands": {"allow": ["read_text_file"], "deny": []}}, "allow-read-text-file-lines": {"identifier": "allow-read-text-file-lines", "description": "Enables the read_text_file_lines command without any pre-configured scope.", "commands": {"allow": ["read_text_file_lines", "read_text_file_lines_next"], "deny": []}}, "allow-read-text-file-lines-next": {"identifier": "allow-read-text-file-lines-next", "description": "Enables the read_text_file_lines_next command without any pre-configured scope.", "commands": {"allow": ["read_text_file_lines_next"], "deny": []}}, "allow-remove": {"identifier": "allow-remove", "description": "Enables the remove command without any pre-configured scope.", "commands": {"allow": ["remove"], "deny": []}}, "allow-rename": {"identifier": "allow-rename", "description": "Enables the rename command without any pre-configured scope.", "commands": {"allow": ["rename"], "deny": []}}, "allow-seek": {"identifier": "allow-seek", "description": "Enables the seek command without any pre-configured scope.", "commands": {"allow": ["seek"], "deny": []}}, "allow-size": {"identifier": "allow-size", "description": "Enables the size command without any pre-configured scope.", "commands": {"allow": ["size"], "deny": []}}, "allow-stat": {"identifier": "allow-stat", "description": "Enables the stat command without any pre-configured scope.", "commands": {"allow": ["stat"], "deny": []}}, "allow-truncate": {"identifier": "allow-truncate", "description": "Enables the truncate command without any pre-configured scope.", "commands": {"allow": ["truncate"], "deny": []}}, "allow-unwatch": {"identifier": "allow-unwatch", "description": "Enables the unwatch command without any pre-configured scope.", "commands": {"allow": ["unwatch"], "deny": []}}, "allow-watch": {"identifier": "allow-watch", "description": "Enables the watch command without any pre-configured scope.", "commands": {"allow": ["watch"], "deny": []}}, "allow-write": {"identifier": "allow-write", "description": "Enables the write command without any pre-configured scope.", "commands": {"allow": ["write"], "deny": []}}, "allow-write-file": {"identifier": "allow-write-file", "description": "Enables the write_file command without any pre-configured scope.", "commands": {"allow": ["write_file", "open", "write"], "deny": []}}, "allow-write-text-file": {"identifier": "allow-write-text-file", "description": "Enables the write_text_file command without any pre-configured scope.", "commands": {"allow": ["write_text_file"], "deny": []}}, "create-app-specific-dirs": {"identifier": "create-app-specific-dirs", "description": "This permissions allows to create the application specific directories.\n", "commands": {"allow": ["mkdir", "scope-app-index"], "deny": []}}, "deny-copy-file": {"identifier": "deny-copy-file", "description": "Denies the copy_file command without any pre-configured scope.", "commands": {"allow": [], "deny": ["copy_file"]}}, "deny-create": {"identifier": "deny-create", "description": "Denies the create command without any pre-configured scope.", "commands": {"allow": [], "deny": ["create"]}}, "deny-exists": {"identifier": "deny-exists", "description": "Denies the exists command without any pre-configured scope.", "commands": {"allow": [], "deny": ["exists"]}}, "deny-fstat": {"identifier": "deny-fstat", "description": "Denies the fstat command without any pre-configured scope.", "commands": {"allow": [], "deny": ["fstat"]}}, "deny-ftruncate": {"identifier": "deny-ftruncate", "description": "Denies the ftruncate command without any pre-configured scope.", "commands": {"allow": [], "deny": ["ftrun<PERSON>"]}}, "deny-lstat": {"identifier": "deny-lstat", "description": "Denies the lstat command without any pre-configured scope.", "commands": {"allow": [], "deny": ["lstat"]}}, "deny-mkdir": {"identifier": "deny-mkdir", "description": "Denies the mkdir command without any pre-configured scope.", "commands": {"allow": [], "deny": ["mkdir"]}}, "deny-open": {"identifier": "deny-open", "description": "Denies the open command without any pre-configured scope.", "commands": {"allow": [], "deny": ["open"]}}, "deny-read": {"identifier": "deny-read", "description": "Denies the read command without any pre-configured scope.", "commands": {"allow": [], "deny": ["read"]}}, "deny-read-dir": {"identifier": "deny-read-dir", "description": "Denies the read_dir command without any pre-configured scope.", "commands": {"allow": [], "deny": ["read_dir"]}}, "deny-read-file": {"identifier": "deny-read-file", "description": "Denies the read_file command without any pre-configured scope.", "commands": {"allow": [], "deny": ["read_file"]}}, "deny-read-text-file": {"identifier": "deny-read-text-file", "description": "Denies the read_text_file command without any pre-configured scope.", "commands": {"allow": [], "deny": ["read_text_file"]}}, "deny-read-text-file-lines": {"identifier": "deny-read-text-file-lines", "description": "Denies the read_text_file_lines command without any pre-configured scope.", "commands": {"allow": [], "deny": ["read_text_file_lines"]}}, "deny-read-text-file-lines-next": {"identifier": "deny-read-text-file-lines-next", "description": "Denies the read_text_file_lines_next command without any pre-configured scope.", "commands": {"allow": [], "deny": ["read_text_file_lines_next"]}}, "deny-remove": {"identifier": "deny-remove", "description": "Denies the remove command without any pre-configured scope.", "commands": {"allow": [], "deny": ["remove"]}}, "deny-rename": {"identifier": "deny-rename", "description": "Denies the rename command without any pre-configured scope.", "commands": {"allow": [], "deny": ["rename"]}}, "deny-seek": {"identifier": "deny-seek", "description": "Denies the seek command without any pre-configured scope.", "commands": {"allow": [], "deny": ["seek"]}}, "deny-size": {"identifier": "deny-size", "description": "Denies the size command without any pre-configured scope.", "commands": {"allow": [], "deny": ["size"]}}, "deny-stat": {"identifier": "deny-stat", "description": "Denies the stat command without any pre-configured scope.", "commands": {"allow": [], "deny": ["stat"]}}, "deny-truncate": {"identifier": "deny-truncate", "description": "Denies the truncate command without any pre-configured scope.", "commands": {"allow": [], "deny": ["truncate"]}}, "deny-unwatch": {"identifier": "deny-unwatch", "description": "Denies the unwatch command without any pre-configured scope.", "commands": {"allow": [], "deny": ["unwatch"]}}, "deny-watch": {"identifier": "deny-watch", "description": "Denies the watch command without any pre-configured scope.", "commands": {"allow": [], "deny": ["watch"]}}, "deny-webview-data-linux": {"identifier": "deny-webview-data-linux", "description": "This denies read access to the\n`$APPLOCALDATA` folder on linux as the webview data and configuration values are stored here.\nAllowing access can lead to sensitive information disclosure and should be well considered.", "commands": {"allow": [], "deny": []}}, "deny-webview-data-windows": {"identifier": "deny-webview-data-windows", "description": "This denies read access to the\n`$APPLOCALDATA/EBWebView` folder on windows as the webview data and configuration values are stored here.\nAllowing access can lead to sensitive information disclosure and should be well considered.", "commands": {"allow": [], "deny": []}}, "deny-write": {"identifier": "deny-write", "description": "Denies the write command without any pre-configured scope.", "commands": {"allow": [], "deny": ["write"]}}, "deny-write-file": {"identifier": "deny-write-file", "description": "Denies the write_file command without any pre-configured scope.", "commands": {"allow": [], "deny": ["write_file"]}}, "deny-write-text-file": {"identifier": "deny-write-text-file", "description": "Denies the write_text_file command without any pre-configured scope.", "commands": {"allow": [], "deny": ["write_text_file"]}}, "read-all": {"identifier": "read-all", "description": "This enables all read related commands without any pre-configured accessible paths.", "commands": {"allow": ["read_dir", "read_file", "read", "open", "read_text_file", "read_text_file_lines", "read_text_file_lines_next", "seek", "stat", "lstat", "fstat", "exists", "watch", "unwatch"], "deny": []}}, "read-app-specific-dirs-recursive": {"identifier": "read-app-specific-dirs-recursive", "description": "This permission allows recursive read functionality on the application\nspecific base directories. \n", "commands": {"allow": ["read_dir", "read_file", "read_text_file", "read_text_file_lines", "read_text_file_lines_next", "exists", "scope-app-recursive"], "deny": []}}, "read-dirs": {"identifier": "read-dirs", "description": "This enables directory read and file metadata related commands without any pre-configured accessible paths.", "commands": {"allow": ["read_dir", "stat", "lstat", "fstat", "exists"], "deny": []}}, "read-files": {"identifier": "read-files", "description": "This enables file read related commands without any pre-configured accessible paths.", "commands": {"allow": ["read_file", "read", "open", "read_text_file", "read_text_file_lines", "read_text_file_lines_next", "seek", "stat", "lstat", "fstat", "exists"], "deny": []}}, "read-meta": {"identifier": "read-meta", "description": "This enables all index or metadata related commands without any pre-configured accessible paths.", "commands": {"allow": ["read_dir", "stat", "lstat", "fstat", "exists", "size"], "deny": []}}, "scope": {"identifier": "scope", "description": "An empty permission you can use to modify the global scope.", "commands": {"allow": [], "deny": []}}, "scope-app": {"identifier": "scope-app", "description": "This scope permits access to all files and list content of top level directories in the application folders.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPCONFIG"}, {"path": "$APPCONFIG/*"}, {"path": "$APPDATA"}, {"path": "$APPDATA/*"}, {"path": "$APPLOCALDATA"}, {"path": "$APPLOCALDATA/*"}, {"path": "$APPCACHE"}, {"path": "$APPCACHE/*"}, {"path": "$APPLOG"}, {"path": "$APPLOG/*"}]}}, "scope-app-index": {"identifier": "scope-app-index", "description": "This scope permits to list all files and folders in the application directories.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPCONFIG"}, {"path": "$APPDATA"}, {"path": "$APPLOCALDATA"}, {"path": "$APPCACHE"}, {"path": "$APPLOG"}]}}, "scope-app-recursive": {"identifier": "scope-app-recursive", "description": "This scope permits recursive access to the complete application folders, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPCONFIG"}, {"path": "$APPCONFIG/**"}, {"path": "$APPDATA"}, {"path": "$APPDATA/**"}, {"path": "$APPLOCALDATA"}, {"path": "$APPLOCALDATA/**"}, {"path": "$APPCACHE"}, {"path": "$APPCACHE/**"}, {"path": "$APPLOG"}, {"path": "$APPLOG/**"}]}}, "scope-appcache": {"identifier": "scope-appcache", "description": "This scope permits access to all files and list content of top level directories in the `$APPCACHE` folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPCACHE"}, {"path": "$APPCACHE/*"}]}}, "scope-appcache-index": {"identifier": "scope-appcache-index", "description": "This scope permits to list all files and folders in the `$APPCACHE`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPCACHE"}]}}, "scope-appcache-recursive": {"identifier": "scope-appcache-recursive", "description": "This scope permits recursive access to the complete `$APPCACHE` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPCACHE"}, {"path": "$APPCACHE/**"}]}}, "scope-appconfig": {"identifier": "scope-appconfig", "description": "This scope permits access to all files and list content of top level directories in the `$APPCONFIG` folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPCONFIG"}, {"path": "$APPCONFIG/*"}]}}, "scope-appconfig-index": {"identifier": "scope-appconfig-index", "description": "This scope permits to list all files and folders in the `$APPCONFIG`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPCONFIG"}]}}, "scope-appconfig-recursive": {"identifier": "scope-appconfig-recursive", "description": "This scope permits recursive access to the complete `$APPCONFIG` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPCONFIG"}, {"path": "$APPCONFIG/**"}]}}, "scope-appdata": {"identifier": "scope-appdata", "description": "This scope permits access to all files and list content of top level directories in the `$APPDATA` folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPDATA"}, {"path": "$APPDATA/*"}]}}, "scope-appdata-index": {"identifier": "scope-appdata-index", "description": "This scope permits to list all files and folders in the `$APPDATA`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPDATA"}]}}, "scope-appdata-recursive": {"identifier": "scope-appdata-recursive", "description": "This scope permits recursive access to the complete `$APPDATA` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPDATA"}, {"path": "$APPDATA/**"}]}}, "scope-applocaldata": {"identifier": "scope-applocaldata", "description": "This scope permits access to all files and list content of top level directories in the `$APPLOCALDATA` folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPLOCALDATA"}, {"path": "$APPLOCALDATA/*"}]}}, "scope-applocaldata-index": {"identifier": "scope-applocaldata-index", "description": "This scope permits to list all files and folders in the `$APPLOCALDATA`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPLOCALDATA"}]}}, "scope-applocaldata-recursive": {"identifier": "scope-applocaldata-recursive", "description": "This scope permits recursive access to the complete `$APPLOCALDATA` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPLOCALDATA"}, {"path": "$APPLOCALDATA/**"}]}}, "scope-applog": {"identifier": "scope-applog", "description": "This scope permits access to all files and list content of top level directories in the `$APPLOG` folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPLOG"}, {"path": "$APPLOG/*"}]}}, "scope-applog-index": {"identifier": "scope-applog-index", "description": "This scope permits to list all files and folders in the `$APPLOG`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPLOG"}]}}, "scope-applog-recursive": {"identifier": "scope-applog-recursive", "description": "This scope permits recursive access to the complete `$APPLOG` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPLOG"}, {"path": "$APPLOG/**"}]}}, "scope-audio": {"identifier": "scope-audio", "description": "This scope permits access to all files and list content of top level directories in the `$AUDIO` folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$AUDIO"}, {"path": "$AUDIO/*"}]}}, "scope-audio-index": {"identifier": "scope-audio-index", "description": "This scope permits to list all files and folders in the `$AUDIO`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$AUDIO"}]}}, "scope-audio-recursive": {"identifier": "scope-audio-recursive", "description": "This scope permits recursive access to the complete `$AUDIO` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$AUDIO"}, {"path": "$AUDIO/**"}]}}, "scope-cache": {"identifier": "scope-cache", "description": "This scope permits access to all files and list content of top level directories in the `$CACHE` folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$CACHE"}, {"path": "$CACHE/*"}]}}, "scope-cache-index": {"identifier": "scope-cache-index", "description": "This scope permits to list all files and folders in the `$CACHE`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$CACHE"}]}}, "scope-cache-recursive": {"identifier": "scope-cache-recursive", "description": "This scope permits recursive access to the complete `$CACHE` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$CACHE"}, {"path": "$CACHE/**"}]}}, "scope-config": {"identifier": "scope-config", "description": "This scope permits access to all files and list content of top level directories in the `$CONFIG` folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$CONFIG"}, {"path": "$CONFIG/*"}]}}, "scope-config-index": {"identifier": "scope-config-index", "description": "This scope permits to list all files and folders in the `$CONFIG`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$CONFIG"}]}}, "scope-config-recursive": {"identifier": "scope-config-recursive", "description": "This scope permits recursive access to the complete `$CONFIG` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$CONFIG"}, {"path": "$CONFIG/**"}]}}, "scope-data": {"identifier": "scope-data", "description": "This scope permits access to all files and list content of top level directories in the `$DATA` folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$DATA"}, {"path": "$DATA/*"}]}}, "scope-data-index": {"identifier": "scope-data-index", "description": "This scope permits to list all files and folders in the `$DATA`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$DATA"}]}}, "scope-data-recursive": {"identifier": "scope-data-recursive", "description": "This scope permits recursive access to the complete `$DATA` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$DATA"}, {"path": "$DATA/**"}]}}, "scope-desktop": {"identifier": "scope-desktop", "description": "This scope permits access to all files and list content of top level directories in the `$DESKTOP` folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$DESKTOP"}, {"path": "$DESKTOP/*"}]}}, "scope-desktop-index": {"identifier": "scope-desktop-index", "description": "This scope permits to list all files and folders in the `$DESKTOP`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$DESKTOP"}]}}, "scope-desktop-recursive": {"identifier": "scope-desktop-recursive", "description": "This scope permits recursive access to the complete `$DESKTOP` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$DESKTOP"}, {"path": "$DESKTOP/**"}]}}, "scope-document": {"identifier": "scope-document", "description": "This scope permits access to all files and list content of top level directories in the `$DOCUMENT` folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$DOCUMENT"}, {"path": "$DOCUMENT/*"}]}}, "scope-document-index": {"identifier": "scope-document-index", "description": "This scope permits to list all files and folders in the `$DOCUMENT`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$DOCUMENT"}]}}, "scope-document-recursive": {"identifier": "scope-document-recursive", "description": "This scope permits recursive access to the complete `$DOCUMENT` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$DOCUMENT"}, {"path": "$DOCUMENT/**"}]}}, "scope-download": {"identifier": "scope-download", "description": "This scope permits access to all files and list content of top level directories in the `$DOWNLOAD` folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$DOWNLOAD"}, {"path": "$DOWNLOAD/*"}]}}, "scope-download-index": {"identifier": "scope-download-index", "description": "This scope permits to list all files and folders in the `$DOWNLOAD`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$DOWNLOAD"}]}}, "scope-download-recursive": {"identifier": "scope-download-recursive", "description": "This scope permits recursive access to the complete `$DOWNLOAD` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$DOWNLOAD"}, {"path": "$DOWNLOAD/**"}]}}, "scope-exe": {"identifier": "scope-exe", "description": "This scope permits access to all files and list content of top level directories in the `$EXE` folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$EXE"}, {"path": "$EXE/*"}]}}, "scope-exe-index": {"identifier": "scope-exe-index", "description": "This scope permits to list all files and folders in the `$EXE`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$EXE"}]}}, "scope-exe-recursive": {"identifier": "scope-exe-recursive", "description": "This scope permits recursive access to the complete `$EXE` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$EXE"}, {"path": "$EXE/**"}]}}, "scope-font": {"identifier": "scope-font", "description": "This scope permits access to all files and list content of top level directories in the `$FONT` folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$FONT"}, {"path": "$FONT/*"}]}}, "scope-font-index": {"identifier": "scope-font-index", "description": "This scope permits to list all files and folders in the `$FONT`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$FONT"}]}}, "scope-font-recursive": {"identifier": "scope-font-recursive", "description": "This scope permits recursive access to the complete `$FONT` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$FONT"}, {"path": "$FONT/**"}]}}, "scope-home": {"identifier": "scope-home", "description": "This scope permits access to all files and list content of top level directories in the `$HOME` folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$HOME"}, {"path": "$HOME/*"}]}}, "scope-home-index": {"identifier": "scope-home-index", "description": "This scope permits to list all files and folders in the `$HOME`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$HOME"}]}}, "scope-home-recursive": {"identifier": "scope-home-recursive", "description": "This scope permits recursive access to the complete `$HOME` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$HOME"}, {"path": "$HOME/**"}]}}, "scope-localdata": {"identifier": "scope-localdata", "description": "This scope permits access to all files and list content of top level directories in the `$LOCALDATA` folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$LOCALDATA"}, {"path": "$LOCALDATA/*"}]}}, "scope-localdata-index": {"identifier": "scope-localdata-index", "description": "This scope permits to list all files and folders in the `$LOCALDATA`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$LOCALDATA"}]}}, "scope-localdata-recursive": {"identifier": "scope-localdata-recursive", "description": "This scope permits recursive access to the complete `$LOCALDATA` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$LOCALDATA"}, {"path": "$LOCALDATA/**"}]}}, "scope-log": {"identifier": "scope-log", "description": "This scope permits access to all files and list content of top level directories in the `$LOG` folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$LOG"}, {"path": "$LOG/*"}]}}, "scope-log-index": {"identifier": "scope-log-index", "description": "This scope permits to list all files and folders in the `$LOG`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$LOG"}]}}, "scope-log-recursive": {"identifier": "scope-log-recursive", "description": "This scope permits recursive access to the complete `$LOG` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$LOG"}, {"path": "$LOG/**"}]}}, "scope-picture": {"identifier": "scope-picture", "description": "This scope permits access to all files and list content of top level directories in the `$PICTURE` folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$PICTURE"}, {"path": "$PICTURE/*"}]}}, "scope-picture-index": {"identifier": "scope-picture-index", "description": "This scope permits to list all files and folders in the `$PICTURE`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$PICTURE"}]}}, "scope-picture-recursive": {"identifier": "scope-picture-recursive", "description": "This scope permits recursive access to the complete `$PICTURE` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$PICTURE"}, {"path": "$PICTURE/**"}]}}, "scope-public": {"identifier": "scope-public", "description": "This scope permits access to all files and list content of top level directories in the `$PUBLIC` folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$PUBLIC"}, {"path": "$PUBLIC/*"}]}}, "scope-public-index": {"identifier": "scope-public-index", "description": "This scope permits to list all files and folders in the `$PUBLIC`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$PUBLIC"}]}}, "scope-public-recursive": {"identifier": "scope-public-recursive", "description": "This scope permits recursive access to the complete `$PUBLIC` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$PUBLIC"}, {"path": "$PUBLIC/**"}]}}, "scope-resource": {"identifier": "scope-resource", "description": "This scope permits access to all files and list content of top level directories in the `$RESOURCE` folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$RESOURCE"}, {"path": "$RESOURCE/*"}]}}, "scope-resource-index": {"identifier": "scope-resource-index", "description": "This scope permits to list all files and folders in the `$RESOURCE`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$RESOURCE"}]}}, "scope-resource-recursive": {"identifier": "scope-resource-recursive", "description": "This scope permits recursive access to the complete `$RESOURCE` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$RESOURCE"}, {"path": "$RESOURCE/**"}]}}, "scope-runtime": {"identifier": "scope-runtime", "description": "This scope permits access to all files and list content of top level directories in the `$RUNTIME` folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$RUNTIME"}, {"path": "$RUNTIME/*"}]}}, "scope-runtime-index": {"identifier": "scope-runtime-index", "description": "This scope permits to list all files and folders in the `$RUNTIME`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$RUNTIME"}]}}, "scope-runtime-recursive": {"identifier": "scope-runtime-recursive", "description": "This scope permits recursive access to the complete `$RUNTIME` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$RUNTIME"}, {"path": "$RUNTIME/**"}]}}, "scope-temp": {"identifier": "scope-temp", "description": "This scope permits access to all files and list content of top level directories in the `$TEMP` folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$TEMP"}, {"path": "$TEMP/*"}]}}, "scope-temp-index": {"identifier": "scope-temp-index", "description": "This scope permits to list all files and folders in the `$TEMP`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$TEMP"}]}}, "scope-temp-recursive": {"identifier": "scope-temp-recursive", "description": "This scope permits recursive access to the complete `$TEMP` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$TEMP"}, {"path": "$TEMP/**"}]}}, "scope-template": {"identifier": "scope-template", "description": "This scope permits access to all files and list content of top level directories in the `$TEMPLATE` folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$TEMPLATE"}, {"path": "$TEMPLATE/*"}]}}, "scope-template-index": {"identifier": "scope-template-index", "description": "This scope permits to list all files and folders in the `$TEMPLATE`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$TEMPLATE"}]}}, "scope-template-recursive": {"identifier": "scope-template-recursive", "description": "This scope permits recursive access to the complete `$TEMPLATE` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$TEMPLATE"}, {"path": "$TEMPLATE/**"}]}}, "scope-video": {"identifier": "scope-video", "description": "This scope permits access to all files and list content of top level directories in the `$VIDEO` folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$VIDEO"}, {"path": "$VIDEO/*"}]}}, "scope-video-index": {"identifier": "scope-video-index", "description": "This scope permits to list all files and folders in the `$VIDEO`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$VIDEO"}]}}, "scope-video-recursive": {"identifier": "scope-video-recursive", "description": "This scope permits recursive access to the complete `$VIDEO` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$VIDEO"}, {"path": "$VIDEO/**"}]}}, "write-all": {"identifier": "write-all", "description": "This enables all write related commands without any pre-configured accessible paths.", "commands": {"allow": ["mkdir", "create", "copy_file", "remove", "rename", "truncate", "ftrun<PERSON>", "write", "write_file", "write_text_file"], "deny": []}}, "write-files": {"identifier": "write-files", "description": "This enables all file write related commands without any pre-configured accessible paths.", "commands": {"allow": ["create", "copy_file", "remove", "rename", "truncate", "ftrun<PERSON>", "write", "write_file", "write_text_file"], "deny": []}}}, "permission_sets": {"allow-app-meta": {"identifier": "allow-app-meta", "description": "This allows non-recursive read access to metadata of the application folders, including file listing and statistics.", "permissions": ["read-meta", "scope-app-index"]}, "allow-app-meta-recursive": {"identifier": "allow-app-meta-recursive", "description": "This allows full recursive read access to metadata of the application folders, including file listing and statistics.", "permissions": ["read-meta", "scope-app-recursive"]}, "allow-app-read": {"identifier": "allow-app-read", "description": "This allows non-recursive read access to the application folders.", "permissions": ["read-all", "scope-app"]}, "allow-app-read-recursive": {"identifier": "allow-app-read-recursive", "description": "This allows full recursive read access to the complete application folders, files and subdirectories.", "permissions": ["read-all", "scope-app-recursive"]}, "allow-app-write": {"identifier": "allow-app-write", "description": "This allows non-recursive write access to the application folders.", "permissions": ["write-all", "scope-app"]}, "allow-app-write-recursive": {"identifier": "allow-app-write-recursive", "description": "This allows full recursive write access to the complete application folders, files and subdirectories.", "permissions": ["write-all", "scope-app-recursive"]}, "allow-appcache-meta": {"identifier": "allow-appcache-meta", "description": "This allows non-recursive read access to metadata of the `$APPCACHE` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-appcache-index"]}, "allow-appcache-meta-recursive": {"identifier": "allow-appcache-meta-recursive", "description": "This allows full recursive read access to metadata of the `$APPCACHE` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-appcache-recursive"]}, "allow-appcache-read": {"identifier": "allow-appcache-read", "description": "This allows non-recursive read access to the `$APPCACHE` folder.", "permissions": ["read-all", "scope-appcache"]}, "allow-appcache-read-recursive": {"identifier": "allow-appcache-read-recursive", "description": "This allows full recursive read access to the complete `$APPCACHE` folder, files and subdirectories.", "permissions": ["read-all", "scope-appcache-recursive"]}, "allow-appcache-write": {"identifier": "allow-appcache-write", "description": "This allows non-recursive write access to the `$APPCACHE` folder.", "permissions": ["write-all", "scope-appcache"]}, "allow-appcache-write-recursive": {"identifier": "allow-appcache-write-recursive", "description": "This allows full recursive write access to the complete `$APPCACHE` folder, files and subdirectories.", "permissions": ["write-all", "scope-appcache-recursive"]}, "allow-appconfig-meta": {"identifier": "allow-appconfig-meta", "description": "This allows non-recursive read access to metadata of the `$APPCONFIG` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-appconfig-index"]}, "allow-appconfig-meta-recursive": {"identifier": "allow-appconfig-meta-recursive", "description": "This allows full recursive read access to metadata of the `$APPCONFIG` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-appconfig-recursive"]}, "allow-appconfig-read": {"identifier": "allow-appconfig-read", "description": "This allows non-recursive read access to the `$APPCONFIG` folder.", "permissions": ["read-all", "scope-appconfig"]}, "allow-appconfig-read-recursive": {"identifier": "allow-appconfig-read-recursive", "description": "This allows full recursive read access to the complete `$APPCONFIG` folder, files and subdirectories.", "permissions": ["read-all", "scope-appconfig-recursive"]}, "allow-appconfig-write": {"identifier": "allow-appconfig-write", "description": "This allows non-recursive write access to the `$APPCONFIG` folder.", "permissions": ["write-all", "scope-appconfig"]}, "allow-appconfig-write-recursive": {"identifier": "allow-appconfig-write-recursive", "description": "This allows full recursive write access to the complete `$APPCONFIG` folder, files and subdirectories.", "permissions": ["write-all", "scope-appconfig-recursive"]}, "allow-appdata-meta": {"identifier": "allow-appdata-meta", "description": "This allows non-recursive read access to metadata of the `$APPDATA` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-appdata-index"]}, "allow-appdata-meta-recursive": {"identifier": "allow-appdata-meta-recursive", "description": "This allows full recursive read access to metadata of the `$APPDATA` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-appdata-recursive"]}, "allow-appdata-read": {"identifier": "allow-appdata-read", "description": "This allows non-recursive read access to the `$APPDATA` folder.", "permissions": ["read-all", "scope-appdata"]}, "allow-appdata-read-recursive": {"identifier": "allow-appdata-read-recursive", "description": "This allows full recursive read access to the complete `$APPDATA` folder, files and subdirectories.", "permissions": ["read-all", "scope-appdata-recursive"]}, "allow-appdata-write": {"identifier": "allow-appdata-write", "description": "This allows non-recursive write access to the `$APPDATA` folder.", "permissions": ["write-all", "scope-appdata"]}, "allow-appdata-write-recursive": {"identifier": "allow-appdata-write-recursive", "description": "This allows full recursive write access to the complete `$APPDATA` folder, files and subdirectories.", "permissions": ["write-all", "scope-appdata-recursive"]}, "allow-applocaldata-meta": {"identifier": "allow-applocaldata-meta", "description": "This allows non-recursive read access to metadata of the `$APPLOCALDATA` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-applocaldata-index"]}, "allow-applocaldata-meta-recursive": {"identifier": "allow-applocaldata-meta-recursive", "description": "This allows full recursive read access to metadata of the `$APPLOCALDATA` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-applocaldata-recursive"]}, "allow-applocaldata-read": {"identifier": "allow-applocaldata-read", "description": "This allows non-recursive read access to the `$APPLOCALDATA` folder.", "permissions": ["read-all", "scope-applocaldata"]}, "allow-applocaldata-read-recursive": {"identifier": "allow-applocaldata-read-recursive", "description": "This allows full recursive read access to the complete `$APPLOCALDATA` folder, files and subdirectories.", "permissions": ["read-all", "scope-applocaldata-recursive"]}, "allow-applocaldata-write": {"identifier": "allow-applocaldata-write", "description": "This allows non-recursive write access to the `$APPLOCALDATA` folder.", "permissions": ["write-all", "scope-applocaldata"]}, "allow-applocaldata-write-recursive": {"identifier": "allow-applocaldata-write-recursive", "description": "This allows full recursive write access to the complete `$APPLOCALDATA` folder, files and subdirectories.", "permissions": ["write-all", "scope-applocaldata-recursive"]}, "allow-applog-meta": {"identifier": "allow-applog-meta", "description": "This allows non-recursive read access to metadata of the `$APPLOG` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-applog-index"]}, "allow-applog-meta-recursive": {"identifier": "allow-applog-meta-recursive", "description": "This allows full recursive read access to metadata of the `$APPLOG` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-applog-recursive"]}, "allow-applog-read": {"identifier": "allow-applog-read", "description": "This allows non-recursive read access to the `$APPLOG` folder.", "permissions": ["read-all", "scope-applog"]}, "allow-applog-read-recursive": {"identifier": "allow-applog-read-recursive", "description": "This allows full recursive read access to the complete `$APPLOG` folder, files and subdirectories.", "permissions": ["read-all", "scope-applog-recursive"]}, "allow-applog-write": {"identifier": "allow-applog-write", "description": "This allows non-recursive write access to the `$APPLOG` folder.", "permissions": ["write-all", "scope-applog"]}, "allow-applog-write-recursive": {"identifier": "allow-applog-write-recursive", "description": "This allows full recursive write access to the complete `$APPLOG` folder, files and subdirectories.", "permissions": ["write-all", "scope-applog-recursive"]}, "allow-audio-meta": {"identifier": "allow-audio-meta", "description": "This allows non-recursive read access to metadata of the `$AUDIO` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-audio-index"]}, "allow-audio-meta-recursive": {"identifier": "allow-audio-meta-recursive", "description": "This allows full recursive read access to metadata of the `$AUDIO` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-audio-recursive"]}, "allow-audio-read": {"identifier": "allow-audio-read", "description": "This allows non-recursive read access to the `$AUDIO` folder.", "permissions": ["read-all", "scope-audio"]}, "allow-audio-read-recursive": {"identifier": "allow-audio-read-recursive", "description": "This allows full recursive read access to the complete `$AUDIO` folder, files and subdirectories.", "permissions": ["read-all", "scope-audio-recursive"]}, "allow-audio-write": {"identifier": "allow-audio-write", "description": "This allows non-recursive write access to the `$AUDIO` folder.", "permissions": ["write-all", "scope-audio"]}, "allow-audio-write-recursive": {"identifier": "allow-audio-write-recursive", "description": "This allows full recursive write access to the complete `$AUDIO` folder, files and subdirectories.", "permissions": ["write-all", "scope-audio-recursive"]}, "allow-cache-meta": {"identifier": "allow-cache-meta", "description": "This allows non-recursive read access to metadata of the `$CACHE` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-cache-index"]}, "allow-cache-meta-recursive": {"identifier": "allow-cache-meta-recursive", "description": "This allows full recursive read access to metadata of the `$CACHE` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-cache-recursive"]}, "allow-cache-read": {"identifier": "allow-cache-read", "description": "This allows non-recursive read access to the `$CACHE` folder.", "permissions": ["read-all", "scope-cache"]}, "allow-cache-read-recursive": {"identifier": "allow-cache-read-recursive", "description": "This allows full recursive read access to the complete `$CACHE` folder, files and subdirectories.", "permissions": ["read-all", "scope-cache-recursive"]}, "allow-cache-write": {"identifier": "allow-cache-write", "description": "This allows non-recursive write access to the `$CACHE` folder.", "permissions": ["write-all", "scope-cache"]}, "allow-cache-write-recursive": {"identifier": "allow-cache-write-recursive", "description": "This allows full recursive write access to the complete `$CACHE` folder, files and subdirectories.", "permissions": ["write-all", "scope-cache-recursive"]}, "allow-config-meta": {"identifier": "allow-config-meta", "description": "This allows non-recursive read access to metadata of the `$CONFIG` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-config-index"]}, "allow-config-meta-recursive": {"identifier": "allow-config-meta-recursive", "description": "This allows full recursive read access to metadata of the `$CONFIG` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-config-recursive"]}, "allow-config-read": {"identifier": "allow-config-read", "description": "This allows non-recursive read access to the `$CONFIG` folder.", "permissions": ["read-all", "scope-config"]}, "allow-config-read-recursive": {"identifier": "allow-config-read-recursive", "description": "This allows full recursive read access to the complete `$CONFIG` folder, files and subdirectories.", "permissions": ["read-all", "scope-config-recursive"]}, "allow-config-write": {"identifier": "allow-config-write", "description": "This allows non-recursive write access to the `$CONFIG` folder.", "permissions": ["write-all", "scope-config"]}, "allow-config-write-recursive": {"identifier": "allow-config-write-recursive", "description": "This allows full recursive write access to the complete `$CONFIG` folder, files and subdirectories.", "permissions": ["write-all", "scope-config-recursive"]}, "allow-data-meta": {"identifier": "allow-data-meta", "description": "This allows non-recursive read access to metadata of the `$DATA` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-data-index"]}, "allow-data-meta-recursive": {"identifier": "allow-data-meta-recursive", "description": "This allows full recursive read access to metadata of the `$DATA` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-data-recursive"]}, "allow-data-read": {"identifier": "allow-data-read", "description": "This allows non-recursive read access to the `$DATA` folder.", "permissions": ["read-all", "scope-data"]}, "allow-data-read-recursive": {"identifier": "allow-data-read-recursive", "description": "This allows full recursive read access to the complete `$DATA` folder, files and subdirectories.", "permissions": ["read-all", "scope-data-recursive"]}, "allow-data-write": {"identifier": "allow-data-write", "description": "This allows non-recursive write access to the `$DATA` folder.", "permissions": ["write-all", "scope-data"]}, "allow-data-write-recursive": {"identifier": "allow-data-write-recursive", "description": "This allows full recursive write access to the complete `$DATA` folder, files and subdirectories.", "permissions": ["write-all", "scope-data-recursive"]}, "allow-desktop-meta": {"identifier": "allow-desktop-meta", "description": "This allows non-recursive read access to metadata of the `$DESKTOP` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-desktop-index"]}, "allow-desktop-meta-recursive": {"identifier": "allow-desktop-meta-recursive", "description": "This allows full recursive read access to metadata of the `$DESKTOP` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-desktop-recursive"]}, "allow-desktop-read": {"identifier": "allow-desktop-read", "description": "This allows non-recursive read access to the `$DESKTOP` folder.", "permissions": ["read-all", "scope-desktop"]}, "allow-desktop-read-recursive": {"identifier": "allow-desktop-read-recursive", "description": "This allows full recursive read access to the complete `$DESKTOP` folder, files and subdirectories.", "permissions": ["read-all", "scope-desktop-recursive"]}, "allow-desktop-write": {"identifier": "allow-desktop-write", "description": "This allows non-recursive write access to the `$DESKTOP` folder.", "permissions": ["write-all", "scope-desktop"]}, "allow-desktop-write-recursive": {"identifier": "allow-desktop-write-recursive", "description": "This allows full recursive write access to the complete `$DESKTOP` folder, files and subdirectories.", "permissions": ["write-all", "scope-desktop-recursive"]}, "allow-document-meta": {"identifier": "allow-document-meta", "description": "This allows non-recursive read access to metadata of the `$DOCUMENT` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-document-index"]}, "allow-document-meta-recursive": {"identifier": "allow-document-meta-recursive", "description": "This allows full recursive read access to metadata of the `$DOCUMENT` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-document-recursive"]}, "allow-document-read": {"identifier": "allow-document-read", "description": "This allows non-recursive read access to the `$DOCUMENT` folder.", "permissions": ["read-all", "scope-document"]}, "allow-document-read-recursive": {"identifier": "allow-document-read-recursive", "description": "This allows full recursive read access to the complete `$DOCUMENT` folder, files and subdirectories.", "permissions": ["read-all", "scope-document-recursive"]}, "allow-document-write": {"identifier": "allow-document-write", "description": "This allows non-recursive write access to the `$DOCUMENT` folder.", "permissions": ["write-all", "scope-document"]}, "allow-document-write-recursive": {"identifier": "allow-document-write-recursive", "description": "This allows full recursive write access to the complete `$DOCUMENT` folder, files and subdirectories.", "permissions": ["write-all", "scope-document-recursive"]}, "allow-download-meta": {"identifier": "allow-download-meta", "description": "This allows non-recursive read access to metadata of the `$DOWNLOAD` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-download-index"]}, "allow-download-meta-recursive": {"identifier": "allow-download-meta-recursive", "description": "This allows full recursive read access to metadata of the `$DOWNLOAD` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-download-recursive"]}, "allow-download-read": {"identifier": "allow-download-read", "description": "This allows non-recursive read access to the `$DOWNLOAD` folder.", "permissions": ["read-all", "scope-download"]}, "allow-download-read-recursive": {"identifier": "allow-download-read-recursive", "description": "This allows full recursive read access to the complete `$DOWNLOAD` folder, files and subdirectories.", "permissions": ["read-all", "scope-download-recursive"]}, "allow-download-write": {"identifier": "allow-download-write", "description": "This allows non-recursive write access to the `$DOWNLOAD` folder.", "permissions": ["write-all", "scope-download"]}, "allow-download-write-recursive": {"identifier": "allow-download-write-recursive", "description": "This allows full recursive write access to the complete `$DOWNLOAD` folder, files and subdirectories.", "permissions": ["write-all", "scope-download-recursive"]}, "allow-exe-meta": {"identifier": "allow-exe-meta", "description": "This allows non-recursive read access to metadata of the `$EXE` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-exe-index"]}, "allow-exe-meta-recursive": {"identifier": "allow-exe-meta-recursive", "description": "This allows full recursive read access to metadata of the `$EXE` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-exe-recursive"]}, "allow-exe-read": {"identifier": "allow-exe-read", "description": "This allows non-recursive read access to the `$EXE` folder.", "permissions": ["read-all", "scope-exe"]}, "allow-exe-read-recursive": {"identifier": "allow-exe-read-recursive", "description": "This allows full recursive read access to the complete `$EXE` folder, files and subdirectories.", "permissions": ["read-all", "scope-exe-recursive"]}, "allow-exe-write": {"identifier": "allow-exe-write", "description": "This allows non-recursive write access to the `$EXE` folder.", "permissions": ["write-all", "scope-exe"]}, "allow-exe-write-recursive": {"identifier": "allow-exe-write-recursive", "description": "This allows full recursive write access to the complete `$EXE` folder, files and subdirectories.", "permissions": ["write-all", "scope-exe-recursive"]}, "allow-font-meta": {"identifier": "allow-font-meta", "description": "This allows non-recursive read access to metadata of the `$FONT` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-font-index"]}, "allow-font-meta-recursive": {"identifier": "allow-font-meta-recursive", "description": "This allows full recursive read access to metadata of the `$FONT` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-font-recursive"]}, "allow-font-read": {"identifier": "allow-font-read", "description": "This allows non-recursive read access to the `$FONT` folder.", "permissions": ["read-all", "scope-font"]}, "allow-font-read-recursive": {"identifier": "allow-font-read-recursive", "description": "This allows full recursive read access to the complete `$FONT` folder, files and subdirectories.", "permissions": ["read-all", "scope-font-recursive"]}, "allow-font-write": {"identifier": "allow-font-write", "description": "This allows non-recursive write access to the `$FONT` folder.", "permissions": ["write-all", "scope-font"]}, "allow-font-write-recursive": {"identifier": "allow-font-write-recursive", "description": "This allows full recursive write access to the complete `$FONT` folder, files and subdirectories.", "permissions": ["write-all", "scope-font-recursive"]}, "allow-home-meta": {"identifier": "allow-home-meta", "description": "This allows non-recursive read access to metadata of the `$HOME` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-home-index"]}, "allow-home-meta-recursive": {"identifier": "allow-home-meta-recursive", "description": "This allows full recursive read access to metadata of the `$HOME` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-home-recursive"]}, "allow-home-read": {"identifier": "allow-home-read", "description": "This allows non-recursive read access to the `$HOME` folder.", "permissions": ["read-all", "scope-home"]}, "allow-home-read-recursive": {"identifier": "allow-home-read-recursive", "description": "This allows full recursive read access to the complete `$HOME` folder, files and subdirectories.", "permissions": ["read-all", "scope-home-recursive"]}, "allow-home-write": {"identifier": "allow-home-write", "description": "This allows non-recursive write access to the `$HOME` folder.", "permissions": ["write-all", "scope-home"]}, "allow-home-write-recursive": {"identifier": "allow-home-write-recursive", "description": "This allows full recursive write access to the complete `$HOME` folder, files and subdirectories.", "permissions": ["write-all", "scope-home-recursive"]}, "allow-localdata-meta": {"identifier": "allow-localdata-meta", "description": "This allows non-recursive read access to metadata of the `$LOCALDATA` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-localdata-index"]}, "allow-localdata-meta-recursive": {"identifier": "allow-localdata-meta-recursive", "description": "This allows full recursive read access to metadata of the `$LOCALDATA` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-localdata-recursive"]}, "allow-localdata-read": {"identifier": "allow-localdata-read", "description": "This allows non-recursive read access to the `$LOCALDATA` folder.", "permissions": ["read-all", "scope-localdata"]}, "allow-localdata-read-recursive": {"identifier": "allow-localdata-read-recursive", "description": "This allows full recursive read access to the complete `$LOCALDATA` folder, files and subdirectories.", "permissions": ["read-all", "scope-localdata-recursive"]}, "allow-localdata-write": {"identifier": "allow-localdata-write", "description": "This allows non-recursive write access to the `$LOCALDATA` folder.", "permissions": ["write-all", "scope-localdata"]}, "allow-localdata-write-recursive": {"identifier": "allow-localdata-write-recursive", "description": "This allows full recursive write access to the complete `$LOCALDATA` folder, files and subdirectories.", "permissions": ["write-all", "scope-localdata-recursive"]}, "allow-log-meta": {"identifier": "allow-log-meta", "description": "This allows non-recursive read access to metadata of the `$LOG` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-log-index"]}, "allow-log-meta-recursive": {"identifier": "allow-log-meta-recursive", "description": "This allows full recursive read access to metadata of the `$LOG` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-log-recursive"]}, "allow-log-read": {"identifier": "allow-log-read", "description": "This allows non-recursive read access to the `$LOG` folder.", "permissions": ["read-all", "scope-log"]}, "allow-log-read-recursive": {"identifier": "allow-log-read-recursive", "description": "This allows full recursive read access to the complete `$LOG` folder, files and subdirectories.", "permissions": ["read-all", "scope-log-recursive"]}, "allow-log-write": {"identifier": "allow-log-write", "description": "This allows non-recursive write access to the `$LOG` folder.", "permissions": ["write-all", "scope-log"]}, "allow-log-write-recursive": {"identifier": "allow-log-write-recursive", "description": "This allows full recursive write access to the complete `$LOG` folder, files and subdirectories.", "permissions": ["write-all", "scope-log-recursive"]}, "allow-picture-meta": {"identifier": "allow-picture-meta", "description": "This allows non-recursive read access to metadata of the `$PICTURE` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-picture-index"]}, "allow-picture-meta-recursive": {"identifier": "allow-picture-meta-recursive", "description": "This allows full recursive read access to metadata of the `$PICTURE` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-picture-recursive"]}, "allow-picture-read": {"identifier": "allow-picture-read", "description": "This allows non-recursive read access to the `$PICTURE` folder.", "permissions": ["read-all", "scope-picture"]}, "allow-picture-read-recursive": {"identifier": "allow-picture-read-recursive", "description": "This allows full recursive read access to the complete `$PICTURE` folder, files and subdirectories.", "permissions": ["read-all", "scope-picture-recursive"]}, "allow-picture-write": {"identifier": "allow-picture-write", "description": "This allows non-recursive write access to the `$PICTURE` folder.", "permissions": ["write-all", "scope-picture"]}, "allow-picture-write-recursive": {"identifier": "allow-picture-write-recursive", "description": "This allows full recursive write access to the complete `$PICTURE` folder, files and subdirectories.", "permissions": ["write-all", "scope-picture-recursive"]}, "allow-public-meta": {"identifier": "allow-public-meta", "description": "This allows non-recursive read access to metadata of the `$PUBLIC` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-public-index"]}, "allow-public-meta-recursive": {"identifier": "allow-public-meta-recursive", "description": "This allows full recursive read access to metadata of the `$PUBLIC` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-public-recursive"]}, "allow-public-read": {"identifier": "allow-public-read", "description": "This allows non-recursive read access to the `$PUBLIC` folder.", "permissions": ["read-all", "scope-public"]}, "allow-public-read-recursive": {"identifier": "allow-public-read-recursive", "description": "This allows full recursive read access to the complete `$PUBLIC` folder, files and subdirectories.", "permissions": ["read-all", "scope-public-recursive"]}, "allow-public-write": {"identifier": "allow-public-write", "description": "This allows non-recursive write access to the `$PUBLIC` folder.", "permissions": ["write-all", "scope-public"]}, "allow-public-write-recursive": {"identifier": "allow-public-write-recursive", "description": "This allows full recursive write access to the complete `$PUBLIC` folder, files and subdirectories.", "permissions": ["write-all", "scope-public-recursive"]}, "allow-resource-meta": {"identifier": "allow-resource-meta", "description": "This allows non-recursive read access to metadata of the `$RESOURCE` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-resource-index"]}, "allow-resource-meta-recursive": {"identifier": "allow-resource-meta-recursive", "description": "This allows full recursive read access to metadata of the `$RESOURCE` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-resource-recursive"]}, "allow-resource-read": {"identifier": "allow-resource-read", "description": "This allows non-recursive read access to the `$RESOURCE` folder.", "permissions": ["read-all", "scope-resource"]}, "allow-resource-read-recursive": {"identifier": "allow-resource-read-recursive", "description": "This allows full recursive read access to the complete `$RESOURCE` folder, files and subdirectories.", "permissions": ["read-all", "scope-resource-recursive"]}, "allow-resource-write": {"identifier": "allow-resource-write", "description": "This allows non-recursive write access to the `$RESOURCE` folder.", "permissions": ["write-all", "scope-resource"]}, "allow-resource-write-recursive": {"identifier": "allow-resource-write-recursive", "description": "This allows full recursive write access to the complete `$RESOURCE` folder, files and subdirectories.", "permissions": ["write-all", "scope-resource-recursive"]}, "allow-runtime-meta": {"identifier": "allow-runtime-meta", "description": "This allows non-recursive read access to metadata of the `$RUNTIME` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-runtime-index"]}, "allow-runtime-meta-recursive": {"identifier": "allow-runtime-meta-recursive", "description": "This allows full recursive read access to metadata of the `$RUNTIME` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-runtime-recursive"]}, "allow-runtime-read": {"identifier": "allow-runtime-read", "description": "This allows non-recursive read access to the `$RUNTIME` folder.", "permissions": ["read-all", "scope-runtime"]}, "allow-runtime-read-recursive": {"identifier": "allow-runtime-read-recursive", "description": "This allows full recursive read access to the complete `$RUNTIME` folder, files and subdirectories.", "permissions": ["read-all", "scope-runtime-recursive"]}, "allow-runtime-write": {"identifier": "allow-runtime-write", "description": "This allows non-recursive write access to the `$RUNTIME` folder.", "permissions": ["write-all", "scope-runtime"]}, "allow-runtime-write-recursive": {"identifier": "allow-runtime-write-recursive", "description": "This allows full recursive write access to the complete `$RUNTIME` folder, files and subdirectories.", "permissions": ["write-all", "scope-runtime-recursive"]}, "allow-temp-meta": {"identifier": "allow-temp-meta", "description": "This allows non-recursive read access to metadata of the `$TEMP` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-temp-index"]}, "allow-temp-meta-recursive": {"identifier": "allow-temp-meta-recursive", "description": "This allows full recursive read access to metadata of the `$TEMP` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-temp-recursive"]}, "allow-temp-read": {"identifier": "allow-temp-read", "description": "This allows non-recursive read access to the `$TEMP` folder.", "permissions": ["read-all", "scope-temp"]}, "allow-temp-read-recursive": {"identifier": "allow-temp-read-recursive", "description": "This allows full recursive read access to the complete `$TEMP` folder, files and subdirectories.", "permissions": ["read-all", "scope-temp-recursive"]}, "allow-temp-write": {"identifier": "allow-temp-write", "description": "This allows non-recursive write access to the `$TEMP` folder.", "permissions": ["write-all", "scope-temp"]}, "allow-temp-write-recursive": {"identifier": "allow-temp-write-recursive", "description": "This allows full recursive write access to the complete `$TEMP` folder, files and subdirectories.", "permissions": ["write-all", "scope-temp-recursive"]}, "allow-template-meta": {"identifier": "allow-template-meta", "description": "This allows non-recursive read access to metadata of the `$TEMPLATE` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-template-index"]}, "allow-template-meta-recursive": {"identifier": "allow-template-meta-recursive", "description": "This allows full recursive read access to metadata of the `$TEMPLATE` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-template-recursive"]}, "allow-template-read": {"identifier": "allow-template-read", "description": "This allows non-recursive read access to the `$TEMPLATE` folder.", "permissions": ["read-all", "scope-template"]}, "allow-template-read-recursive": {"identifier": "allow-template-read-recursive", "description": "This allows full recursive read access to the complete `$TEMPLATE` folder, files and subdirectories.", "permissions": ["read-all", "scope-template-recursive"]}, "allow-template-write": {"identifier": "allow-template-write", "description": "This allows non-recursive write access to the `$TEMPLATE` folder.", "permissions": ["write-all", "scope-template"]}, "allow-template-write-recursive": {"identifier": "allow-template-write-recursive", "description": "This allows full recursive write access to the complete `$TEMPLATE` folder, files and subdirectories.", "permissions": ["write-all", "scope-template-recursive"]}, "allow-video-meta": {"identifier": "allow-video-meta", "description": "This allows non-recursive read access to metadata of the `$VIDEO` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-video-index"]}, "allow-video-meta-recursive": {"identifier": "allow-video-meta-recursive", "description": "This allows full recursive read access to metadata of the `$VIDEO` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-video-recursive"]}, "allow-video-read": {"identifier": "allow-video-read", "description": "This allows non-recursive read access to the `$VIDEO` folder.", "permissions": ["read-all", "scope-video"]}, "allow-video-read-recursive": {"identifier": "allow-video-read-recursive", "description": "This allows full recursive read access to the complete `$VIDEO` folder, files and subdirectories.", "permissions": ["read-all", "scope-video-recursive"]}, "allow-video-write": {"identifier": "allow-video-write", "description": "This allows non-recursive write access to the `$VIDEO` folder.", "permissions": ["write-all", "scope-video"]}, "allow-video-write-recursive": {"identifier": "allow-video-write-recursive", "description": "This allows full recursive write access to the complete `$VIDEO` folder, files and subdirectories.", "permissions": ["write-all", "scope-video-recursive"]}, "deny-default": {"identifier": "deny-default", "description": "This denies access to dangerous Tauri relevant files and folders by default.", "permissions": ["deny-webview-data-linux", "deny-webview-data-windows"]}}, "global_scope_schema": {"$schema": "http://json-schema.org/draft-07/schema#", "anyOf": [{"description": "A path that can be accessed by the webview when using the fs APIs. FS scope path pattern.\n\nThe pattern can start with a variable that resolves to a system base directory. The variables are: `$AUDIO`, `$CACHE`, `$CONFIG`, `$DATA`, `$LOCALDATA`, `$DESKTOP`, `$DOCUMENT`, `$DOWNLOAD`, `$EXE`, `$FONT`, `$HOME`, `$PICTURE`, `$PUBLIC`, `$RUNTIME`, `$TEMPLATE`, `$VIDEO`, `$RESOURCE`, `$APP`, `$LOG`, `$TEMP`, `$APPCONFIG`, `$APPDATA`, `$APPLOCALDATA`, `$APPCACHE`, `$APPLOG`.", "type": "string"}, {"properties": {"path": {"description": "A path that can be accessed by the webview when using the fs APIs.\n\nThe pattern can start with a variable that resolves to a system base directory. The variables are: `$AUDIO`, `$CACHE`, `$CONFIG`, `$DATA`, `$LOCALDATA`, `$DESKTOP`, `$DOCUMENT`, `$DOWNLOAD`, `$EXE`, `$FONT`, `$HOME`, `$PICTURE`, `$PUBLIC`, `$RUNTIME`, `$TEMPLATE`, `$VIDEO`, `$RESOURCE`, `$APP`, `$LOG`, `$TEMP`, `$APPCONFIG`, `$APPDATA`, `$APPLOCALDATA`, `$APPCACHE`, `$APPLOG`.", "type": "string"}}, "required": ["path"], "type": "object"}], "description": "FS scope entry.", "title": "FsScopeEntry"}}, "opener": {"default_permission": {"identifier": "default", "description": "This permission set allows opening `mailto:`, `tel:`, `https://` and `http://` urls using their default application\nas well as reveal file in directories using default file explorer", "permissions": ["allow-open-url", "allow-reveal-item-in-dir", "allow-default-urls"]}, "permissions": {"allow-default-urls": {"identifier": "allow-default-urls", "description": "This enables opening `mailto:`, `tel:`, `https://` and `http://` urls using their default application.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"url": "mailto:*"}, {"url": "tel:*"}, {"url": "http://*"}, {"url": "https://*"}]}}, "allow-open-path": {"identifier": "allow-open-path", "description": "Enables the open_path command without any pre-configured scope.", "commands": {"allow": ["open_path"], "deny": []}}, "allow-open-url": {"identifier": "allow-open-url", "description": "Enables the open_url command without any pre-configured scope.", "commands": {"allow": ["open_url"], "deny": []}}, "allow-reveal-item-in-dir": {"identifier": "allow-reveal-item-in-dir", "description": "Enables the reveal_item_in_dir command without any pre-configured scope.", "commands": {"allow": ["reveal_item_in_dir"], "deny": []}}, "deny-open-path": {"identifier": "deny-open-path", "description": "Denies the open_path command without any pre-configured scope.", "commands": {"allow": [], "deny": ["open_path"]}}, "deny-open-url": {"identifier": "deny-open-url", "description": "Denies the open_url command without any pre-configured scope.", "commands": {"allow": [], "deny": ["open_url"]}}, "deny-reveal-item-in-dir": {"identifier": "deny-reveal-item-in-dir", "description": "Denies the reveal_item_in_dir command without any pre-configured scope.", "commands": {"allow": [], "deny": ["reveal_item_in_dir"]}}}, "permission_sets": {}, "global_scope_schema": {"$schema": "http://json-schema.org/draft-07/schema#", "anyOf": [{"properties": {"app": {"allOf": [{"$ref": "#/definitions/Application"}], "description": "An application to open this url with, for example: firefox."}, "url": {"description": "A URL that can be opened by the webview when using the Opener APIs.\n\nWildcards can be used following the UNIX glob pattern.\n\nExamples:\n\n- \"https://*\" : allows all HTTPS origin\n\n- \"https://*.github.com/tauri-apps/tauri\": allows any subdomain of \"github.com\" with the \"tauri-apps/api\" path\n\n- \"https://myapi.service.com/users/*\": allows access to any URLs that begins with \"https://myapi.service.com/users/\"", "type": "string"}}, "required": ["url"], "type": "object"}, {"properties": {"app": {"allOf": [{"$ref": "#/definitions/Application"}], "description": "An application to open this path with, for example: xdg-open."}, "path": {"description": "A path that can be opened by the webview when using the Opener APIs.\n\nThe pattern can start with a variable that resolves to a system base directory. The variables are: `$AUDIO`, `$CACHE`, `$CONFIG`, `$DATA`, `$LOCALDATA`, `$DESKTOP`, `$DOCUMENT`, `$DOWNLOAD`, `$EXE`, `$FONT`, `$HOME`, `$PICTURE`, `$PUBLIC`, `$RUNTIME`, `$TEMPLATE`, `$VIDEO`, `$RESOURCE`, `$APP`, `$LOG`, `$TEMP`, `$APPCONFIG`, `$APPDATA`, `$APPLOCALDATA`, `$APPCACHE`, `$APPLOG`.", "type": "string"}}, "required": ["path"], "type": "object"}], "definitions": {"Application": {"anyOf": [{"description": "Open in default application.", "type": "null"}, {"description": "If true, allow open with any application.", "type": "boolean"}, {"description": "Allow specific application to open with.", "type": "string"}], "description": "Opener scope application."}}, "description": "Opener scope entry.", "title": "OpenerScopeEntry"}}, "os": {"default_permission": {"identifier": "default", "description": "This permission set configures which\noperating system information are available\nto gather from the frontend.\n\n#### Granted Permissions\n\nAll information except the host name are available.\n\n", "permissions": ["allow-arch", "allow-exe-extension", "allow-family", "allow-locale", "allow-os-type", "allow-platform", "allow-version"]}, "permissions": {"allow-arch": {"identifier": "allow-arch", "description": "Enables the arch command without any pre-configured scope.", "commands": {"allow": ["arch"], "deny": []}}, "allow-exe-extension": {"identifier": "allow-exe-extension", "description": "Enables the exe_extension command without any pre-configured scope.", "commands": {"allow": ["exe_extension"], "deny": []}}, "allow-family": {"identifier": "allow-family", "description": "Enables the family command without any pre-configured scope.", "commands": {"allow": ["family"], "deny": []}}, "allow-hostname": {"identifier": "allow-hostname", "description": "Enables the hostname command without any pre-configured scope.", "commands": {"allow": ["hostname"], "deny": []}}, "allow-locale": {"identifier": "allow-locale", "description": "Enables the locale command without any pre-configured scope.", "commands": {"allow": ["locale"], "deny": []}}, "allow-os-type": {"identifier": "allow-os-type", "description": "Enables the os_type command without any pre-configured scope.", "commands": {"allow": ["os_type"], "deny": []}}, "allow-platform": {"identifier": "allow-platform", "description": "Enables the platform command without any pre-configured scope.", "commands": {"allow": ["platform"], "deny": []}}, "allow-version": {"identifier": "allow-version", "description": "Enables the version command without any pre-configured scope.", "commands": {"allow": ["version"], "deny": []}}, "deny-arch": {"identifier": "deny-arch", "description": "Denies the arch command without any pre-configured scope.", "commands": {"allow": [], "deny": ["arch"]}}, "deny-exe-extension": {"identifier": "deny-exe-extension", "description": "Denies the exe_extension command without any pre-configured scope.", "commands": {"allow": [], "deny": ["exe_extension"]}}, "deny-family": {"identifier": "deny-family", "description": "Denies the family command without any pre-configured scope.", "commands": {"allow": [], "deny": ["family"]}}, "deny-hostname": {"identifier": "deny-hostname", "description": "Denies the hostname command without any pre-configured scope.", "commands": {"allow": [], "deny": ["hostname"]}}, "deny-locale": {"identifier": "deny-locale", "description": "Denies the locale command without any pre-configured scope.", "commands": {"allow": [], "deny": ["locale"]}}, "deny-os-type": {"identifier": "deny-os-type", "description": "Denies the os_type command without any pre-configured scope.", "commands": {"allow": [], "deny": ["os_type"]}}, "deny-platform": {"identifier": "deny-platform", "description": "Denies the platform command without any pre-configured scope.", "commands": {"allow": [], "deny": ["platform"]}}, "deny-version": {"identifier": "deny-version", "description": "Denies the version command without any pre-configured scope.", "commands": {"allow": [], "deny": ["version"]}}}, "permission_sets": {}, "global_scope_schema": null}}