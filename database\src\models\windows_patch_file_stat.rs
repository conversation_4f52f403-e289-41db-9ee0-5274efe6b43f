use logger::{debug, error};
use serde::{Deserialize, Serialize};

use crate::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, PrimaryKey};

#[derive(Debug, <PERSON><PERSON>, Default, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct WindowsPatchFileStat {
    pub file_name: String,
    pub last_sync_time: u64,
}

impl WindowsPatchFileStat {
    pub async fn has_file_changed(&self) -> bool {
        let db_record = DBWindowsPatchFileStat {
            id: PrimaryKey::LocalId(self.file_name.clone()),
            ..Default::default()
        };
        match db_record.load().await {
            Ok(record) => {
                debug!("Found record in db for file stat {record:?}",);
                if record.last_sync_time != self.last_sync_time {
                    return true;
                } else {
                    return false;
                }
            }
            Err(error) => {
                error!(
                    ?error,
                    "Failed to load windows file stat for file {}", self.file_name
                );
                true
            }
        }
    }

    pub async fn save(&self) -> Result<(), anyhow::Error> {
        let db_record = DBWindowsPatchFileStat {
            id: PrimaryKey::LocalId(self.file_name.clone()),
            file_name: self.file_name.clone(),
            last_sync_time: self.last_sync_time,
        };
        match db_record.persist().await {
            Ok(_) => {
                debug!("Saved file stat for file {}", self.file_name);
                Ok(())
            }
            Err(error) => {
                error!(
                    ?error,
                    "Failed to save windows file stat for file {}", self.file_name
                );
                Err(error.into())
            }
        }
    }
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
struct DBWindowsPatchFileStat {
    id: PrimaryKey,
    file_name: String,
    last_sync_time: u64,
}

impl HasPrimaryKey for DBWindowsPatchFileStat {
    fn table_name(&self) -> &str {
        "windows_patch_file_stat"
    }

    fn get_primary_key(&self) -> &PrimaryKey {
        &self.id
    }
}

impl Model for DBWindowsPatchFileStat {}
