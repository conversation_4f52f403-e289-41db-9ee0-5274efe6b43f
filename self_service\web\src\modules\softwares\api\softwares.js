import { invoke } from '@tauri-apps/api/core';
import { platform } from '@tauri-apps/plugin-os';

function getPlatformValue() {
  let platformValue = platform();
  if (platformValue === 'macos') {
    return 'mac';
  }
  return platformValue;
}

export function transformPackageIcon(data) {
  return data.iconFile && data.iconFile.refName ? data.iconFile : undefined;
}

const transform = (item) => ({
  id: item.id,
  name: item.name,
  displayName: item.displayName,
  description: item.description,
  os: item.os,
  osArch: item.osArch,
  version: item.version,
  pkgType: item.pkgType,
  iconFile: transformPackageIcon(item),
  tags: item.tags,
  selfServiceSupported: item.selfServiceSupported
});

const sortKeyMap = {
  createdAt: 'createdTime'
};

const searchableColumns = [
  'name',
  'displayName',
  'description',
  'version',
  'installCommand',
  'uninstallCommand',
  'upgradeCommand'
];

export function getAllPackagesApi(offset, size, sortFilter, configs = {}) {
  const payload = {
    offset,
    size,
    ...(sortFilter && sortFilter.sort && sortFilter.sort.field
      ? {
          sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
            sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
          }`
        }
      : {}),
    qualification: [
      {
        column: 'selfServiceSupported',
        operator: '=',
        value: true,
        condition: 'and'
      },
      {
        operator: '=',
        column: 'os',
        value: getPlatformValue(),
        condition: 'and'
      },
      ...(sortFilter.searchTerm
        ? [
            {
              operator: 'field_list_contains',
              columns: searchableColumns,
              value: sortFilter.searchTerm
            }
          ]
        : []),

      ...(configs?.selectedTags?.length
        ? configs.selectedTags.map((tag) => ({
            operator: 'in',
            column: 'tags',
            value: tag,
            condition: 'or'
          }))
        : [])
    ]
  };
  return invoke('get_softwares', { payload }).then((response) => {
    return {
      totalCount: response.totalCount,
      result: (response.result || []).map(transform)
    };
  });
}

export function installPackageApi(item) {
  return invoke('trigger_software_installation', {
    software: item
  });
}
