import { lazy } from 'react';
import { Route, Outlet } from 'react-router-dom';
import SoftwareLayout from './layout/SoftwareLayout.jsx';
import NotFound from '../../components/NotFound.jsx';

const Softwares = lazy(() => import(/* webpackChunkName: "settings" */ './views/Softwares.jsx'));
/**
 *
 * you can pass any config during init and those config can become prop for any of the route component
 */

export default function getRoutes(config) {
  return (
    <Route path="/softwares" key="softwares" element={<Outlet />}>
      <Route element={<SoftwareLayout />}>
        <Route index path="" element={<Softwares />} />
        <Route path="*" element={<NotFound redirectTo="/" />} />
      </Route>
    </Route>
  );
}
