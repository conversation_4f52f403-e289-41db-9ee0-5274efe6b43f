#[macro_use]
extern crate cfg_if;

mod has_commands;
mod has_task;
mod log_task;
mod prelude;
mod sync_task;
mod task_executable;
mod tasks;

use async_speed_limit::Limiter;
use database::models::{AgentMetadata, Task, TaskType};
pub use prelude::*;
use std::{f64::INFINITY, sync::LazyLock};
pub use task_executable::*;
pub use tasks::Unzip;

use tasks::{
    ApplicationControlPolicyTask, ComplianceTask, ConfigurationTask, PackageTask, PatchScanTask,
    PatchTask, SystemActionTask, UpgradeTask,
};

pub use tasks::QuickCheckTask;

use crate::tasks::RedhatAgentNominationTask;

pub(crate) static OUTPUT_FILE_NAME: &str = "output.txt";
pub static BANDWIDTH_LIMITER: LazyLock<Limiter> = LazyLock::new(|| Limiter::new(INFINITY));

pub(crate) fn get_current_date_time() -> String {
    chrono::Local::now().format("%FT%T%z").to_string()
}

pub fn build_executable_task(
    task: Task,
    agent_metadata: AgentMetadata,
) -> Result<Box<dyn TaskExecutable>, TaskExecutionError> {
    let task_type = if task
        .task_type
        .as_ref()
        .is_some_and(|item| item == &TaskType::Deployment)
    {
        task.module_name.as_ref().unwrap()
    } else {
        task.task_type.as_ref().unwrap()
    };

    match task_type {
        TaskType::Package => Ok(Box::new(PackageTask::new(task))),
        TaskType::Patch => Ok(Box::new(PatchTask::new(task))),
        TaskType::Configuration => Ok(Box::new(ConfigurationTask::new(task))),
        TaskType::Compliance | TaskType::ComplianceTest => Ok(Box::new(ComplianceTask::new(task))),
        TaskType::PatchScan => Ok(Box::new(PatchScanTask::new(task, agent_metadata))),
        TaskType::QuickCheck => Ok(Box::new(QuickCheckTask::new(task))),
        TaskType::Upgrade => Ok(Box::new(UpgradeTask::new(task))),
        TaskType::SystemAction | TaskType::SystemActions => {
            if task
                .module_name
                .as_ref()
                .is_some_and(|m| m == &TaskType::Configuration)
            {
                Ok(Box::new(ConfigurationTask::new(task)))
            } else {
                Ok(Box::new(SystemActionTask::new(task, agent_metadata)))
            }
        }
        TaskType::ApplicationControlPolicy | TaskType::ApplicationControlPolicies => {
            Ok(Box::new(ApplicationControlPolicyTask::new(task)))
        }
        TaskType::RedhatAgentNomination => Ok(Box::new(RedhatAgentNominationTask::new(
            task,
            agent_metadata,
        ))),
        _ => Err(TaskExecutionError::TaskIsNotSupported(task)),
    }
}
