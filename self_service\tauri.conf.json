{"$schema": "https://schema.tauri.app/config/2", "productName": "EndpointOps", "identifier": "com.endpointops.self-service", "build": {"beforeDevCommand": {"script": "npm run dev", "cwd": "web/"}, "devUrl": "http://localhost:1420", "beforeBuildCommand": {"script": "npm run build", "cwd": "web/"}, "frontendDist": "dist/"}, "app": {"macOSPrivateApi": true, "windows": [{"title": "EndpointOps", "minWidth": 1366, "minHeight": 800, "skipTaskbar": true, "devtools": true, "visible": false}], "security": {"csp": "default-src 'self' ipc: http://ipc.localhost; img-src 'self' asset: http://asset.localhost data:; style-src 'self' 'unsafe-inline' data:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; font-src 'self' data:", "assetProtocol": {"scope": ["$APPCACHE/**"], "enable": true}}}, "bundle": {"active": false, "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}