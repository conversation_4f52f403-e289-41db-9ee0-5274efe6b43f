use crate::{
    cpp_data_types::{
        EventType as CPPEventType, FileCreateInfo, FileDeleteInfo, FileReadInfo, FileRenameINFO,
        FileWriteInfo, ProcessCreateInfo, ProcessExitInfo, RegistrySetValueInfo, COMMON_MONINFO,
    },
    proc_owner::<PERSON><PERSON><PERSON>wn<PERSON>,
    WinDriverHandlerError,
};
use std::{
    collections::HashSet,
    fmt::Display,
    path::Path,
    time::{Duration, UNIX_EPOCH},
};
use tracing::{error, trace};
use windows::core::PCWSTR;

struct ExcludedFileOperation;

impl ExcludedFileOperation {
    fn should_exclude(file_path: &str) -> bool {
        let path = Path::new(file_path);
        let file_name = path
            .file_name()
            .unwrap_or_default()
            .to_string_lossy()
            .to_string();
        let ext = path
            .extension()
            .unwrap_or_default()
            .to_str()
            .unwrap_or_default()
            .to_lowercase();
        let excluded_extensions = vec!["tmp"];
        excluded_extensions.contains(&ext.as_str())
            || file_name.to_lowercase().starts_with("~")
            || file_name.ends_with(":Zone.Identifier")
    }

    fn is_tmp_extension(file_name: &str) -> bool {
        let ext = Path::new(file_name)
            .extension()
            .unwrap_or_default()
            .to_str()
            .unwrap_or_default()
            .to_lowercase();
        let excluded_extensions = vec!["tmp"];
        excluded_extensions.contains(&ext.as_str())
    }
}

pub struct U16String<'a>(pub &'a [u16]);

impl<'a> From<U16String<'a>> for String {
    fn from(value: U16String) -> Self {
        match unsafe { PCWSTR::from_raw(value.0.as_ptr()).to_string() } {
            Ok(result) => result,
            Err(error) => {
                error!("Failed to encode utf-16 string {:?}", error);
                "".to_owned()
            }
        }
    }
}

fn large_integer_to_system_time(filetime: i64) -> i64 {
    // FILETIME is the number of 100-nanosecond intervals since 1601-01-01
    const WINDOWS_TO_UNIX_EPOCH_SECS: u64 = 11644473600;

    let total_nanos = (filetime as u64) * 100; // Convert to nanoseconds
    let secs = total_nanos / 1_000_000_000;
    let nanos = (total_nanos % 1_000_000_000) as u32;

    let unix = UNIX_EPOCH + Duration::new(secs - WINDOWS_TO_UNIX_EPOCH_SECS, nanos);
    match unix.duration_since(UNIX_EPOCH) {
        Ok(duration) => duration.as_secs() as i64,
        Err(err) => {
            // Time is before UNIX_EPOCH, return negative timestamp
            -(err.duration().as_secs() as i64)
        }
    }
}

#[derive(Debug, Default, PartialEq, Eq, Clone)]
pub enum EventType {
    #[default]
    None,
    ProcessCreate,
    ProcessExit,
    FileCreate,
    FileDelete,
    FileRead,
    FileWrite,
    FileRename,
    DirRename,
    DirDelete,
    DirCreate,
    RegKeyCreate,
    RegKeyRename,
    RegValueCreate,
    RegSetValue,
    RegKeyDelete,
    RegValueDelete,
    RegSaveKey,
    FileBlock,
    ProcessBlock,
}

impl Display for EventType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "{}",
            match self {
                EventType::None => "UNKNOWN",
                EventType::ProcessCreate => "Process Created",
                EventType::ProcessExit => "Process Exit",
                EventType::FileCreate => "File Created",
                EventType::FileDelete => "File Deleted",
                EventType::FileRead => "File Read",
                EventType::FileWrite => "File Write",
                EventType::FileRename => "File Rename",
                EventType::DirRename => "Dir Rename",
                EventType::DirDelete => "Dir Delete",
                EventType::DirCreate => "Dir Create",
                EventType::RegKeyCreate => "Reg Key Created",
                EventType::RegKeyRename => "Reg Key Rename",
                EventType::RegValueCreate => "Reg Value Created",
                EventType::RegSetValue => "Reg Set Value",
                EventType::RegKeyDelete => "Reg Key Deleted",
                EventType::RegValueDelete => "Reg Value Deleted",
                EventType::RegSaveKey => "Reg Save Key",
                EventType::FileBlock => "File Blocked",
                EventType::ProcessBlock => "Process Blocked",
            }
        )
    }
}

impl From<CPPEventType> for EventType {
    fn from(value: CPPEventType) -> Self {
        match value {
            CPPEventType::None => EventType::None,
            CPPEventType::ProcessCreate => EventType::ProcessCreate,
            CPPEventType::ProcessExit => EventType::ProcessExit,
            CPPEventType::FileCreate => EventType::FileCreate,
            CPPEventType::FileDelete => EventType::FileDelete,
            CPPEventType::FileRead => EventType::FileRead,
            CPPEventType::FileWrite => EventType::FileWrite,
            CPPEventType::FileRename => EventType::FileRename,
            CPPEventType::DirRename => EventType::DirRename,
            CPPEventType::DirDelete => EventType::DirDelete,
            CPPEventType::DirCreate => EventType::DirCreate,
            CPPEventType::RegKeyCreate => EventType::RegKeyCreate,
            CPPEventType::RegKeyRename => EventType::RegKeyRename,
            CPPEventType::RegValueCreate => EventType::RegValueCreate,
            CPPEventType::RegistrySetValue => EventType::RegSetValue,
            CPPEventType::RegKeyDelete => EventType::RegKeyDelete,
            CPPEventType::RegValueDelete => EventType::RegValueDelete,
            CPPEventType::RegiSaveKey => EventType::RegSaveKey,
            CPPEventType::FileAccessBlock => EventType::FileBlock,
            CPPEventType::ProcessBlock => EventType::ProcessBlock,
        }
    }
}

#[derive(Debug, Default, PartialEq, Eq, Clone)]
pub struct ProcessEvent {
    pub pid: u32,
    pub ppid: u32,
    pub session_id: i32,
    pub name: Option<String>,
    pub cmd: Option<String>,
    pub created_at: i64,
}

impl ProcessEvent {
    pub fn unique_key(&self) -> String {
        format!("{}-{}-{}", self.pid, self.ppid, self.session_id)
    }
}

impl From<ProcessCreateInfo> for ProcessEvent {
    fn from(value: ProcessCreateInfo) -> Self {
        ProcessEvent {
            pid: value.ProcessId,
            ppid: value.ParentProcessId,
            session_id: value.SessionId,
            name: Some(U16String(value.ProcessName.as_ref()).into()),
            cmd: Some(U16String(value.CommandLine.as_ref()).into()),
            created_at: large_integer_to_system_time(value.header.Time),
            ..Default::default()
        }
    }
}

impl From<ProcessExitInfo> for ProcessEvent {
    fn from(value: ProcessExitInfo) -> Self {
        ProcessEvent {
            pid: value.ProcessId,
            ppid: value.ParentProcessId,
            session_id: value.SessionId,
            created_at: large_integer_to_system_time(value.header.Time),
            ..Default::default()
        }
    }
}

#[derive(Debug, Default, Clone, PartialEq, Eq)]
pub struct RegistryEvent {
    pub pid: u32,
    pub key_name: String,
    pub updated_key_name: Option<String>,
    pub value_name: Option<String>,
    pub updated_value_name: Option<String>,
    pub data_type: u32,
    pub data: Option<String>,
    pub data_size: u32,
    pub created_at: i64,
    pub proc_info: ProcOwner,
}

impl RegistryEvent {
    pub fn unique_key(&self) -> String {
        format!("{}-{}", self.pid, self.key_name)
    }
}

impl From<RegistrySetValueInfo> for RegistryEvent {
    fn from(value: RegistrySetValueInfo) -> Self {
        RegistryEvent {
            pid: value.ProcessId,
            key_name: U16String(value.KeyName.as_ref()).into(),
            updated_key_name: if value.NewKeyName.len() > 0 {
                Some(U16String(value.NewKeyName.as_ref()).into())
            } else {
                None
            },
            value_name: if value.ValueName.len() > 0 {
                Some(U16String(value.ValueName.as_ref()).into())
            } else {
                None
            },
            updated_value_name: if value.NewValueName.len() > 0 {
                Some(U16String(value.NewValueName.as_ref()).into())
            } else {
                None
            },
            data: if value.Data.len() > 0 {
                Some(U16String(value.Data.as_ref()).into())
            } else {
                None
            },
            data_type: value.DataType,
            data_size: value.DataSize,
            created_at: large_integer_to_system_time(value.header.Time),
            ..Default::default()
        }
    }
}

#[derive(Debug, Default, Clone, PartialEq, Eq)]
pub struct FileEvent {
    pub pid: u32,
    pub file_object: u32,
    pub create_option: Option<i32>,
    pub file_name: String,
    pub renamed_file_name: Option<String>,
    pub created_at: i64,
    pub proc_info: ProcOwner,
    pub operation: EventType,
}

impl FileEvent {
    pub fn is_temp_event(&self, event_type: &EventType) -> bool {
        if event_type == &EventType::FileRename {
            trace!(
                "Checking temp event target: {}, renamed: {:?}",
                self.file_name,
                self.renamed_file_name
            );
            self.renamed_file_name
                .as_ref()
                .is_some_and(|f| ExcludedFileOperation::should_exclude(&f.as_str()))
        } else {
            ExcludedFileOperation::should_exclude(&self.file_name)
        }
    }

    pub fn unique_key(&self) -> String {
        format!("{}-{}-{}", self.file_name, self.pid, self.operation)
    }
}

impl From<FileCreateInfo> for FileEvent {
    fn from(value: FileCreateInfo) -> Self {
        FileEvent {
            pid: value.ProcessId,
            file_object: value.FileObject,
            operation: EventType::FileCreate,
            file_name: U16String(value.FileName.as_ref()).into(),
            create_option: Some(value.CreateOption),
            created_at: large_integer_to_system_time(value.header.Time),
            ..Default::default()
        }
    }
}

impl From<FileDeleteInfo> for FileEvent {
    fn from(value: FileDeleteInfo) -> Self {
        FileEvent {
            pid: value.ProcessId,
            file_object: value.FileObject,
            operation: EventType::FileDelete,
            file_name: U16String(value.FileName.as_ref()).into(),
            created_at: large_integer_to_system_time(value.header.Time),
            ..Default::default()
        }
    }
}

impl From<FileRenameINFO> for FileEvent {
    fn from(value: FileRenameINFO) -> Self {
        FileEvent {
            pid: value.ProcessId,
            operation: EventType::FileRename,
            file_name: U16String(value.SourceFileName.as_ref()).into(),
            renamed_file_name: Some(U16String(value.TargetFileName.as_ref()).into()),
            created_at: large_integer_to_system_time(value.header.Time),
            ..Default::default()
        }
    }
}

impl From<FileReadInfo> for FileEvent {
    fn from(value: FileReadInfo) -> Self {
        FileEvent {
            pid: value.ProcessId,
            operation: EventType::FileRead,
            file_name: U16String(value.FileName.as_ref()).into(),
            created_at: large_integer_to_system_time(value.header.Time),
            ..Default::default()
        }
    }
}

impl From<FileWriteInfo> for FileEvent {
    fn from(value: FileWriteInfo) -> Self {
        FileEvent {
            pid: value.ProcessId,
            operation: EventType::FileWrite,
            file_name: U16String(value.FileName.as_ref()).into(),
            created_at: large_integer_to_system_time(value.header.Time),
            ..Default::default()
        }
    }
}

#[derive(Debug, Default, PartialEq, Eq, Clone)]
pub struct WinDriverEvent {
    event_type: EventType,
    file_payload: Option<FileEvent>,
    process_payload: Option<ProcessEvent>,
    registry_payload: Option<RegistryEvent>,
}

impl WinDriverEvent {
    pub fn should_skip_by_process(&self, excluded_processes: &HashSet<String>) -> bool {
        if let Some(file_data) = &self.file_payload {
            excluded_processes.contains(file_data.proc_info.get_name())
        } else if let Some(registry_data) = &self.registry_payload {
            excluded_processes.contains(registry_data.proc_info.get_name())
        } else {
            false
        }
    }

    pub fn timestamp(&self) -> i64 {
        if let Some(file_data) = &self.file_payload {
            file_data.created_at
        } else if let Some(process_data) = &self.process_payload {
            process_data.created_at
        } else if let Some(registry_data) = &self.registry_payload {
            registry_data.created_at
        } else {
            0
        }
    }
    pub fn unique_key(&self) -> String {
        if let Some(file_data) = &self.file_payload {
            file_data.unique_key()
        } else if let Some(process_data) = &self.process_payload {
            process_data.unique_key()
        } else if let Some(registry_data) = &self.registry_payload {
            registry_data.unique_key()
        } else {
            "".to_owned()
        }
    }

    pub fn get_event_type(&self) -> &EventType {
        &self.event_type
    }

    pub fn get_file_data(&self) -> Option<&FileEvent> {
        self.file_payload.as_ref()
    }

    pub fn get_process_data(&self) -> Option<&ProcessEvent> {
        self.process_payload.as_ref()
    }

    pub fn set_process_data(&mut self, data: ProcessEvent) {
        self.process_payload = Some(data);
    }

    pub fn set_file_data(&mut self, data: FileEvent) {
        self.file_payload = Some(data);
    }

    pub fn set_registry_data(&mut self, data: RegistryEvent) {
        self.registry_payload = Some(data);
    }

    pub fn get_registry_data(&self) -> Option<&RegistryEvent> {
        self.registry_payload.as_ref()
    }

    pub fn process_rename_file_operation(mut self) -> Self {
        if self.event_type.clone() == EventType::FileRename {
            if let Some(mut file_data) = self.file_payload.clone() {
                if ExcludedFileOperation::is_tmp_extension(&file_data.file_name) {
                    self.event_type = EventType::FileWrite;
                    file_data.file_name = file_data.renamed_file_name.clone().unwrap_or_default();
                    file_data.renamed_file_name = None;
                    self.set_file_data(file_data);
                }
            }
        }
        self
    }

    pub fn is_file_event(&self) -> bool {
        match self.event_type {
            EventType::FileRename
            | EventType::DirCreate
            | EventType::DirDelete
            | EventType::DirRename
            | EventType::FileCreate
            | EventType::FileDelete
            | EventType::FileRead
            | EventType::FileWrite
            | EventType::FileBlock => true,
            _ => false,
        }
    }

    pub fn is_process_create_event(&self) -> bool {
        self.event_type == EventType::ProcessCreate
    }

    pub fn is_process_exit_event(&self) -> bool {
        self.event_type == EventType::ProcessExit
    }

    pub fn is_process_event(&self) -> bool {
        match self.event_type {
            EventType::ProcessCreate | EventType::ProcessExit => true,
            _ => false,
        }
    }

    pub fn is_registry_event(&self) -> bool {
        match self.event_type {
            EventType::RegKeyCreate
            | EventType::RegKeyDelete
            | EventType::RegKeyRename
            | EventType::RegSaveKey
            | EventType::RegSetValue
            | EventType::RegValueCreate
            | EventType::RegValueDelete => true,
            _ => false,
        }
    }
}

impl TryFrom<&COMMON_MONINFO> for WinDriverEvent {
    type Error = WinDriverHandlerError;

    fn try_from(value: &COMMON_MONINFO) -> Result<Self, Self::Error> {
        match value.eventType {
            CPPEventType::ProcessCreate => Ok(WinDriverEvent {
                event_type: value.eventType.into(),
                process_payload: Some(unsafe { value.eventInfo.procCreateInfo.into() }),
                ..Default::default()
            }),
            CPPEventType::ProcessExit => Ok(WinDriverEvent {
                event_type: value.eventType.into(),
                process_payload: Some(unsafe { value.eventInfo.procExitInfo.into() }),
                ..Default::default()
            }),
            CPPEventType::FileCreate | CPPEventType::DirCreate => Ok(WinDriverEvent {
                event_type: value.eventType.into(),
                file_payload: Some(unsafe { value.eventInfo.fileCreateInfo.into() }),
                ..Default::default()
            }),
            CPPEventType::FileDelete | CPPEventType::DirDelete => Ok(WinDriverEvent {
                event_type: value.eventType.into(),
                file_payload: Some(unsafe { value.eventInfo.fileDeleteInfo.into() }),
                ..Default::default()
            }),
            CPPEventType::FileRead | CPPEventType::FileAccessBlock => Ok(WinDriverEvent {
                event_type: value.eventType.into(),
                file_payload: Some(unsafe { value.eventInfo.fileReadInfo.into() }),
                ..Default::default()
            }),
            CPPEventType::FileRename | CPPEventType::DirRename => Ok(WinDriverEvent {
                event_type: value.eventType.into(),
                file_payload: Some(unsafe { value.eventInfo.fileRenameInfo.into() }),
                ..Default::default()
            }),
            CPPEventType::FileWrite => Ok(WinDriverEvent {
                event_type: value.eventType.into(),
                file_payload: Some(unsafe { value.eventInfo.fileWriteInfo.into() }),
                ..Default::default()
            }),
            CPPEventType::ProcessBlock => Ok(WinDriverEvent {
                event_type: value.eventType.into(),
                ..Default::default()
            }),
            CPPEventType::RegKeyCreate
            | CPPEventType::RegKeyDelete
            | CPPEventType::RegKeyRename
            | CPPEventType::RegValueCreate
            | CPPEventType::RegValueDelete
            | CPPEventType::RegiSaveKey
            | CPPEventType::RegistrySetValue => Ok(WinDriverEvent {
                event_type: value.eventType.into(),
                registry_payload: Some(unsafe { value.eventInfo.regSetValueInfo.into() }),
                ..Default::default()
            }),
            CPPEventType::None => Err(WinDriverHandlerError::FailedToConvertEvent(value.eventType)),
        }
    }
}
