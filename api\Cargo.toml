[package]
name = "api"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
logger = { path = "../logger" }
database = { path = "../database" }
shell = { path = "../shell" }
reqwest = { version = "0.12.22", features = [
  "json",
  "stream",
  "rustls-tls",
  "multipart",
], default-features = false }
serde_json = { workspace = true }
serde = { workspace = true }
thiserror = { workspace = true }
anyhow = { workspace = true }
tokio = { workspace = true }
futures-util = { workspace = true }
tokio-stream = { workspace = true }
async-trait = { workspace = true }
cfg-if = { workspace = true }
tokio-util = { workspace = true }
async-speed-limit = { workspace = true }
regex = { workspace = true }
reqwest-middleware = { version = "0.4.2", features = ["json", "multipart"] }
reqwest-retry = "0.7.0"
bytes = "1.10.1"
urlencoding = "2.1.3"
http = "1.3.1"
