use crate::start_up_items::start_up_item::{StartUpItem, StartUpItemStatus};
use glob::glob;
use logger::{error, trace};
use regex::Regex;
use std::{
    collections::{HashMap, HashSet},
    fs,
    path::{Path, PathBuf},
};
use utils::shutdown::is_system_running;
use windows_registry::WinRegistry;

fn from_registry(path: &Path) -> HashSet<StartUpItem> {
    trace!("Checking registry at path {}", path.display());
    let mut items = HashSet::new();
    if let Ok(registry) = WinRegistry::new(&path) {
        let values = registry.values(None);
        trace!(
            "Found total {} items at registry {}",
            values.len(),
            path.display()
        );
        for (name, path) in values {
            items.insert(StartUpItem::default().name(name).path(path));
        }
    }
    items
}

fn from_folder(path: &Path) -> HashSet<StartUpItem> {
    let dir = match fs::read_dir(&path) {
        Ok(dir) => dir,
        Err(error) => {
            error!(
                ?error,
                "Failed to read directory at path {}",
                path.display()
            );
            return HashSet::new();
        }
    };

    let mut items = HashSet::new();
    for entry in dir {
        if let Err(error) = entry.as_ref() {
            error!(?error, "Failed to read directory entry");
            continue;
        }
        let entry = entry.unwrap();
        items.insert(
            StartUpItem::default()
                .name(entry.file_name().to_string_lossy().to_string())
                .path(entry.path().to_string_lossy().to_string()),
        );
    }
    items
}

pub fn get_start_up_items() -> HashSet<StartUpItem> {
    let mut start_up_items = HashSet::new();
    let mut statuses: HashMap<String, StartUpItemStatus> = HashMap::new();

    let status_enabled_reg = Regex::new(r"^02.*$").unwrap();

    if let Ok(registry) = WinRegistry::new("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer\\StartupApproved") {
        statuses.extend(
        registry.values(None).into_iter()
                .map(|(key, value)| {
                    let value = format!("0{}", value.trim_start_matches("[").trim_end_matches("]").split(", ").collect::<Vec<_>>().join(""));
                    (key, status_enabled_reg.is_match(&value).into())
                }
            )
        );
    }

    // get global startup items from registry
    let registry_paths = [
        "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run",
        "HKEY_LOCAL_MACHINE\\SOFTWARE\\WOW6432Node\\Microsoft\\Windows\\CurrentVersion\\Run",
        "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\Explorer\\Run",
    ];
    for path in registry_paths {
        start_up_items.extend(from_registry(PathBuf::from(path).as_path()));
    }

    // get user startup items from directory
    start_up_items.extend(WinRegistry::get_users_key().iter().flat_map(|sid| {
            if let Ok(registry) = WinRegistry::new(format!(
                "HKU\\{}\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer\\StartupApproved\\Run",
                sid
            )) {
                statuses.extend(
                registry.values(None).into_iter()
                .map(|(key, value)| {
                            let value = format!("0{}", value.trim_start_matches("[").trim_end_matches("]").split(", ").collect::<Vec<_>>().join(""));
                            (key, status_enabled_reg.is_match(&value).into())
                        }
                    )
                );
            }
            vec![
                format!("HKU\\{}\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run", sid),
                format!("HKU\\{}\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\RunNotification", sid),
                format!("HKU\\{}\\SOFTWARE\\WOW6432Node\\Microsoft\\Windows\\CurrentVersion\\Run", sid),
                format!("HKU\\{}\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\Explorer\\Run", sid),
            ].into_iter()
        }).flat_map(|path| from_registry(PathBuf::from(path).as_path()))
        .collect::<HashSet<StartUpItem>>()
    );

    // get global startup items from directory
    start_up_items.extend(from_folder(
        PathBuf::from("C:\\ProgramData\\Microsoft\\Windows\\Start Menu\\Programs\\Startup")
            .as_path(),
    ));

    // get user startup items from directory
    let folders = match glob(
        "C:\\Users\\<USER>\\AppData\\Roaming\\Microsoft\\Windows\\Start Menu\\Programs\\Startup",
    ) {
        Ok(paths) => paths
            .into_iter()
            .filter_map(Result::ok)
            .collect::<Vec<PathBuf>>(),
        Err(error) => {
            error!(?error, "Failed to get users startup items glob error");
            vec![]
        }
    };

    start_up_items.extend(
        folders
            .into_iter()
            .flat_map(|path| from_folder(path.as_path()))
            .collect::<HashSet<StartUpItem>>(),
    );

    start_up_items
        .into_iter()
        .take_while(|_| is_system_running())
        .map(|item| {
            let status = statuses.remove(item.get_name()).unwrap_or_default();
            item.status(status)
        })
        .collect()
}
