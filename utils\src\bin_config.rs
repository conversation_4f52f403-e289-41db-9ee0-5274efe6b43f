use anyhow::Result;
use bincode::serde::{decode_from_reader, encode_into_std_write};
use serde::{de::DeserializeOwned, Serialize};
use std::{
    fs::{self, OpenOptions},
    io::<PERSON><PERSON><PERSON><PERSON><PERSON>,
    path::Path,
};
use tracing::error;

pub fn write_config<P: AsRef<Path>, E: Serialize>(path: P, config: E) -> Result<usize> {
    if path.as_ref().exists() {
        let _ = fs::remove_file(path.as_ref());
    }
    let mut config_file = match OpenOptions::new()
        .create(true)
        .append(true)
        .open(path.as_ref())
    {
        Err(error) => {
            error!(?error, "Failed to create file {}", path.as_ref().display());
            return Err(error.into());
        }
        Ok(file) => file,
    };

    Ok(encode_into_std_write(
        serde_json::to_string(&config)?,
        &mut config_file,
        bincode::config::standard()
            .with_big_endian()
            .with_fixed_int_encoding(),
    )?)
}

pub fn read_config<D: DeserializeOwned, P: AsRef<Path>>(path: P) -> Result<D> {
    let config_file = match OpenOptions::new()
        .create(false)
        .read(true)
        .open(path.as_ref())
    {
        Err(error) => {
            error!(?error, "Failed to read file {}", path.as_ref().display());
            return Err(error.into());
        }
        Ok(file) => file,
    };

    let decoded: String = decode_from_reader(
        BufReader::new(config_file),
        bincode::config::standard()
            .with_big_endian()
            .with_fixed_int_encoding(),
    )?;

    Ok(serde_json::from_str(&decoded)?)
}
