use crate::{args::CmdArgs, prelude::EndpointopsError};
use std::process::exit;

fn parse_mac(mac_str: &str) -> Result<[u8; 6], String> {
    let parts: Vec<&str> = mac_str.split(':').collect();
    if parts.len() != 6 {
        return Err(format!("Invalid MAC format: {}", mac_str));
    }

    let mut mac = [0u8; 6];
    for (i, part) in parts.iter().enumerate() {
        mac[i] = u8::from_str_radix(part, 16)
            .map_err(|e| format!("Invalid hex in '{}': {}", part, e))?;
    }
    Ok(mac)
}

pub fn wol(args: &CmdArgs) -> Result<(), EndpointopsError> {
    if args.mac.is_none() {
        println!("Please provide mac addresses to wake");
        exit(1);
    }

    for mac in args.mac.as_ref().unwrap().split(',') {
        println!("Processing MAC: {}", mac);
        let parsed_mac = match parse_mac(mac.trim()) {
            Ok(mac) => mac,
            Err(e) => {
                println!("Error parsing MAC: {}", e);
                continue;
            }
        };

        let magic_packet = wake_on_lan::MagicPacket::new(&parsed_mac);

        // Send the magic packet via UDP to the broadcast address ***************:9 from 0.0.0.0:0
        match magic_packet.send() {
            Ok(_) => {
                println!("Sent magic packet to {}", mac);
            }
            Err(error) => {
                println!("Error sending magic packet: {:?} for mac {}", error, mac);
            }
        };
    }

    Ok(())
}
