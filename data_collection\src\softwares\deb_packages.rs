use super::software::{PackageType, Software};
use logger::error;
use serde::Deserialize;
use serde_json::json;
use shell::ShellCommand;
use std::{collections::HashSet, fs::OpenOptions, path::PathBuf};
use utils::{runtime::create_new_runtime, shutdown::is_system_running};

#[derive(Debug, Deserialize)]
pub struct DebPackage {
    name: String,
    version: String,
    arch: String,
    status: String,
    priority: String,
    section: String,
    source: String,
    size: u64,
    maintainer: String,
}

impl From<DebPackage> for Software {
    fn from(value: DebPackage) -> Self {
        let version_parts = value.version.split("-");
        let release = if version_parts.clone().count() > 1 {
            version_parts.last().unwrap_or_default().to_owned()
        } else {
            "".to_owned()
        };
        Self {
            name: value.name,
            version: value.version,
            r#type: PackageType::DebianPackages,
            vendor: value.maintainer,
            release,
            arch: value.arch,
            properties: json!({
                "status": value.status,
                "priority": value.priority,
                "section": value.section,
                "source": value.source,
                "size": value.size,
            }),
            ..Default::default()
        }
    }
}

impl DebPackage {
    pub fn collect() -> HashSet<Software> {
        let path = PathBuf::from("/var/lib/dpkg/status");

        if path.exists() && OpenOptions::new().read(true).open(&path).is_ok() {
            let command = create_new_runtime().block_on(async { ShellCommand::new(r#"dpkg-query -W -f '"${Package}","${Version}","${Architecture}","${db:Status-Status}","${Priority}","${Section}","${Source}","${Installed-Size}","${Maintainer}"\n'"#).run().await });
            if let Err(error) = command {
                error!(?error, "Failed to execute dpkg command");
                return HashSet::new();
            }

            let output = command.unwrap().output;
            let mut byte_reader = csv::ReaderBuilder::new()
                .has_headers(false)
                .double_quote(true)
                .from_reader(output.as_bytes());

            byte_reader
                .deserialize::<DebPackage>()
                .into_iter()
                .take_while(|_| is_system_running())
                .filter_map(Result::ok)
                .map(|p| p.into())
                .collect()
        } else {
            HashSet::new()
        }
    }
}
