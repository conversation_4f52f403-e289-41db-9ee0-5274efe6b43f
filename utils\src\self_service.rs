use crate::constants::{ENDPOINTOPS_BINARY_NAME, SELF_SERVICE_APP_NAME};
use std::{io, path::PathBuf};

const ICON: &[u8] = include_bytes!("../../self_service/icons/<EMAIL>");

#[cfg(target_os = "windows")]
mod platform {
    use std::ffi::OsString;
    use std::io;
    use std::iter::once;
    use std::os::windows::ffi::OsStrExt;
    use std::path::PathBuf;
    use std::ptr::null_mut;
    use tracing::{error, info};
    use windows::Win32::Security::{
        AdjustTokenPrivileges, LookupPrivilegeValueW, SecurityImpersonation, LUID_AND_ATTRIBUTES,
        SE_ASSIGNPRIMARYTOKEN_NAME, SE_INCREASE_QUOTA_NAME, SE_PRIVILEGE_ENABLED, SE_TCB_NAME,
        TOKEN_ACCESS_MASK, TOKEN_ADJUST_PRIVILEGES, TOKEN_PRIVILEGES, TOKEN_QUERY,
    };
    use windows::Win32::System::LibraryLoader::GetModuleHandleW;
    use windows::Win32::System::RemoteDesktop::{
        WTSActive, WTSConnectState, WTSEnumerateSessionsW, WTSFreeMemory,
        WTSQuerySessionInformationW, WTSRegisterSessionNotification,
        WTSUnRegisterSessionNotification, NOTIFY_FOR_ALL_SESSIONS, WTS_CONNECTSTATE_CLASS,
        WTS_CURRENT_SERVER_HANDLE, WTS_SESSION_INFOW,
    };
    use windows::Win32::System::Threading::{
        CreateProcessAsUserW, CREATE_NEW_CONSOLE, CREATE_UNICODE_ENVIRONMENT, PROCESS_INFORMATION,
        STARTUPINFOW,
    };
    use windows::Win32::UI::WindowsAndMessaging::{
        CreateWindowExW, DefWindowProcW, DispatchMessageW, GetMessageW, GetWindowLongPtrW,
        PostQuitMessage, RegisterClassW, SetWindowLongPtrW, TranslateMessage, GWLP_USERDATA, MSG,
        WM_DESTROY, WM_WTSSESSION_CHANGE, WNDCLASSW, WS_OVERLAPPEDWINDOW, WTS_SESSION_LOGON,
    };
    use windows::{
        core::PWSTR,
        Win32::{
            Foundation::{GetLastError, *},
            Security::{DuplicateTokenEx, TokenPrimary},
            System::{
                Environment::{CreateEnvironmentBlock, DestroyEnvironmentBlock},
                RemoteDesktop::{WTSGetActiveConsoleSessionId, WTSQueryUserToken},
            },
        },
    };

    extern "system" fn wnd_proc(hwnd: HWND, msg: u32, wparam: WPARAM, lparam: LPARAM) -> LRESULT {
        unsafe {
            match msg {
                WM_WTSSESSION_CHANGE => match wparam.0 as u32 {
                    WTS_SESSION_LOGON => {
                        info!("User logged in (session {})", lparam.0);

                        // Get the command from window user data
                        let cmd_ptr =
                            GetWindowLongPtrW(hwnd, GWLP_USERDATA) as *const std::ffi::c_char;
                        if !cmd_ptr.is_null() {
                            let cmd_cstr = std::ffi::CStr::from_ptr(cmd_ptr);
                            if let Ok(cmd_str) = cmd_cstr.to_str() {
                                if let Err(err) = launch_user_process(cmd_str) {
                                    error!("Failed to launch user process: {:?}", err);
                                }
                            } else {
                                error!("Failed to convert command to string");
                            }
                        } else {
                            error!("No command found in window user data");
                        }
                    }
                    _ => {}
                },
                WM_DESTROY => {
                    // Clean up the allocated command string
                    let cmd_ptr = GetWindowLongPtrW(hwnd, GWLP_USERDATA) as *mut std::ffi::c_char;
                    if !cmd_ptr.is_null() {
                        drop(std::ffi::CString::from_raw(cmd_ptr));
                    }
                    PostQuitMessage(0);
                }
                _ => return DefWindowProcW(hwnd, msg, wparam, lparam),
            }
        }
        LRESULT(0)
    }

    fn start_process_for_user(cmd: &str) -> windows::core::Result<()> {
        let session_id = unsafe { WTSGetActiveConsoleSessionId() };

        // Check if there's an active console session
        if session_id != 0xFFFFFFFF && session_id != 0 {
            info!("Active console session found with id {}", session_id);
            // Launch immediately in the active session
            return launch_user_process(cmd);
        } else {
            info!(
                "No active console session found (session_id: {}), waiting for user logon",
                session_id
            );
        }

        // No active session - set up session monitoring to wait for user logon
        unsafe {
            // Register window class
            let class_name = windows::core::w!("SessionWatcherClass");
            let h_instance = GetModuleHandleW(None)?;
            let wc = WNDCLASSW {
                lpfnWndProc: Some(wnd_proc),
                hInstance: h_instance.into(),
                lpszClassName: class_name,
                ..Default::default()
            };
            RegisterClassW(&wc);

            // Create hidden message window
            let hwnd = match CreateWindowExW(
                Default::default(),
                class_name,
                windows::core::w!("SessionWatcher"),
                WS_OVERLAPPEDWINDOW,
                0,
                0,
                0,
                0,
                None,
                None,
                Some(h_instance.into()),
                None,
            ) {
                Ok(hwnd) => hwnd,
                Err(error) => {
                    error!(?error, "Failed to create window");
                    return Err(error);
                }
            };

            // Store the command in window user data
            let cmd_cstring =
                std::ffi::CString::new(cmd).map_err(|_| windows::core::Error::from_win32())?;
            let cmd_ptr = cmd_cstring.into_raw();
            SetWindowLongPtrW(hwnd, GWLP_USERDATA, cmd_ptr as isize);

            // Register for session notifications
            if WTSRegisterSessionNotification(hwnd, NOTIFY_FOR_ALL_SESSIONS).is_err() {
                error!("Failed to register for WTS session notifications");
            }

            info!("Waiting for session logon events...");

            // Message loop
            let mut msg = MSG::default();
            while GetMessageW(&mut msg, None, 0, 0).into() {
                let _ = TranslateMessage(&msg);
                DispatchMessageW(&msg);
            }

            WTSUnRegisterSessionNotification(hwnd).ok();
        }
        Ok(())
    }

    fn wide_null(s: &str) -> Vec<u16> {
        OsString::from(s).encode_wide().chain(once(0)).collect()
    }

    fn enable_privilege() -> windows::core::Result<()> {
        use windows::Win32::Foundation::ERROR_SUCCESS;
        use windows::Win32::System::Threading::{GetCurrentProcess, OpenProcessToken};

        unsafe {
            let mut token = HANDLE::default();
            OpenProcessToken(
                GetCurrentProcess(),
                TOKEN_ADJUST_PRIVILEGES | TOKEN_QUERY,
                &mut token,
            )?;

            for privilege in [
                SE_INCREASE_QUOTA_NAME,
                SE_TCB_NAME,
                SE_ASSIGNPRIMARYTOKEN_NAME,
            ] {
                let mut luid = LUID::default();
                LookupPrivilegeValueW(None, privilege, &mut luid)?;

                let tp = TOKEN_PRIVILEGES {
                    PrivilegeCount: 1,
                    Privileges: [LUID_AND_ATTRIBUTES {
                        Luid: luid,
                        Attributes: SE_PRIVILEGE_ENABLED,
                    }],
                };

                AdjustTokenPrivileges(
                    token,
                    false,
                    Some(&tp),
                    std::mem::size_of::<TOKEN_PRIVILEGES>() as u32,
                    None,
                    None,
                )?;

                // Check last error: AdjustTokenPrivileges can succeed but still not enable without error check
                let err = GetLastError();
                if err.0 != ERROR_SUCCESS.0 {
                    CloseHandle(token).ok();
                    return Err(windows::core::Error::from_win32());
                }
            }

            CloseHandle(token).ok();
        }
        Ok(())
    }

    fn launch_user_process(cmd: &str) -> windows::core::Result<()> {
        info!("Starting command: {}", cmd); // ensure required privileges on service/process token
        enable_privilege()?;

        unsafe {
            let mut p_sessions: *mut WTS_SESSION_INFOW = null_mut();
            let mut session_count: u32 = 0;

            WTSEnumerateSessionsW(
                Some(WTS_CURRENT_SERVER_HANDLE),
                0,
                1,
                &mut p_sessions,
                &mut session_count,
            )?;

            if session_count == 0 {
                error!("No sessions found");
                return Ok(());
            }

            let sessions = std::slice::from_raw_parts(p_sessions, session_count as usize);

            for session in sessions {
                let session_id = session.SessionId;

                // Query connection state (WTSConnectState)
                let mut p_state: *mut WTS_CONNECTSTATE_CLASS = null_mut();
                let mut bytes_returned: u32 = 0;
                if WTSQuerySessionInformationW(
                    Some(WTS_CURRENT_SERVER_HANDLE),
                    session_id,
                    WTSConnectState,
                    &mut p_state as *mut _ as *mut _,
                    &mut bytes_returned,
                )
                .is_err()
                {
                    error!(
                        "WTSQuerySessionInformationW failed for session {}",
                        session_id
                    );
                    continue;
                }

                if p_state.is_null() {
                    error!(
                        "WTSQuerySessionInformationW returned null state for {}",
                        session_id
                    );
                    continue;
                }

                let state = *p_state;
                WTSFreeMemory(p_state as _);
                if state != WTSActive {
                    info!("session {} not active, skipping", session_id);
                    continue;
                }

                // Get impersonation token for that session
                let mut h_impersonation_token = HANDLE::default();
                if let Err(error) = WTSQueryUserToken(session_id, &mut h_impersonation_token) {
                    error!(
                        "WTSQueryUserToken failed for session {} with error {:?}",
                        session_id, error
                    );
                    continue;
                }
                // Duplicate to primary token
                let mut h_user_token = HANDLE::default();
                // request MAXIMUM_ALLOWED - simpler and avoids missing rights; DuplicateTokenEx will fail if not allowed.
                if DuplicateTokenEx(
                    h_impersonation_token,
                    TOKEN_ACCESS_MASK(0),
                    None,
                    // SecurityImpersonation,
                    SecurityImpersonation,
                    TokenPrimary,
                    &mut h_user_token,
                )
                .is_err()
                {
                    error!("DuplicateTokenEx failed for session {}", session_id);
                    CloseHandle(h_impersonation_token).ok();
                    continue;
                }

                // Create environment block for the user token
                let mut lp_environment: *mut core::ffi::c_void = std::ptr::null_mut();
                if CreateEnvironmentBlock(&mut lp_environment, Some(h_user_token), false).is_err() {
                    error!("CreateEnvironmentBlock failed for session {}", session_id);
                    CloseHandle(h_user_token).ok();
                    CloseHandle(h_impersonation_token).ok();
                    continue;
                }

                // Build mutable command line buffer (must be writable)
                let mut cmd_wide = wide_null(cmd);
                // Startup info (desktop to winsta0\\default so GUI appears)
                let mut desktop_wide = wide_null("winsta0\\default");

                let mut si: STARTUPINFOW = std::mem::zeroed();
                si.cb = std::mem::size_of::<STARTUPINFOW>() as u32;
                // si.lpDesktop expects PWSTR; we pass pointer to our desktop_wide buffer
                si.lpDesktop = PWSTR(desktop_wide.as_mut_ptr());

                let mut pi: PROCESS_INFORMATION = std::mem::zeroed();

                // CreateProcessAsUserW: pass lpEnvironment as *const _ (pointer returned by CreateEnvironmentBlock)
                if let Err(error) = CreateProcessAsUserW(
                    Some(h_user_token),
                    None, // lpApplicationName - let windows parse
                    Some(PWSTR::from_raw(cmd_wide.as_mut_ptr())), // lpCommandLine - must be mutable buffer
                    None,                                         // lpProcessAttributes
                    None,                                         // lpThreadAttributes
                    false,                                        // bInheritHandles
                    CREATE_UNICODE_ENVIRONMENT | CREATE_NEW_CONSOLE,
                    Some(lp_environment as *const _), // lpEnvironment - pointer returned earlier
                    None,                             // lpCurrentDirectory
                    &si,
                    &mut pi,
                ) {
                    error!(
                        "CreateProcessAsUserW failed for session {}: {:?}",
                        session_id, error
                    );
                } else {
                    info!(
                        "Process created for session {} with pid {}",
                        session_id, pi.dwProcessId
                    );
                    CloseHandle(pi.hProcess).ok();
                    CloseHandle(pi.hThread).ok();
                }
                // cleanup
                DestroyEnvironmentBlock(lp_environment).ok();
                CloseHandle(h_user_token).ok();
                CloseHandle(h_impersonation_token).ok();
                // no harm to call RevertToSelf even if we didn't impersonate directly here
                windows::Win32::Security::RevertToSelf().ok();
            }

            // free sessions memory
            WTSFreeMemory(p_sessions as _);
        }
        Ok(())
    }

    pub fn start_now(_app_name: &str, exe_path: &PathBuf) -> io::Result<()> {
        let cmd = format!("\"{}\" --gui", exe_path.display());
        // let cmd = "C:\\Windows\\System32\\notepad.exe";
        info!("starting command {}", cmd);
        if let Err(error) = start_process_for_user(&cmd) {
            error!(?error, "Failed to wait for login or launch user process");
            return Err(io::Error::new(
                io::ErrorKind::Other,
                "Failed to wait for login or launch user process",
            ));
        };

        Ok(())
    }

    pub fn register(_app_name: &str, _exe_path: &PathBuf) -> io::Result<()> {
        Ok(())
    }

    pub fn deregister(_app_name: &str) -> io::Result<()> {
        Ok(())
    }
}

#[cfg(target_os = "macos")]
mod platform {
    use std::fs;
    use std::io;
    use std::path::PathBuf;
    use std::process::Command;

    fn plist_path(app_name: &str) -> PathBuf {
        PathBuf::from(format!("/Library/LaunchAgents/{}.plist", app_name))
    }

    pub fn start_now(app_name: &str, _exe_path: &PathBuf) -> io::Result<()> {
        let plist_path = plist_path(app_name);
        // Load immediately for current user
        Command::new("sh")
            .arg("-c")
            .arg(format!(
                r#"for uid in $(/usr/bin/id -u $(/usr/bin/users)); do
    sudo launchctl bootstrap gui/$uid {}
done"#,
                plist_path.display()
            ))
            .status()
            .ok();

        Ok(())
    }

    pub fn register(app_name: &str, exe_path: &PathBuf) -> io::Result<()> {
        let plist_path = plist_path(app_name);
        let plist_content = format!(
            r#"<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>Label</key>
	<string>{}</string>
	<key>ProgramArguments</key>
	<array>
		<string>{}</string>
		<string>--gui</string>
	</array>
	<key>KeepAlive</key>
	<true/>
	<key>WorkingDirectory</key>
	<string>{}</string>
	<key>RunAtLoad</key>
	<true/>
</dict>
</plist>"#,
            app_name,
            exe_path.display(),
            exe_path.parent().unwrap().display()
        );

        fs::write(&plist_path, plist_content)?;

        Ok(())
    }

    pub fn deregister(app_name: &str) -> io::Result<()> {
        let plist_path = plist_path(app_name);

        // Unload for current user (others unload at next logout)
        Command::new("sh")
            .arg("-c")
            .arg(format!(
                r#"for uid in $(/usr/bin/id -u $(/usr/bin/users)); do
    sudo launchctl bootout gui/$uid {}
done"#,
                plist_path.display()
            ))
            .status()
            .ok();

        let _ = fs::remove_file(&plist_path);
        Ok(())
    }
}

#[cfg(target_os = "linux")]
mod platform {
    use std::fs;
    use std::io;
    use std::path::Path;
    use std::path::PathBuf;
    use std::process::Command;

    pub fn start_now(_app_name: &str, _exe_path: &PathBuf) -> io::Result<()> {
        Ok(())
    }

    pub fn register(app_name: &str, exe_path: &PathBuf) -> io::Result<()> {
        std::fs::write(
            PathBuf::from("/usr/share/icons/hicolor/512x512/apps").join("endpointops.png"),
            super::ICON,
        )
        .ok();

        let desktop_entry = format!(
            r#"[Desktop Entry]
Type=Application
Name={0}
Exec={1} --gui
Icon=endpointops
Hidden=false
NoDisplay=false
X-GNOME-Autostart-enabled=true
Comment=EndpointOps on login
X-GNOME-Autostart-Delay=0
"#,
            app_name,
            exe_path.display(),
        );

        let global_app_dir = Path::new("/usr/share/applications");
        let global_autostart_dir = Path::new("/etc/xdg/autostart");

        fs::create_dir_all(global_app_dir)?;
        fs::create_dir_all(global_autostart_dir)?;

        let app_desktop = global_app_dir.join(format!("{app_name}.desktop"));
        let autostart_desktop = global_autostart_dir.join(format!("{app_name}.desktop"));

        fs::write(&app_desktop, &desktop_entry)?;
        fs::write(&autostart_desktop, &desktop_entry)?;

        // Optionally make sure ownership and permissions are correct
        Command::new("chmod")
            .args([
                "644",
                app_desktop.to_str().unwrap(),
                autostart_desktop.to_str().unwrap(),
            ])
            .status()
            .ok();

        Command::new("gtk-update-icon-cache")
            .args(["/usr/share/icons/hicolor"])
            .status()
            .ok();

        Ok(())
    }

    pub fn deregister(app_name: &str) -> io::Result<()> {
        let global_app_dir = Path::new("/usr/share/applications");
        let global_autostart_dir = Path::new("/etc/xdg/autostart");

        let app_desktop = global_app_dir.join(format!("{app_name}.desktop"));
        let autostart_desktop = global_autostart_dir.join(format!("{app_name}.desktop"));

        fs::remove_file(&app_desktop)?;
        fs::remove_file(&autostart_desktop)?;
        Ok(())
    }
}

pub fn register_gui_autostart_all_users(install_path: &str) -> io::Result<()> {
    let app_name = SELF_SERVICE_APP_NAME;
    let exe_path = PathBuf::from(install_path).join(ENDPOINTOPS_BINARY_NAME);

    std::fs::write(PathBuf::from(&install_path).join("icon.png"), ICON).ok();

    platform::register(app_name, &exe_path)
}

pub fn start_now(install_path: &str) -> io::Result<()> {
    let app_name = SELF_SERVICE_APP_NAME;
    let exe_path = PathBuf::from(install_path).join(ENDPOINTOPS_BINARY_NAME);
    platform::start_now(app_name, &exe_path)
}

pub fn deregister_gui_autostart_all_users() -> io::Result<()> {
    let app_name = SELF_SERVICE_APP_NAME;
    platform::deregister(app_name)
}
