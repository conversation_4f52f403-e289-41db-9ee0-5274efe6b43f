use super::FileAttachment;
use crate::PrimaryKey;
use serde::{Deserialize, Serialize};
use utils::serde::none_if_empty_value;

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct Patch {
    pub id: PrimaryKey,
    pub name: Option<String>,
    #[serde(alias = "title")]
    pub display_name: Option<String>,
    pub description: Option<String>,
    pub patch_update_category: Option<String>,
    pub is_third_party: Option<bool>,
    #[serde(alias = "downloadFileDetails")]
    pub download_files: Option<Vec<FileAttachment>>,
    pub product_type: Option<String>,
    #[serde(alias = "atLeastOneFileInstallation")]
    pub should_succeed_on_single_file_installation: bool,
    pub kb_id: Option<String>,
    #[serde(default, deserialize_with = "none_if_empty_value")]
    pub install_command: Option<String>,
    #[serde(default, deserialize_with = "none_if_empty_value")]
    pub uninstall_command: Option<String>,
    #[serde(default, deserialize_with = "none_if_empty_value")]
    pub upgrade_command: Option<String>,
    #[serde(default, deserialize_with = "none_if_empty_value")]
    pub package_names: Option<Vec<String>>,
}
