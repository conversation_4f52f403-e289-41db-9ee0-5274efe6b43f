use crate::{get_current_date_time, has_task::HasT<PERSON>, TaskExecutionError, OUTPUT_FILE_NAME};
use async_trait::async_trait;
use database::Model;
use logger::{debug, error, info, ModuleLogger};
use std::{path::Path, sync::Arc};
use sysinfo::System;
use tokio::{
    fs::{self, OpenOptions},
    io::AsyncWriteExt,
};
use utils::dir::{get_patch_cab_dir, get_task_execution_dir};

#[async_trait]
pub trait LogTask: HasTask {
    fn logger(&self) -> Arc<ModuleLogger>;

    async fn create_task_dir(&self) -> Result<(), TaskExecutionError> {
        let path = self.get_task_dir();
        if !Path::exists(path.as_ref()) {
            debug!("Creating Execution Directory {}", path.display());
            match fs::create_dir_all(&path).await {
                Ok(()) => {
                    debug!("Created task execution directory");
                }
                Err(error) => {
                    error!(?error, "Failed to create task execution directory");
                    return Err(TaskExecutionError::FailedToCreateTaskFile(error));
                }
            };
            debug!("Created Execution directory {}", path.display());
        }

        match OpenOptions::new()
            .create(true)
            .append(true)
            .open(path.join(OUTPUT_FILE_NAME))
            .await
        {
            Ok(_) => {
                debug!("Created output file");
                Ok(())
            }
            Err(error) => {
                error!(?error, "Failed to create output file");
                return Err(TaskExecutionError::FailedToCreateTaskFile(error));
            }
        }
    }

    async fn remove_task_resources(&self) -> Result<(), TaskExecutionError> {
        if self.get_task().should_persist() {
            let task_id = self.get_id();
            debug!("Cleaning up task {} from database", task_id);
            match self.get_task().delete().await {
                Ok(_) => debug!(%task_id, "Task Deleted successfully from database"),
                Err(error) => {
                    error!(?error, %task_id, "failed to delete task from database");
                }
            };
        }
        if self.get_task().is_patch_scan_task() {
            // delete patch
            let task_id = self.get_id();
            info!("Cleaning up patch directory");
            let path = {
                #[cfg(windows)]
                {
                    utils::dir::get_patch_xml_dir()
                }
                #[cfg(not(windows))]
                {
                    utils::dir::get_patch_dir()
                }
            };
            if fs::try_exists(path.as_ref()).await.is_ok_and(|i| i) {
                match fs::remove_dir_all(path.as_ref()).await {
                    Ok(_) => info!(%task_id, "Removed patch directory"),
                    Err(error) => {
                        error!(?error, %task_id, "failed to remove patch directory")
                    }
                };
            }
            info!("Cleaning up cab directory");
            let path = get_patch_cab_dir();
            if fs::try_exists(path.as_ref()).await.is_ok_and(|i| i) {
                match fs::remove_dir_all(path.as_ref()).await {
                    Ok(_) => info!(%task_id, "Removed patch cab directory"),
                    Err(error) => {
                        error!(?error, %task_id, "failed to remove patch cab directory")
                    }
                };
            }
        }
        if !self.get_task().is_system_action_task() {
            info!("Cleaning up task directory");
        }
        let path = self.get_task_dir();
        if Path::exists(path.as_ref()) {
            match fs::remove_dir_all(path.as_ref()).await {
                Ok(_) => {
                    debug!("Removed task directory {}", path.display());
                    Ok(())
                }
                Err(error) => {
                    error!(?error, "Failed to remove task directory");
                    Err(TaskExecutionError::FailedToRemoveTaskDir(format!(
                        "{:?}",
                        error
                    )))
                }
            }
        } else {
            error!("Task directory {} does not exist", path.display());
            Ok(())
        }
    }

    fn get_log_file(&self) -> Box<Path> {
        Box::from(self.get_task_dir().join(OUTPUT_FILE_NAME))
    }

    fn get_task_dir(&self) -> Box<Path> {
        get_task_execution_dir(self.get_task().id.to_string())
    }

    fn build_log_message(&self, level: Option<&str>, message: String) -> String {
        let host_name = match System::host_name() {
            Some(name) => name,
            None => "Unknown".to_owned(),
        };

        format!(
            "{}: {} {} {}\n",
            get_current_date_time(),
            level.as_ref().unwrap_or(&"INFO"),
            host_name,
            message
        )
    }

    async fn write_task_log(&self, message: String, level: Option<&str>) {
        let file_to_write = OpenOptions::new()
            .append(true)
            .open(self.get_log_file())
            .await;

        let msg = self.build_log_message(level, message);

        if let Ok(mut file_to_write) = file_to_write {
            match file_to_write.write_all(msg.as_bytes()).await {
                Err(error) => {
                    error!(?error, "{}", msg)
                }
                _ => {}
            };

            let _ = file_to_write.write_all(b"\n");

            let _ = file_to_write.flush();
        } else {
            error!("Failed to write task log {:?}", msg);
        }
    }

    async fn read_output(&self) -> String {
        let path = self.get_task_dir();

        match fs::read_to_string(path.join(OUTPUT_FILE_NAME)).await {
            Ok(output) => output,
            Err(error) => {
                error!(?error, "Failed to read output of task {}", self.get_name());
                "".to_owned()
            }
        }
    }
}
