import { Layout, theme, Menu } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { invoke } from '@tauri-apps/api/core';
import Icon from './Icon';
import Image from './Image';
import { useApp } from '../App';

export default function AppHeader({ handleThemeChange, collapsed, setCollapsed, ...props }) {
  const {
    token: { colorBgContainer }
  } = theme.useToken();

  const { state } = useApp();

  const [selectedKeys, setSelectedKeys] = useState(['announcement']);

  const route = useLocation();

  useEffect(() => {
    invoke('get_branding_image');
  }, []);

  useEffect(() => {
    const pathParts = route.pathname.split('/').filter(Boolean);
    setSelectedKeys([pathParts[0]]);
  }, [route]);

  const menuItems = [
    // {
    //   key: 'announcement',
    //   label: (
    //     <span>
    //       <Icon name="dashboard" className="text-lg mr-2" />
    //       Announcements
    //     </span>
    //   )
    // },
    {
      key: 'patch',
      label: (
        <span>
          <Icon name="patch" className="text-lg mr-2" />
          Patch
        </span>
      )
    },
    {
      key: 'softwares',
      label: (
        <span>
          <Icon name="inventory" className="text-lg mr-2" />
          Application
        </span>
      )
    }
  ];

  const navigate = useNavigate();

  function handleOnSelect({ keyPath }) {
    navigate(`/${keyPath.reverse().join('/')}`);
  }

  return (
    <Layout.Header
      {...props}
      className="mr-4 px-2 flex items-center w-full"
      style={{ backgroundColor: colorBgContainer }}>
      <div className="flex items-center border-bottom w-full">
        <Image src={'images/brand-logo.png'} className="h-full max-h-[30px] mx-6" />
        {state.is_agent_approved ? (
          <Menu
            mode="horizontal"
            selectedKeys={selectedKeys}
            items={menuItems}
            onSelect={handleOnSelect}
            style={{ flex: 1, minWidth: 0 }}
          />
        ) : null}
      </div>
    </Layout.Header>
  );
}
