use agent_manager::Agent<PERSON>unnable;
use anyhow::Result;
use async_trait::async_trait;
use database::models::FIMConfig;
use database::{Model, PrimaryKey};
use fim::Watcher;
use logger::{debug, error, ModuleLogger};
use logger::{info, WithSubscriber};
use std::sync::Arc;
use tokio::select;
use tokio::sync::broadcast::{self, Receiver};
use tokio::sync::Mutex;
use utils::shutdown::get_shutdown_signal;

pub struct FIMWatchAgent {
    endpoint_id: i64,
    restart_receiver: Arc<Mutex<Receiver<bool>>>,
}

impl FIMWatchAgent {
    pub fn new(endpoint_id: i64, restart_receiver: Receiver<bool>) -> FIMWatchAgent {
        FIMWatchAgent {
            endpoint_id,
            restart_receiver: Arc::new(Mutex::new(restart_receiver)),
        }
    }
}

#[async_trait]
impl AgentRunnable for FIMWatchAgent {
    fn get_name(&self) -> &str {
        "fim_watch_agent"
    }

    async fn start(&self, logger: Arc<ModuleLogger>) -> Result<()> {
        info!("---------------------- Starting FIM Watch Agent ------------------------");
        let mut shutdown_signal = get_shutdown_signal();

        let mut restart_signal = self.restart_receiver.lock().await;

        let fim_configs = match FIMConfig::default().get_all(None).await {
            Ok(configs) => configs
                .into_iter()
                .map(|mut item| {
                    if item.is_usb.is_some_and(|i| i) {
                        item.id = PrimaryKey::default()
                    }
                    item
                })
                .collect::<Vec<FIMConfig>>(),
            Err(error) => {
                error!(?error, "Error reading FIM config from database");
                tokio::select! {
                    biased;

                    _ = shutdown_signal.recv() => {
                        info!("Shutting Down FIM Watch Agent");
                    },

                    _ = restart_signal.recv() => {
                        info!("Restarting FIM Watch Agent");
                    }
                };
                return Err(error.into());
            }
        };

        if fim_configs.len() == 0 {
            debug!("No configurations found so waiting");
            tokio::select! {
                biased;

                _ = shutdown_signal.recv() => {
                    info!("Shutting Down FIM Watch Agent");
                },

                _ = restart_signal.recv() => {
                    info!("Restarting FIM Watch Agent");
                }
            };
            return Ok(());
        }

        let (restart_tx, _restart_signal) = broadcast::channel(16);

        let watcher: Box<dyn Watcher> = {
            cfg_if! {
                if #[cfg(windows)]
                {
                    use utils::windows_driver_utility;

                    if windows_driver_utility::has_driver() {
                        info!("Driver is available so using driver watcher");
                        Box::new(fim::windows_driver_watcher::FIMWatcher::new(
                            fim_configs,
                            self.endpoint_id,
                            restart_tx.subscribe(),
                        ))
                    } else {
                        info!("Driver is not available so using generic watcher");
                        Box::new(fim::generic_watcher::FIMWatcher::new(fim_configs, self.endpoint_id, restart_tx.subscribe()))
                    }
                } else if #[cfg(target_os = "linux")] {
                    Box::new(fim::linux_ebpf_watcher::FIMWatcher::new(
                        fim_configs,
                        self.endpoint_id,
                        restart_tx.subscribe(),
                    ))
                } else if #[cfg(target_os = "macos")] {
                    Box::new(fim::generic_watcher::FIMWatcher::new(fim_configs, self.endpoint_id, restart_tx.subscribe()))
                }
            }
        };

        let mut watcher_task = tokio::task::spawn(
            async move {
                if let Err(error) = watcher.watch().await {
                    error!(?error, "Failed to watch files");
                }
            }
            .with_subscriber(logger.subscriber()),
        );

        loop {
            select! {
                biased;

                _ = shutdown_signal.recv() => {
                    info!("Shutting Down FIM Watch Agent");
                    restart_tx.send(true).ok();
                    watcher_task.await.ok();
                    break;
                },

                _ = restart_signal.recv() => {
                    info!("Restarting FIM Watch Agent");
                    restart_tx.send(true).ok();
                    watcher_task.await.ok();
                    break;
                }

                _ = &mut watcher_task => {
                    info!("Finished all watchers execution This should never happen");
                    shutdown_signal.recv().await.ok();
                }
            }
        }

        info!("---------------------- Stopped FIM Watch Agent ------------------------");
        Ok(())
    }
}
