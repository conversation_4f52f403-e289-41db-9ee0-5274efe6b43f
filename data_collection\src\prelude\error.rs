use anyhow::Error as AnyhowError;
use api::ApiError;
use serde_json::Error as SerdeError;
use shell::ShellError;
use thiserror::Error;
use tokio::task::JoinError;

#[derive(Error, Debug)]
pub enum DataCollectionError {
    #[error("Data Collection Error: Shell Command execution error")]
    ShellExecutionError(#[from] ShellError),

    #[error("Data Collection Error: Serialize/Deserialize error {0:?}")]
    SerdeError(#[from] SerdeError),

    #[error("Data Collection Error: Failed to finish api {0:?}")]
    ApiError(#[from] ApiError),

    #[error("Data Collection Error: Failed to join tokio task {0:?}")]
    TokioJoinError(#[from] JoinError),

    #[error("Data Collection Error: Failed to collect data {0:?}")]
    DataCollectorError(#[from] AnyhowError),
}
