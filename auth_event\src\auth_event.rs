use database::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Uuid};
use serde::{Deserialize, Serialize};

#[derive(Debug, Default, Serialize, Deserialize, Clone, Hash, PartialEq, Eq)]
pub enum Action {
    Login,
    Logout,
    #[serde(rename = "Login Failed")]
    LoginFailed,
    #[default]
    Other,
}

impl From<String> for Action {
    fn from(value: String) -> Self {
        match value.as_str() {
            "Login" => Action::Login,
            "Logout" => Action::Logout,
            "Login Failed" => Action::LoginFailed,
            _ => Action::Other,
        }
    }
}

#[derive(Debug, Default, Serialize, Deserialize, Clone, PartialEq, Eq, Hash)]
pub struct AuthEvent {
    id: PrimaryKey,
    event_id: String,
    action: Action,
    username: String,
    event_time: i64,
    ip_address: String,
}

impl AuthEvent {
    pub fn new(action: Action, username: String, ip_address: String) -> Self {
        AuthEvent {
            action,
            username,
            ip_address,
            ..Default::default()
        }
    }

    pub fn set_event_time(&mut self, time: i64) {
        self.event_time = time;
    }

    pub fn generate_id(&mut self) {
        let uuid = Uuid::new_v4().to_string();
        self.id = PrimaryKey::LocalId(uuid.to_owned());
        self.event_id = uuid;
    }
}

impl HasPrimaryKey for AuthEvent {
    fn table_name(&self) -> &str {
        "auth_events"
    }

    fn get_primary_key(&self) -> &PrimaryKey {
        &self.id
    }
}

impl Model for AuthEvent {}
