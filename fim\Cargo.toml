[package]
name = "fim"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
logger = { path = "../logger" }
database = { path = "../database" }
api = { path = "../api" }
utils = { path = "../utils" }
thiserror = { workspace = true }
tokio = { workspace = true }
anyhow = { workspace = true }
tokio-stream = { workspace = true }
serde = { workspace = true }
chrono = { workspace = true }
serde_json = { workspace = true }
async-trait = { workspace = true }
globset = "0.4.16"
notify-debouncer-full = "0.5.0"

[target.'cfg(windows)'.dependencies]
windows_driver_handler = { path = "../windows_driver_handler" }

[target.'cfg(target_os = "linux")'.dependencies]
linux_ebpf = { path = "../linux_ebpf" }
