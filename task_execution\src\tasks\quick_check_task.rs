use super::command_executor::CommandExecutor;
use crate::{
    has_task::HasTask, log_task::LogTask, sync_task::SyncTask, TaskExecutable, TaskExecutionError,
};
use anyhow::{Error, Result};
use async_trait::async_trait;
use database::{
    data_types::TaskResult,
    models::{CommandType, LogicalCondition, OutputMatch, Task, TaskStatus, TaskType},
};
use evalexpr::eval_boolean;
use logger::{debug, error, ModuleLogger};
use serde_json::json;
use std::{path::Path, sync::Arc};
use tokio::sync::RwLock;
use utils::dir::get_current_dir;

#[derive(Debug)]
pub struct QuickCheckTask {
    task: Task,
    logger: Arc<ModuleLogger>,
    output: RwLock<String>,
}

impl QuickCheckTask {
    pub fn new(task: Task) -> Self {
        Self {
            task,
            output: RwLock::new(String::new()),
            logger: ModuleLogger::new("quick_check", None, Some("quick_check".to_owned())),
        }
    }

    async fn match_output(&self, output: &OutputMatch, result: &str) -> String {
        let match_expression = output.get_expression(result);
        let group_condition = output
            .group_condition
            .as_ref()
            .map_or(LogicalCondition::And, |i| i.to_owned());
        let group_condition = group_condition.expression();
        match output.is_passed(result) {
            Ok(value) => {
                debug!(
                    "Evaluated output match {} with result {}",
                    match_expression, value
                );
                let _ = self
                    .write_task_log(
                        format!(
                            "Evaluated output match {} with result {}",
                            match_expression, value
                        ),
                        None,
                    )
                    .await;
                format!(" {} {}", value, group_condition)
            }
            Err(error) => {
                error!(?error, "Failed to evaluate output match {:?}", output);
                let _ = self
                    .write_task_log(
                        format!(
                            "Failed to evaluate {} with error {:?}",
                            match_expression, error
                        ),
                        Some("ERROR"),
                    )
                    .await;
                format!(" {} {}", "false", group_condition)
            }
        }
    }
}

impl HasTask for QuickCheckTask {
    fn get_task(&self) -> &Task {
        &self.task
    }

    fn set_task(&mut self, task: Task) {
        self.task = task;
    }
}

#[async_trait]
impl LogTask for QuickCheckTask {
    fn logger(&self) -> Arc<ModuleLogger> {
        Arc::clone(&self.logger)
    }

    fn get_task_dir(&self) -> Box<Path> {
        get_current_dir()
    }

    async fn create_task_dir(&self) -> Result<(), TaskExecutionError> {
        debug!("Quick check task doesn't need to create directory");
        Ok(())
    }

    async fn remove_task_resources(&self) -> Result<(), TaskExecutionError> {
        debug!("Quick check task doesn't have any resources");
        Ok(())
    }

    async fn write_task_log(&self, message: String, level: Option<&str>) {
        let msg = self.build_log_message(level, message);

        let mut value = self.output.write().await;
        *value = format!("{}{}\n", *value, msg);
    }

    async fn read_output(&self) -> String {
        let value = self.output.read().await;

        value.to_owned()
    }
}

impl SyncTask for QuickCheckTask {}

#[async_trait]
impl TaskExecutable for QuickCheckTask {
    async fn execute(&mut self) -> Result<TaskResult, Error> {
        self.write_task_log(
            format!("Initiating execution of quick check {}", self.get_name()),
            None,
        )
        .await;

        let mut task_result = TaskResult::default();

        if self.get_task().task_type == Some(TaskType::QuickCheck)
            && self.get_task().quick_check.is_none()
        {
            if self.get_task().custom_task_details.is_none() {
                error!(
                    "No Quick Check detail found for quick_check_task {:?}",
                    self.task
                );
                self.write_task_log(
                    "No Quick Check detail found for quick_check_task".to_owned(),
                    Some("ERROR"),
                )
                .await;
                task_result.status = TaskStatus::Failed;
                task_result.output = "".to_owned();
                task_result.exit_code = 99;
                return Ok(task_result);
            }
            let mut quick_check = self.task.custom_task_details.clone().unwrap();
            let rules = quick_check.as_object_mut().unwrap();
            if rules.contains_key("id") == false {
                rules.insert("id".to_owned(), 0.into());
            }
            self.task.quick_check = Some(json!(rules).into());
        }

        // if no compliance found return error
        if self.get_task().quick_check.is_none() {
            error!("No quick check found for {:?}", self.task);
            self.write_task_log("No quick check found".to_owned(), Some("ERROR"))
                .await;
            task_result.status = TaskStatus::Failed;
            task_result.output = "".to_owned();
            task_result.exit_code = 99;
            return Ok(task_result);
        }

        let quick_check = self.get_task().quick_check.as_ref().unwrap();

        let command_executor = if quick_check
            .command_type
            .as_ref()
            .is_some_and(|item| item == &CommandType::Powershell)
        {
            CommandExecutor::new_powershell(&quick_check.command, Box::new(self))
        } else {
            CommandExecutor::new_command(&quick_check.command, Box::new(self))
        };
        // execute each action
        let result = command_executor.capture().execute().await?;
        debug!("Result for quick check command {}", result.output);
        task_result.exit_code = result.exit_code;

        // we match output only if output is given
        let mut rule_output_matches_expression = "".to_owned();

        for output_match in quick_check.expected_output.iter() {
            rule_output_matches_expression.push_str(
                self.match_output(output_match, &result.output)
                    .await
                    .as_str(),
            );
        }

        if rule_output_matches_expression.is_empty() == false {
            let rule_output_matches_expression = rule_output_matches_expression
                .as_str()
                .trim()
                .trim_end_matches("&&")
                .trim_end_matches("||");

            match eval_boolean(rule_output_matches_expression) {
                Ok(result) => {
                    debug!(
                        "Rule output matches expression {} evaluate to {}",
                        rule_output_matches_expression, result
                    );
                    if result {
                        task_result.exit_code = 0;
                        task_result.status = TaskStatus::Success;
                    } else {
                        task_result.exit_code = 99;
                        task_result.status = TaskStatus::Failed;
                    }
                }
                Err(error) => {
                    error!(
                        ?error,
                        "Failed to evaluate output matches expression {}",
                        rule_output_matches_expression
                    );
                    task_result.exit_code = 99;
                    task_result.status = TaskStatus::Failed;
                    self.write_task_log(
                        format!(
                            "Failed to evaluate expression {}, {:?}",
                            rule_output_matches_expression, error
                        ),
                        Some("ERROR"),
                    )
                    .await;
                }
            };
        } else {
            self.write_task_log(format!("No output matching is provided"), None)
                .await;
            task_result.exit_code = 0;
            task_result.status = TaskStatus::Success;
        }

        Ok(task_result)
    }
}
