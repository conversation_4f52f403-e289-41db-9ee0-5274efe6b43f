use serde::Deserialize;
use std::fmt::Display;

#[derive(Debug, Deserialize, PartialEq, Eq)]
pub enum Operator {
    #[serde(alias = ">=", alias = "greaterthanorequalto")]
    GreaterThanOrEqualTo,
    #[serde(alias = ">", alias = "greaterthan")]
    GreaterThan,
    #[serde(alias = "=", alias = "equalto")]
    EqualTo,
    #[serde(alias = "<", alias = "lessthan")]
    LessThan,
    #[serde(alias = "<=", alias = "lessthanorequalto")]
    LessThanOrEqualTo,
    #[serde(alias = "contains")]
    Contains,
}

impl Display for Operator {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Operator::GreaterThan => write!(f, ">"),
            Operator::GreaterThanOrEqualTo => write!(f, ">="),
            Operator::EqualTo => write!(f, "=="),
            Operator::LessThan => write!(f, "<"),
            Operator::LessThanOrEqualTo => write!(f, "<="),
            Operator::Contains => write!(f, "contains"),
        }
    }
}
