use crate::{args::CmdArgs, prelude::EndpointopsError};
use agent_manager::{Agent, AgentManager};
use agents::{
    ApprovalAgent, AuthEventAgent, ConfigurationAgent, DataCollectionAgent, FIMAgent,
    QuickCheckAgent, RdpAgent, RefreshCallAgent, ScheduledTaskAgent, SoftwareMeterAgent,
    TaskExecutionAgent,
};
use anyhow::{anyhow, Result};
use api::ApiError;
use console::Style;
use data_collection::{DataCollectionExtension, Provision};
use database::{
    models::{AgentConfig, AgentMetadata, ServerConfig, ServerProductType, Task},
    Database, DatabaseError, DbOptions, Model, PrimaryKey, DB_NAME, DB_NAME_SPACE, DB_PASSWORD,
    DB_USERNAME,
};
use figlet_rs::FIGfont;
use logger::{debug, error, info, ModuleLogger};
use std::{
    cmp,
    path::Path,
    sync::{
        atomic::{AtomicUsize, Ordering},
        OnceLock,
    },
    time::Duration,
};
use tokio::{
    runtime::Builder,
    sync::{
        broadcast::{self, Sender},
        mpsc::unbounded_channel,
    },
    time::sleep,
};
use utils::{
    constants::SHUTDOWN_TIME,
    cpu::{concurrency_cores, max_blocking_threads},
    dir::get_db_dir,
    shutdown::{listen_for_shutdown, set_system_state, trigger_shutdown},
};

pub static SERVER_CONFIG: OnceLock<ServerConfig> = OnceLock::new();
pub static AGENT_METADATA: OnceLock<AgentMetadata> = OnceLock::new();

pub async fn init_db(db_path: &Path) -> Result<(), DatabaseError> {
    Database::init(DbOptions::new(
        db_path,
        DB_NAME_SPACE,
        DB_NAME,
        DB_USERNAME,
        DB_PASSWORD,
    ))
    .await?;
    Ok(())
}

pub async fn init_api(server_url: &str, username: &str, password: &str) -> Result<(), ApiError> {
    let mut api_options = api::ApiOptions::new(server_url.to_owned());
    api_options.with_credential(username.to_owned(), password.to_owned());
    api::init(api_options)
}

pub async fn wait_for_agent_approval() {
    let mut agent_manager = AgentManager::default();
    agent_manager.add_agent(Agent::new(Box::new(ApprovalAgent::new(
        SERVER_CONFIG.get().unwrap(),
    ))));

    // wait for agent approval
    agent_manager.start().await;
}

pub async fn provision_agent(server_config: &ServerConfig) -> Result<i64> {
    let mut provision = Provision::new(
        server_config.uuid().to_owned(),
        server_config.enroll_secret().to_owned(),
    );

    if let Err(error) = provision.collect_and_send(None).await {
        error!("Failed to collect and send provision data");
        return Err(anyhow!(
            "Failed to collect and send provision data with error {:?}",
            error
        ));
    }

    let endpoint_id = provision.get_endpoint_id();

    if endpoint_id == 0 {
        error!("EndpointID is received 0");
        return Err(anyhow!("EndpointID is received 0",));
    }

    Ok(endpoint_id)
}

async fn run_agents(
    product_type: ServerProductType,
    server_config: &'static ServerConfig,
    license_tx: Sender<bool>,
) -> Result<(), EndpointopsError> {
    set_system_state(true);

    #[cfg(windows)]
    {
        use utils::constants::ENDPOINTOPS_SERVICE_NAME;
        use winreg::enums::*;
        use winreg::RegKey;
        let hklm = RegKey::predef(HKEY_LOCAL_MACHINE);
        let path = r"Software\Microsoft\Windows\CurrentVersion\Uninstall";
        match hklm
            .open_subkey_with_flags(format!("{}\\{}", path, ENDPOINTOPS_SERVICE_NAME), KEY_WRITE)
        {
            Ok(key) => {
                match key.set_value("DisplayVersion", &env!("CARGO_PKG_VERSION")) {
                    Err(error) => {
                        error!(?error, "Failed to set DisplayVersion key");
                    }
                    _ => {}
                };
            }
            Err(error) => {
                error!(?error, "Failed to open registry key {}", path);
            }
        }
    }

    info!("Using product license type as {}", product_type);

    let mut agent_manager = AgentManager::default();

    let (task_tx, task_rx) = unbounded_channel::<Task>();

    let (fim_restart_tx, _fim_restart_rx) = broadcast::channel::<bool>(1);

    let agent_metadata = AGENT_METADATA.get().unwrap();

    let configuration_agent =
        ConfigurationAgent::new(agent_metadata, fim_restart_tx.clone(), license_tx);

    // add configuration call agent
    agent_manager.add_agent(Agent::new(Box::new(configuration_agent)));

    // add refresh call agent
    agent_manager.add_agent(Agent::new(Box::new(RefreshCallAgent::new(
        agent_metadata,
        task_tx.clone(),
    ))));

    // add db scheduled task agent
    agent_manager.add_agent(Agent::new(Box::new(ScheduledTaskAgent::new(
        agent_metadata,
        task_tx,
        server_config,
    ))));

    // add data collection agent
    agent_manager.add_agent(Agent::new(Box::new(DataCollectionAgent::new(
        agent_metadata,
        product_type.clone(),
    ))));

    // add task receiver agent
    agent_manager.add_agent(Agent::new(Box::new(TaskExecutionAgent::new(
        agent_metadata,
        task_rx,
    ))));

    agent_manager.add_agent(Agent::new(Box::new(RdpAgent::new())));

    agent_manager.add_agent(Agent::new(Box::new(AuthEventAgent::new(agent_metadata))));

    if product_type.is_ziro_patch() == false && product_type.is_ziro_patch_plus() == false {
        // add fim agent only if not ziro patch
        agent_manager.add_agent(Agent::new(Box::new(FIMAgent::new(
            agent_metadata,
            fim_restart_tx,
        ))));
    }

    if product_type.is_endpointops() {
        // add quick check agent only if not ziro patch
        agent_manager.add_agent(Agent::new(Box::new(QuickCheckAgent::new(agent_metadata))));

        agent_manager.add_agent(Agent::new(Box::new(SoftwareMeterAgent::new(
            agent_metadata,
        ))));
    }

    agent_manager.start().await;
    sleep(Duration::from_secs(SHUTDOWN_TIME as u64)).await;
    Ok(())
}

pub fn start_system() -> Result<(), EndpointopsError> {
    let module_loger = ModuleLogger::new("global", None, Some("endpointops".to_owned()));

    let _guard = module_loger.guard();

    module_loger.set_global().ok();

    info!("starting endpointops agent");
    info!("Agent Version: {}", env!("CARGO_PKG_VERSION"));
    info!(
        "Git Version: {} | {}",
        env!("GIT_BRANCH_OR_TAG"),
        env!("GIT_COMMIT_HASH")
    );

    std::panic::set_hook(Box::new(move |info| {
        let backtrace = backtrace::Backtrace::new();
        error!("Got a Panic With Message: {}", info.to_string());
        error!("Got a panic: {info:#?}\n");
        error!("Stack backtrace:\n{backtrace:?}");
    }));

    let blocking_and_rayon_threads = max_blocking_threads();
    let tokio_worker_threads = cmp::max(concurrency_cores(50) - 1, 1);

    info!(
        "Using {} threads for blocking I/O and {} threads for tokio worker",
        blocking_and_rayon_threads, tokio_worker_threads
    );

    if let Err(error) = rayon::ThreadPoolBuilder::new()
        .num_threads(blocking_and_rayon_threads)
        .stack_size(10 * 1024 * 1024)
        .thread_name(|index| format!("rayon-thread-{}", index))
        .build_global()
    {
        error!(?error, "Failed to configure rayon global pool size");
    }

    let rt = Builder::new_multi_thread()
        .worker_threads(tokio_worker_threads)
        .max_blocking_threads(16)
        .enable_all()
        .thread_stack_size(10 * 1024 * 1024)
        .thread_name_fn(|| {
            static ATOMIC_ID: AtomicUsize = AtomicUsize::new(0);
            let id = ATOMIC_ID.fetch_add(1, Ordering::SeqCst);
            format!("thread-{}", id)
        })
        .build();

    if let Err(error) = rt {
        error!(?error, "Failed to initialise async runtime");
        panic!("Failed to initialise async runtime");
    }

    let runtime = rt.unwrap();

    runtime.block_on(async move {
        init_db(get_db_dir().as_ref()).await?;

        let server_config = ServerConfig::default()
            .set_id(PrimaryKey::ZirozenId(1))
            .load()
            .await?;

        SERVER_CONFIG.get_or_init(|| server_config);

        let server_config = SERVER_CONFIG.get().unwrap();

        init_api(
            server_config.url(),
            server_config.username(),
            server_config.password(),
        )
        .await?;

        wait_for_agent_approval().await;

        let agent_id = match provision_agent(server_config).await {
            Ok(agent_id) => agent_id,
            Err(error) => {
                error!(?error, "Failed to provision agent");
                return Err(EndpointopsError::FailedToProvisionAgent);
            }
        };

        #[cfg(feature = "self_service")]
        if let Err(error) = ipc::client::send_ipc_message(
            ipc::SELF_SERVICE_IPC_SOCK_NAME,
            ipc::IpcMessage {
                r#type: "provisioned".to_owned(),
                ..Default::default()
            },
        )
        .await
        {
            error!(?error, "Failed to notify gui for agent provisioned");
        };
        let metadata = AgentMetadata::new(agent_id, AgentConfig::default());

        debug!("Received Agent Metadata from database {:?}", metadata);

        AGENT_METADATA.get_or_init(|| metadata);

        debug!(
            "Agent metadata {:?}",
            AGENT_METADATA.get().unwrap().get_config()
        );

        let agent_metadata = AGENT_METADATA.get().unwrap();

        // handle initial configuration refresh call
        {
            let (fim_restart_tx, _fim_restart_rx) = broadcast::channel::<bool>(1);
            let (license_tx, _) = broadcast::channel::<bool>(1);

            let configuration_agent =
                ConfigurationAgent::new(agent_metadata, fim_restart_tx.clone(), license_tx);

            configuration_agent.handle_refresh_call().await;
        }

        loop {
            let license_code = agent_metadata.get_config().product_type.unwrap_or_default();

            let (license_tx, mut license_rx) = broadcast::channel::<bool>(1);

            tokio::select! {
                biased;

                _ = listen_for_shutdown() => {
                    info!("Received shutdown signal.");
                    break;
                }

                result = run_agents(license_code, server_config, license_tx) => {
                    match result {
                        Ok(_) => {
                            info!("Agent manager finished execution");
                            break;
                        }
                        Err(error) => {
                            error!(?error, "Agent manager failed to execute");
                        }
                    }
                }

                _ = license_rx.recv() => {
                    info!("Received license update signal restarting agents.");
                    trigger_shutdown();
                    sleep(Duration::from_secs(SHUTDOWN_TIME as u64)).await;
                }
            }
        }

        #[cfg(feature = "self_service")]
        if let Err(error) = ipc::client::send_ipc_message(
            ipc::SELF_SERVICE_IPC_SOCK_NAME,
            ipc::IpcMessage {
                r#type: "shutdown".to_owned(),
                ..Default::default()
            },
        )
        .await
        {
            error!(?error, "Failed to notify gui for shutdown");
        };

        sleep(Duration::from_secs(SHUTDOWN_TIME as u64)).await;

        info!("Shutdown is complete! Good Bye!");

        Ok::<(), EndpointopsError>(())
    })
}

pub fn run(_args: &CmdArgs) -> Result<(), EndpointopsError> {
    if let Some(figure) = FIGfont::standard()
        .expect("unable to initialise fig")
        .convert("EndpointOps")
    {
        let style = Style::new().blue();
        println!("{}", style.apply_to(figure));
    }

    start_system()
}
