use bpf_common::{
    ebpf_program, parsing::<PERSON><PERSON>erIndex, program::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BpfSender, Program, ProgramBuilder,
    ProgramError,
};
use libc::{major, minor};
use logger::error;
use std::{os::linux::fs::MetadataExt, path::PathBuf};
use tokio::fs;

const MODULE_NAME: &str = "file-system-monitor";
const DEV_MINOR_BITS: u32 = 20;
const DEV_MINOR_MASK: u64 = (1 << DEV_MINOR_BITS) - 1;
const FOR_ALL_USER: u32 = 0xFFFFFFFF;

#[repr(C)]
#[derive(<PERSON><PERSON>, Co<PERSON>, Debug)]
struct RuleKey {
    dev_id: u64,
    ino_id: u64,
}

unsafe impl bpf_common::aya::Pod for RuleKey {}

/// Represents a rule for blocking file system operations
/// This struct must match the `blocked_path_entry` struct in the BPF program
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct FileRule {
    /// Path to block (up to MAX_PATH_LEN characters)
    pub path: String,
    /// UID to block, or BLOCK_ALL_USERS for all users
    pub uid: u32,
}

impl FileRule {
    /// Create a new blocked rule
    pub fn new(path: &str, uid: Option<u32>) -> Self {
        Self {
            path: path.to_owned(),
            uid: uid.unwrap_or(FOR_ALL_USER),
        }
    }

    /// Create a rule that blocks for all users
    pub fn for_all_users(path: &str) -> Self {
        Self::new(path, None)
    }

    /// Create a rule that blocks for a specific user
    pub fn for_user(path: &str, uid: u32) -> Self {
        Self::new(path, Some(uid))
    }

    async fn make_key(&self) -> Option<RuleKey> {
        match fs::metadata(&self.path).await {
            Ok(metadata) => {
                let dev = metadata.st_dev();
                let major = major(dev) as u64;
                let minor = minor(dev) as u64;

                Some(RuleKey {
                    dev_id: ((major << DEV_MINOR_BITS) | (minor & DEV_MINOR_MASK)),
                    ino_id: metadata.st_ino(),
                })
            }
            Err(error) => {
                error!(?error, "Failed to read metadata for path {}", self.path);
                return None;
            }
        }
    }
}

pub async fn program(
    ctx: BpfContext,
    sender: impl BpfSender<FsEvent>,
) -> Result<Program, ProgramError> {
    let attach_to_lsm = ctx.lsm_supported();
    let binary = ebpf_program!(&ctx, "probes");
    let mut builder = ProgramBuilder::new(ctx, MODULE_NAME, binary);
    // LSM hooks provide the perfet intercept point for file system operations.
    // If LSM eBPF programs is not supported, we'll attach to the same kernel
    // functions, but using kprobes.
    if attach_to_lsm {
        builder = builder
            .lsm("path_mknod")
            .lsm("path_unlink")
            .lsm("path_mkdir")
            .lsm("path_rmdir")
            .lsm("path_rename")
            .lsm("file_open")
            .lsm("path_link")
            .lsm("path_symlink");
    } else {
        builder = builder
            .kprobe("security_path_mknod")
            .kprobe("security_path_unlink")
            .kprobe("security_path_mkdir")
            .kprobe("security_path_rmdir")
            .kprobe("security_path_rename")
            .kprobe("security_file_open")
            .kprobe("security_path_link")
            .kprobe("security_path_symlink");
    }
    let mut program = match builder.start().await {
        Ok(program) => program,
        Err(error) => {
            error!(?error, "Failed to start file system monitor program");
            return Err(error);
        }
    };
    program
        .read_events("map_output_fs_event", sender.clone())
        .await?;
    Ok(program)
}

#[allow(dead_code)]
#[derive(Debug)]
#[repr(C)]
pub enum FsEvent {
    FileCreated {
        filename: BufferIndex<str>,
    },
    FileDeleted {
        filename: BufferIndex<str>,
    },
    DirCreated {
        filename: BufferIndex<str>,
    },
    DirDeleted {
        filename: BufferIndex<str>,
    },
    FileOpened {
        filename: BufferIndex<str>,
        flags: i32,
    },
    FileLink {
        source: BufferIndex<str>,
        destination: BufferIndex<str>,
        hard_link: bool,
    },
    FileRename {
        source: BufferIndex<str>,
        destination: BufferIndex<str>,
    },
}

pub(crate) fn get_unix_username(uid: u32) -> Option<String> {
    unsafe {
        let mut result = std::ptr::null_mut();
        let amt = match libc::sysconf(libc::_SC_GETPW_R_SIZE_MAX) {
            n if n < 0 => 512 as usize,
            n => n as usize,
        };
        let mut buf = Vec::with_capacity(amt);
        let mut passwd: libc::passwd = std::mem::zeroed();

        match libc::getpwuid_r(
            uid,
            &mut passwd,
            buf.as_mut_ptr(),
            buf.capacity() as libc::size_t,
            &mut result,
        ) {
            0 if !result.is_null() => {
                let ptr = passwd.pw_name as *const _;
                let username = std::ffi::CStr::from_ptr(ptr).to_str().unwrap().to_owned();
                Some(username)
            }
            _ => None,
        }
    }
}

pub mod pulsar {

    use crate::{FileEvent, Operation};
    use std::{path::Path, sync::Arc, time::SystemTime};

    use super::*;
    use bpf_common::{aya::maps::HashMap, parsing::IndexError, program::BpfEvent, Pid};
    use chrono::{DateTime, Utc};
    use logger::{debug, error};
    use pulsar_core::{
        event::FileFlags,
        pdk::{
            Event, IntoPayload, ModuleContext, ModuleError, NoConfig, Payload, SimplePulsarModule,
        },
        Timestamp,
    };
    use tokio::sync::mpsc::Sender;

    pub struct FileSystemMonitorModule {
        pub tx: Sender<FileEvent>,
        pub included_paths: Arc<Vec<PathBuf>>,
        pub blocked_rules: Arc<Vec<FileRule>>,
        pub exception_rules: Arc<Vec<FileRule>>,
    }

    impl SimplePulsarModule for FileSystemMonitorModule {
        type Config = NoConfig;
        type State = FileSystemMonitorState;

        const MODULE_NAME: &'static str = MODULE_NAME;
        const DEFAULT_ENABLED: bool = true;

        async fn init_state(
            &self,
            _config: &Self::Config,
            ctx: &ModuleContext,
        ) -> Result<Self::State, ModuleError> {
            let mut program = match program(ctx.get_bpf_context(), ctx.clone()).await {
                Ok(bpf) => bpf,
                Err(error) => {
                    error!(?error, "BPF Program error failed to start program");
                    return Err(error.into());
                }
            };

            let bpf = program.bpf();
            let mut map: HashMap<_, RuleKey, u32> =
                HashMap::try_from(bpf.map_mut("endpointops_blacklist").unwrap())?;

            // Populate the blocked paths map with rules
            if !self.blocked_rules.is_empty() {
                for rule in self.blocked_rules.iter() {
                    if let Some(key) = rule.make_key().await {
                        if let Err(error) = map.insert(key, rule.uid, 0) {
                            error!(?error, "Failed to insert rule into map {:?}", key);
                        } else {
                            debug!("Rule with key {:?} is added to hashmap", key);
                        }
                    }
                }
            }

            let mut map: HashMap<_, RuleKey, u32> =
                HashMap::try_from(bpf.map_mut("endpointops_exception").unwrap())?;

            // Populate the blocked paths map with rules
            if !self.exception_rules.is_empty() {
                for rule in self.exception_rules.iter() {
                    if let Some(key) = rule.make_key().await {
                        if let Err(error) = map.insert(key, rule.uid, 0) {
                            error!(?error, "Failed to insert rule into map {:?}", key);
                        } else {
                            debug!("Rule with key {:?} is added to exception hashmap", key);
                        }
                    }
                }
            }

            Ok(Self::State {
                _ebpf_program: program,
                channel: self.tx.clone(),
                included_paths: self.included_paths.clone(),
            })
        }

        async fn on_event(
            event: &Event,
            _config: &Self::Config,
            state: &mut Self::State,
            ctx: &ModuleContext,
        ) -> Result<(), ModuleError> {
            let mut should_consider_event = false;
            let mut file_event = FileEvent::default();
            match event.payload() {
                Payload::FileCreated { filename } => {
                    if state.should_consider_event(filename) {
                        should_consider_event = true;
                        file_event.operation = Operation::FileCreated;
                        file_event.source_path = filename.clone();
                    }
                }
                Payload::FileDeleted { filename } => {
                    if state.should_consider_event(filename) {
                        should_consider_event = true;
                        file_event.operation = Operation::FileDeleted;
                        file_event.source_path = filename.clone();
                    }
                }
                Payload::DirCreated { dirname } => {
                    if state.should_consider_event(dirname) {
                        should_consider_event = true;
                        file_event.operation = Operation::DirCreated;
                        file_event.source_path = dirname.clone();
                    }
                }
                Payload::DirDeleted { dirname } => {
                    if state.should_consider_event(dirname) {
                        should_consider_event = true;
                        file_event.operation = Operation::DirDeleted;
                        file_event.source_path = dirname.clone();
                    }
                }
                Payload::FileOpened { filename, flags: _ } => {
                    if state.should_consider_event(filename) {
                        should_consider_event = true;
                        file_event.operation = Operation::FileOpened;
                        file_event.source_path = filename.clone();
                    }
                }
                Payload::FileRename {
                    source,
                    destination,
                } => {
                    if state.should_consider_event(source) {
                        should_consider_event = true;
                        file_event.operation = Operation::FileRename;
                        file_event.source_path = source.clone();
                        file_event.target_path = Some(destination.clone());
                    }
                }
                _ => {}
            }

            if should_consider_event {
                file_event.process = event.header().into();
                let system_time: SystemTime = event.header().timestamp.into();
                let timestamp: DateTime<Utc> = system_time.into();
                file_event.timestamp = timestamp.timestamp();

                match ctx
                    .get_process_tracker()
                    .get(Pid::from_raw(event.header().pid), Timestamp::now())
                    .await
                {
                    Ok(process) => {
                        file_event.process.argv = process.argv.join(" ");
                    }
                    Err(error) => {
                        error!(
                            ?error,
                            "Failed to get process information for event {:?}",
                            event.payload()
                        );
                    }
                }
                if let Err(error) = state.channel.send(file_event).await {
                    error!(?error, "Failed to send file_event {:?}", error.0);
                }
            }

            Ok(())
        }
    }

    pub struct FileSystemMonitorState {
        _ebpf_program: Program,
        included_paths: Arc<Vec<PathBuf>>,
        channel: Sender<FileEvent>,
    }

    impl FileSystemMonitorState {
        fn should_consider_event(&self, path: &String) -> bool {
            let event_path = Path::new(path);
            self.included_paths
                .iter()
                .filter(|p| event_path.starts_with(p))
                .count()
                > 0
                && FileEvent::should_skip_for_path(path) == false
        }
    }

    impl IntoPayload for FsEvent {
        type Error = IndexError;

        fn try_into_payload(data: BpfEvent<Self>) -> Result<Payload, Self::Error> {
            let BpfEvent {
                payload, buffer, ..
            } = data;
            Ok(match payload {
                FsEvent::FileCreated { filename } => Payload::FileCreated {
                    filename: filename.string(&buffer)?,
                },
                FsEvent::FileDeleted { filename } => Payload::FileDeleted {
                    filename: filename.string(&buffer)?,
                },
                FsEvent::DirCreated { filename } => Payload::DirCreated {
                    dirname: filename.string(&buffer)?,
                },
                FsEvent::DirDeleted { filename } => Payload::DirDeleted {
                    dirname: filename.string(&buffer)?,
                },
                FsEvent::FileOpened { filename, flags } => Payload::FileOpened {
                    filename: filename.string(&buffer)?,
                    flags: FileFlags::from_raw_unchecked(flags),
                },
                FsEvent::FileLink {
                    source,
                    destination,
                    hard_link,
                } => Payload::FileLink {
                    source: source.string(&buffer)?,
                    destination: destination.string(&buffer)?,
                    hard_link,
                },
                FsEvent::FileRename {
                    source,
                    destination,
                } => Payload::FileRename {
                    source: source.string(&buffer)?,
                    destination: destination.string(&buffer)?,
                },
            })
        }
    }
}
