import { createContext, useContext, useState, Suspense, useEffect } from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import { Layout, message, notification, ConfigProvider, theme } from 'antd';
import AppHeader from '../components/Header.jsx';
import Loading from '../components/Loading';
import { PageHeader } from '../components/PageHeader';
import { standalonePaths } from '../App.jsx';

const PageHeaderComponent = (props) => {
  const { pageHeader } = PageHeader.usePageHeader();
  return pageHeader;
};

const { Content } = Layout;
const { darkAlgorithm } = theme;
const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

const LayoutContext = createContext();

export default function AppLayout({ children }) {
  const [myTheme, setMyTheme] = useState(null);

  const { pathname } = useLocation();

  const [collapsed, setCollapsed] = useState(true);
  const [masking, setMasking] = useState(false);

  const [autoThemeType, setAutoThemeType] = useState(null);
  // eslint-disable-next-line
  const [messageApi, messageHolder] = message.useMessage();
  const [notificationApi, notificationHolder] = notification.useNotification();

  function handleSetTheme(themeType) {
    setMyTheme(themeType);
    if (themeType === 'dark') {
      document.body.setAttribute('data-theme', 'dark-theme');
    } else if (themeType === 'light') {
      document.body.removeAttribute('data-theme');
    } else if (themeType === 'auto') {
      if (mediaQuery.matches) {
        setAutoThemeType('dark');
        document.body.setAttribute('data-theme', 'dark-theme');
      } else {
        setAutoThemeType('light');
        document.body.removeAttribute('data-theme');
      }
      const listener = (e) => {
        if (e.matches) {
          setAutoThemeType('dark');
          document.body.setAttribute('data-theme', 'dark-theme');
        } else {
          setAutoThemeType('light');
          document.body.removeAttribute('data-theme');
        }
      };
      mediaQuery.addEventListener('change', listener);
    }
  }

  useEffect(() => {
    handleSetTheme('auto');
  }, []);

  return (
    <LayoutContext.Provider
      value={{
        notification: notificationApi,
        message: messageApi,
        setMasking: setMasking,
        theme: myTheme
      }}>
      <ConfigProvider
        componentSize={'small'}
        theme={{
          ...(myTheme === 'dark' || (myTheme === 'auto' && autoThemeType === 'dark')
            ? { algorithm: darkAlgorithm }
            : {})
        }}>
        <Layout className="h-full overflow-hidden">
          {masking ? <div className="drawer-mask-layer" /> : null}
          <PageHeader.Provider>
            <Layout className="bg-page-background">
              {standalonePaths.includes(pathname) ? null : (
                <AppHeader
                  theme={myTheme}
                  collapsed={collapsed}
                  setCollapsed={setCollapsed}
                  handleThemeChange={handleSetTheme}
                  className="mx-4 border-solid border-t-0 border-x-0 border-b border-border"
                />
              )}
              <Content className="mx-4 flex flex-col min-h-0 flex-1 overflow-auto">
                {/* <div className="mb-2 border-bottom">
                  <PageHeaderComponent />
                </div> */}
                <Suspense fallback={<Loading />}>
                  <Outlet />
                  {children}
                </Suspense>
              </Content>
            </Layout>
          </PageHeader.Provider>
          {messageHolder}
          {notificationHolder}
        </Layout>
      </ConfigProvider>
    </LayoutContext.Provider>
  );
}

export function useLayout() {
  return useContext(LayoutContext);
}
