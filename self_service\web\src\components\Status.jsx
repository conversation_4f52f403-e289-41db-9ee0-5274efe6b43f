import Capitalize from 'lodash/capitalize';
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { Tag } from 'antd';

export default function Status({ status, color, useIcon, useTag, ignoreMinWidth }) {
  const iconMap = {
    enabled: <CheckCircleOutlined className="mr-1 text-sm" />,
    approved: <CheckCircleOutlined className="mr-1 text-sm" />,
    active: <CheckCircleOutlined className="mr-1 text-sm" />,
    success: <CheckCircleOutlined className="mr-1 text-sm" />,
    pass: <CheckCircleOutlined className="mr-1 text-sm" />,
    allow: <CheckCircleOutlined className="mr-1 text-sm" />,
    enable: <CheckCircleOutlined className="mr-1 text-sm" />,
    disabled: <CloseCircleOutlined className="mr-1 text-sm" />,
    error: <CloseCircleOutlined className="mr-1 text-sm" />,
    failed: <CloseCircleOutlined className="mr-1 text-sm" />,
    fail: <CloseCircleOutlined className="mr-1 text-sm" />,
    disable: <CloseCircleOutlined className="mr-1 text-sm" />,
    inactive: <CloseCircleOutlined className="mr-1 text-sm" />,
    block: <CloseCircleOutlined className="mr-1 text-sm" />,
    supported: <CheckCircleOutlined className="mr-1 text-sm" />,
    'not supported': <CloseCircleOutlined className="mr-1 text-sm" />,
    decline: <CloseCircleOutlined className="mr-1 text-sm" />,
    not_approved: <CloseCircleOutlined className="mr-1 text-sm" />
  };

  const tagColorMap = {
    'text-success': 'success',
    'text-danger': 'error',
    'text-warning': 'warning',
    'text-[#d4380d]': 'orange'
  };

  const colorMap = {
    enabled: 'text-success',
    approved: 'text-success',
    decline: 'text-danger',
    success: 'text-success',
    supported: 'text-success',
    pass: 'text-success',
    enable: 'text-success',
    active: 'text-success',
    allow: 'text-success',
    disabled: 'text-danger',
    disable: 'text-danger',
    error: 'text-danger',
    failed: 'text-danger',
    'not supported': 'text-danger',
    fail: 'text-danger',
    running: 'text-success',
    up: 'text-success',
    stopped: 'text-danger',
    inactive: 'text-danger',
    block: 'text-danger',
    critical: 'text-danger',
    high: 'text-danger',
    down: 'text-danger',
    medium: 'text-warning',
    unknown: 'text-warning',
    not_approved: 'text-warning',
    low: 'text-[#d4380d]',
    expired: 'text-danger',
    'not expired': 'text-success'
  };

  return (
    <div className={`inline-flex items-center ${colorMap[(status || '').toLowerCase()]}`}>
      {useTag ? (
        <Tag
          color={color || tagColorMap[colorMap[(status || '').toLowerCase()]]}
          className="inline-flex items-center justify-center"
          style={{
            ...(ignoreMinWidth ? {} : { minWidth: '80px' }),
            textAlign: 'center',
            textTransform: 'uppercase'
          }}>
          {useIcon && iconMap[(status || '').toLowerCase()]}
          {Capitalize((status || '').toLowerCase())}
        </Tag>
      ) : (
        <>
          {useIcon && iconMap[(status || '').toLowerCase()]}
          <span className={useIcon ? `ml-1` : ''}>{Capitalize((status || '').toLowerCase())}</span>
        </>
      )}
    </div>
  );
}
