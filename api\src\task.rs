use crate::{get_client, ApiError};
use database::{
    data_types::TaskResult,
    models::{Task, TaskStatus},
};
use serde_json::{json, Value};

pub async fn get_task_by_id(id: i64) -> Result<Task, ApiError> {
    get_client()?
        .get(format!("/patch/agent/task-details/{}", id))
        .await
}

pub async fn change_task_only_status(
    job_ids: Vec<i64>,
    status: TaskStatus,
) -> Result<Value, ApiError> {
    get_client()?
        .put::<Value, _>(
            "/patch/agent/task/bulk/update",
            json!({
                "ids": job_ids,
                "payload": {
                    "taskStatus": status
                }
            }),
        )
        .await
}

pub async fn change_task_status(
    job_ids: &Vec<i64>,
    result: &TaskResult,
) -> Result<Value, ApiError> {
    get_client()?
        .put::<Value, _>(
            "/patch/agent/task/bulk/update",
            json!({
                "ids": job_ids,
                "payload": {
                    "taskStatus": result.status,
                    "taskResult": result.output
                }
            }),
        )
        .await
}
