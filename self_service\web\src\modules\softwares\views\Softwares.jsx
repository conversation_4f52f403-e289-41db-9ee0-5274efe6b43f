import { useState } from 'react';
import { ask, message } from '@tauri-apps/plugin-dialog';
import { Segmented, Row, Col, Button } from 'antd';
import { AppstoreOutlined, BarsOutlined } from '@ant-design/icons';
import { getAllPackagesApi, installPackageApi } from '../api/softwares';
import { CrudProvider } from '../../../components/CrudProvider';
import Icon from '../../../components/Icon';
import PackageCard from '../components/PackageCard';
import SelectedItemPilles from '../../../components/SelectedItemPilles';

export default function Packages({ disabled }) {
  const [viewType, setViewType] = useState('card');

  const columns = [
    {
      title: 'ID',
      dataIndex: 'name',
      key: 'name',
      type: 'view_link'
    },
    {
      title: 'Name',
      dataIndex: 'displayName',
      key: 'displayName',
      render({ record, update }) {
        return (
          <div className="flex items-center">
            {/* <PackageLogo
              disabled={disabled}
              package={record}
              style={{ width: '30px' }}
              className="mr-2"
              onChange={(logo) => {
                update({
                  ...record,
                  iconFile: [logo]
                });
              }}
            /> */}
            <div>{record.displayName}</div>
          </div>
        );
      }
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: 'OS',
      dataIndex: 'os',
      key: 'os',
      align: 'center',
      sortable: false,
      render({ record }) {
        return (
          <div className="text-xl">
            <Icon name={`platform_${record.os}`} />
          </div>
        );
      }
    },
    {
      title: 'Version',
      dataIndex: 'version',
      align: 'center',
      key: 'version',
      sortable: false
    },
    // {
    //   title: 'Type',
    //   dataIndex: 'pkgType',
    //   align: 'center',
    //   key: 'pkgType',
    //   sortable: false,
    //   render({ record }) {
    //     return (
    //       <Tag
    //         color={'processing'}
    //         className="inline-flex items-center justify-center"
    //         style={{
    //           textAlign: 'center',
    //           textTransform: 'uppercase'
    //         }}>
    //         {record.pkgType}
    //       </Tag>
    //     );
    //   }
    // },
    // {
    //   title: 'Tags',
    //   dataIndex: 'tags',
    //   key: 'tags',
    //   sortable: false,
    //   render({ record }) {
    //     return (
    //       <SelectedItemPilles value={record.tags || []} className="inline-block" maxItems={1} />
    //     );
    //   }
    // },
    ...(disabled
      ? []
      : [
          {
            title: '',
            dataIndex: 'actions',
            key: 'actions',
            prependAction({ record }) {
              return <Button onClick={() => handleInstallSoftware(record)}>Install</Button>;
            }
          }
        ])
  ];

  function handleInstallSoftware(software) {
    ask(`Are you sure you want to install ${software.displayName}?`).then((isConfirm) => {
      if (isConfirm) {
        installPackageApi(software)
          .then(() => {
            message('Installation request has been sent successfully', {
              title: 'Success',
              kind: 'info'
            });
          })
          .catch((e) => {
            message(`Failed to send Installation request ${e.toString()}`, {
              title: 'Error',
              kind: 'error'
            });
          });
      }
    });
  }

  return (
    <Row className="h-full ">
      <Col span={24} className="h-full">
        <CrudProvider
          className="h-full"
          columns={columns}
          resourceTitle="Application"
          disableFormScrolling
          hasSearch
          fetchFn={getAllPackagesApi}
          disableColumnSelection={true}
          createSlot={(createFn) =>
            !disabled && (
              <>
                <Segmented
                  value={viewType}
                  onChange={(e) => setViewType(e)}
                  options={[
                    { value: 'list', icon: <BarsOutlined /> },
                    { value: 'card', icon: <AppstoreOutlined /> }
                  ]}
                />
              </>
            )
          }
          tableSlot={
            viewType === 'card'
              ? ({ data, edit, deleteItem }) => (
                  <div className="px-2">
                    <Row gutter={16}>
                      {data.length ? null : (
                        <Col span={24}>
                          <div className="text-center flex flex-col flex-1 min-h-0 items-center justify-center text-base">
                            No Data Found
                          </div>
                        </Col>
                      )}
                      {data.map((item) => (
                        <PackageCard
                          key={item.id}
                          record={item}
                          span={8}
                          onInstall={handleInstallSoftware}
                          disabled={disabled}
                          deleteItem={deleteItem}
                          edit={edit}
                        />
                      ))}
                    </Row>
                  </div>
                )
              : null
          }
        />
      </Col>
    </Row>
  );
}
