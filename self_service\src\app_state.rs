use anyhow::{anyhow, Result};
use api::{agent::get_configuration, self_service::get_agent_id_by_uuid};
use database::models::{AgentMetadata, ServerConfig};
use logger::{debug, error};
use serde::Serialize;
use utils::{bin_config::read_config, dir::get_current_dir};

pub async fn get_current_server_config() -> Result<ServerConfig> {
    match read_config::<ServerConfig, _>(get_current_dir().join("config.bin")) {
        Ok(config) => Ok(config),
        Err(error) => {
            error!(?error, "Failed to read config.bin file");
            Err(anyhow!(format!(
                "Failed to read config.bin file {:?}",
                error
            )))
        }
    }
}

#[derive(Debug, <PERSON><PERSON>, Default, Serialize)]
pub struct AppState {
    pub agent: AgentMetadata,
    pub is_agent_approved: bool,
}

impl AppState {
    pub async fn init() -> Result<Self> {
        let server_config = get_current_server_config().await?;
        let mut api_options = api::ApiOptions::new(server_config.url().to_owned());
        api_options.with_credential(
            server_config.username().to_owned(),
            server_config.password().to_owned(),
        );
        api::init(api_options)?;

        let agent_id_response = get_agent_id_by_uuid(server_config.uuid()).await?;

        if agent_id_response.is_empty() {
            error!("No agent id found for uuid {}", server_config.uuid());
            return Err(anyhow!(
                "No agent id found for uuid {}",
                server_config.uuid()
            ));
        }

        let agent_id = agent_id_response.get("assetId").unwrap();

        let configuration = get_configuration(*agent_id).await?;

        debug!("Got configuration {:?}", configuration);

        let metadata = AgentMetadata::new(*agent_id, configuration);

        Ok(Self {
            agent: metadata,
            is_agent_approved: true,
        })
    }
}
