use crate::TaskExecutable;
use anyhow::{anyhow, Error};
use database::models::FileAttachment;
use logger::{debug, error, info};
use shell::ShellOutput;
use std::collections::HashMap;
use std::os::windows::process::CommandExt;
use std::time::Duration;
use sysinfo::{ProcessRefreshKind, RefreshKind, System};
use tokio::{process::Command, time::sleep};
use utils::dir::get_current_dir;

pub struct Iso<'a> {
    attachment: &'a FileAttachment,
    task: Box<&'a dyn TaskExecutable>,
}

impl<'a> Iso<'a> {
    pub fn new(attachment: &'a FileAttachment, task: Box<&'a dyn TaskExecutable>) -> Self {
        Self { attachment, task }
    }

    pub async fn write_task_error_log(&self, message: String) {
        self.task.write_task_log(message, Some("ERROR")).await;
    }

    pub async fn install(self) -> Result<ShellOutput, Error> {
        let iso_extract_path = self.task.get_task_dir();
        let extract_process = Command::new(get_current_dir().join("7z.exe"))
            .raw_arg(format!(
                "x \"{}\" -y -o\"{}\"",
                self.attachment.path_to_file_str(),
                iso_extract_path.display()
            ))
            .status()
            .await;

        match extract_process {
            Ok(status) => {
                if status.success() {
                    debug!("ISO extracted successfully");
                } else {
                    error!(
                        "ISO extraction failed with exit code {}",
                        status.code().unwrap_or_else(|| 99)
                    );
                    self.write_task_error_log(format!(
                        "ISO extraction failed with exit code {}",
                        status.code().unwrap_or_else(|| 99)
                    ))
                    .await;
                    return Err(anyhow!(
                        "ISO extraction failed with exit code {}",
                        status.code().unwrap_or_else(|| 99)
                    ));
                }
            }
            Err(error) => {
                error!(?error, "Failed to extract ISO");
                self.write_task_error_log(format!("Failed to extract iso {:?}", error))
                    .await;
                return Err(anyhow!("Failed to extract iso {:?}", error).into());
            }
        }

        // let command = "setup.exe /auto upgrade /dynamicupdate enable /eula accept /quiet";
        match std::process::Command::new(self.task.get_task_dir().join("setup.exe"))
            .raw_arg("/auto upgrade /dynamicupdate enable /eula accept /quiet")
            .spawn()
        {
            Err(error) => {
                error!(?error, "Failed to spawn setup.exe command");
                self.write_task_error_log(format!("Failed to spawn setup.exe command {:?}", error))
                    .await;
                return Err(anyhow!("Failed to spawn setup.exe command {:?}", error).into());
            }
            Ok(child) => {
                info!("Successfully spawned setup.exe with pid {}", child.id());
            }
        };

        info!("Waiting for setuphost.exe, setupprep.exe and setup.exe process!");

        let mut found_processes = HashMap::new();

        loop {
            sleep(Duration::from_secs(10)).await;

            let sys = System::new_with_specifics(
                RefreshKind::nothing().with_processes(ProcessRefreshKind::everything()),
            );
            for (pid, process) in sys.processes() {
                if let Some(exe) = process.exe() {
                    if let Some(file_name) = exe.file_name() {
                        if file_name.to_str().is_some_and(|name| {
                            name.to_lowercase() == "setuphost.exe"
                                || name.to_lowercase() == "setupprep.exe"
                                || name.to_lowercase() == "setup.exe"
                        }) {
                            if found_processes.contains_key(file_name.to_str().unwrap()) == false {
                                info!(
                                    "{} has been found with pid {}",
                                    file_name.to_str().unwrap(),
                                    pid
                                );
                                found_processes
                                    .insert(file_name.to_str().unwrap().to_string(), pid.as_u32());
                                self.task
                                    .write_task_log(
                                        format!(
                                            "Update process {} has been spawned with pid {}.",
                                            file_name.to_str().unwrap(),
                                            pid.as_u32()
                                        ),
                                        None,
                                    )
                                    .await;
                            }
                        }
                    }
                }
            }
            if found_processes.len() >= 3 {
                self.task.write_task_log(format!("Update process is running, Please do not interupt the upgrade process, System will reboot automatically to finish upgrade."), None).await;
                break;
            }
        }
        let mut output = ShellOutput::default();
        if found_processes.len() >= 3 {
            output.exit_code = 3010;
        } else {
            output.exit_code = 199;
        }
        Ok(output)
    }

    pub async fn uninstall(self) -> Result<ShellOutput, Error> {
        Err(anyhow!("Uninstall is not supported for ISO file"))
    }

    pub async fn upgrade(self) -> Result<ShellOutput, Error> {
        Err(anyhow!("Upgrade is not supported for ISO file"))
    }
}
