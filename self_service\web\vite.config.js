import { defineConfig } from 'vite';
import path from 'path'; // Import the 'path' module
import react from '@vitejs/plugin-react';
import svgr from 'vite-plugin-svgr';

const host = process.env.TAURI_DEV_HOST;
// https://vite.dev/config/
export default defineConfig(async () => ({
  plugins: [svgr(), react()],

  css: {
    preprocessorOptions: {
      less: {
        math: 'always', // Enable Less math functions
        relativeUrls: true, // Handle relative URLs in Less imports
        javascriptEnabled: true // Allow JavaScript in Less (use with caution)
      }
    },
    // Ensure CSS is properly extracted in production
    extract: true
  },

  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@components': path.resolve(__dirname, './components')
    }
  },

  build: {
    // Ensure CSS is properly handled in production
    cssCodeSplit: true,
    outDir: '../dist',
    emptyOutDir: true,
    rollupOptions: {
      input: {
        main: path.resolve(__dirname, 'index.html')
      },
      output: {
        // Ensure CSS files are properly named
        assetFileNames: (assetInfo) => {
          if (assetInfo.name && assetInfo.name.endsWith('.css')) {
            return 'assets/[name]-[hash][extname]';
          }
          return 'assets/[name]-[hash][extname]';
        }
      }
    }
  },

  // Vite options tailored for Tauri development and only applied in `tauri dev` or `tauri build`
  //
  // 1. prevent Vite from obscuring rust errors
  clearScreen: false,
  // 2. tauri expects a fixed port, fail if that port is not available
  server: {
    port: 1420,
    strictPort: true,
    host: host || false,
    hmr: host
      ? {
          protocol: 'ws',
          host,
          port: 1421
        }
      : undefined,
    watch: {
      // 3. tell Vite to ignore watching `src-tauri`
      ignored: ['**/src-tauri/**']
    }
  }
}));
