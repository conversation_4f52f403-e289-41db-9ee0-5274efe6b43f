use std::fmt::Debug;

use crate::{log_task::LogTask, sync_task::SyncTask};
use anyhow::Result;
use api::task::get_task_by_id;
use async_trait::async_trait;
use database::{
    data_types::TaskResult,
    models::{PatchScanHistory, RestartType, TaskStatus},
    Model,
};
use logger::{debug, error, info};
use shell::ShellCommand;
use utils::reboot::{force_reboot_command, warn_reboot_command};

#[async_trait]
pub trait TaskExecutable: SyncTask + LogTask + Send + Sync + Debug {
    async fn execute(&mut self) -> Result<TaskResult>;

    async fn abort_task(&self, message: String) -> TaskResult {
        info!(
            "Aborting task {:?} with message {}",
            self.get_task(),
            message
        );
        match self.remove_task_resources().await {
            Err(error) => {
                error!(
                    "Failed to remove task resources for task {} with error {:?}",
                    self.get_name(),
                    error
                );
            }
            _ => {}
        };

        TaskResult {
            exit_code: 99,
            status: TaskStatus::Cancelled,
            output: message,
        }
    }

    async fn run(&mut self) -> TaskResult {
        if self.get_task().should_persist() {
            self.change_task_status_in_db(TaskStatus::InProgress).await;
            match get_task_by_id(self.get_id()).await {
                Ok(task) => {
                    if task.should_abort() {
                        return self.abort_task(
                            format!("Aborting task execution at agent as task status from server is received {}", task.status)
                        ).await;
                    }
                }
                Err(error) => {
                    error!(
                        ?error,
                        "Failed to get task with id {} from server",
                        self.get_id()
                    );
                    return self
                        .abort_task(format!(
                            "Failed to get task with id {} from server with error {:?}",
                            self.get_id(),
                            error
                        ))
                        .await;
                }
            }
        }
        let result = self.operate().await;
        if result.status == TaskStatus::Success
            || result.status == TaskStatus::SuccessWithRebootRequired
            || self.get_task().has_all_attempt_exceeded()
        {
            self.send_result_to_server(&result).await;
            // remove task resource only if status is success
            if result.status == TaskStatus::Success {
                match self.remove_task_resources().await {
                    Err(error) => {
                        error!(
                            "Failed to remove task resources for task {} with error {:?}",
                            self.get_name(),
                            error
                        );
                    }
                    _ => {}
                };
            } else {
                // keep task in db with success with reboot status on next reboot we will mark it as success
                self.change_task_status_in_db(result.status.clone()).await;
            }
            if self.get_task().is_deployment_task() && result.status == TaskStatus::Success {
                if let Some(policy) = self.get_task().policy.as_ref() {
                    let restart_type = policy
                        .restart_type
                        .as_ref()
                        .unwrap_or(&RestartType::NoRestart);

                    let reboot_time = policy.reboot_time.unwrap_or(60);
                    let reboot_message = policy
                        .warning_message
                        .clone()
                        .unwrap_or("System will restart in 1 minute to complete update. Please save your work.".to_owned());

                    // triger restart here if given
                    match restart_type {
                        RestartType::ForceRestart => {
                            info!("Executing Force Restart based on deployment policy");
                            if let Err(error) = ShellCommand::new(force_reboot_command().as_str())
                                .run()
                                .await
                            {
                                error!(?error, "Failed to execute shutdown command");
                            };
                        }
                        RestartType::WarnRestart => {
                            info!("Executing Warn Restart based on deployment policy");
                            if let Err(error) = ShellCommand::new(
                                warn_reboot_command(reboot_time, reboot_message.as_str()).as_str(),
                            )
                            .run()
                            .await
                            {
                                error!(?error, "Failed to execute shutdown command");
                            };
                        }
                        RestartType::PromptRestart => {
                            info!("Executing Prompt Restart based on deployment policy");
                            if let Err(error) = ShellCommand::new(
                                warn_reboot_command(reboot_time, reboot_message.as_str()).as_str(),
                            )
                            .run()
                            .await
                            {
                                error!(?error, "Failed to execute shutdown command");
                            }
                        }
                        _ => {}
                    };
                }
            }
        } else {
            info!("We will retry this task again!");
            self.change_task_status_in_db(result.status.clone()).await;
        }

        result
    }

    async fn operate(&mut self) -> TaskResult {
        let mut result = TaskResult::default();

        // create task execution directory and output file
        match self.create_task_dir().await {
            Err(error) => {
                result.output = self.build_log_message(
                    Some("ERROR"),
                    format!(
                        "Failed to prepare for task {} with error {:?}",
                        self.get_name(),
                        error
                    ),
                );
                result.status = TaskStatus::Failed;
                result.exit_code = 99;

                return result;
            }
            _ => {}
        };

        if self.get_task().remaining_attempts() <= 0 {
            error!(
                "Task with 0 retry count should not be received for run {:?}",
                self.get_task()
            );
            self.write_task_log(
                format!(
                    "Task with 0 retry count should not be received for run {:?}",
                    self.get_task()
                ),
                Some("ERROR"),
            )
            .await;
            result.output = self.read_output().await;
            result.exit_code = 99;
            return result;
        }

        if self
            .get_task()
            .deployment
            .as_ref()
            .is_some_and(|d| d.retry_count > 1)
            && self.get_task().should_persist()
        {
            self.write_task_log(
                format!(
                    "======================= Starting {} attempt =======================",
                    (self.get_task().deployment.as_ref().unwrap().retry_count
                        - self.get_task().retry_count.as_ref().unwrap_or(&0)
                        + 1)
                ),
                None,
            )
            .await;
        }

        if self.get_task().should_persist() {
            self.send_result_to_server(&TaskResult {
                status: TaskStatus::InProgress,
                ..Default::default()
            })
            .await;
            self.decrement_retry_count();
            self.change_task_status_in_db(TaskStatus::InProgress).await;
        }

        #[cfg(feature = "self_service")]
        if self.get_task().should_notify() {
            debug!("Notifying gui for task starting");
            let message = self.get_task().get_start_nofitication_message();

            if let Err(error) = ipc::client::send_ipc_message(
                ipc::SELF_SERVICE_IPC_SOCK_NAME,
                ipc::IpcMessage {
                    r#type: "notification".to_owned(),
                    payload: Some(serde_json::json!({
                        "message": message,
                        "title": "EndpointOps"
                    })),
                    ..Default::default()
                },
            )
            .await
            {
                error!(?error, "Failed to notify gui for the notification");
            };
        }

        match self.execute().await {
            Ok(task_result) => {
                result = task_result;
                result.output = self.read_output().await;
            }
            Err(error) => {
                error!(
                    "Failed to execute task {} with error {:?}",
                    self.get_name(),
                    error
                );
                self.write_task_log(
                    format!(
                        "Failed to execute task {} with error {:?}",
                        self.get_name(),
                        error
                    ),
                    Some("ERROR"),
                )
                .await;
                result.status = TaskStatus::Failed;
                result.output = self.read_output().await;
                result.exit_code = 99;
            }
        };

        if self.get_task().is_patch_scan_task() {
            match PatchScanHistory::new(result.status.to_owned())
                .persist()
                .await
            {
                Err(error) => {
                    error!(?error, "Failed to persist last scan history");
                }
                Ok(record) => {
                    debug!(
                        ?record,
                        "Last patch scan history has been saved successfully"
                    )
                }
            };
        }

        #[cfg(feature = "self_service")]
        if self.get_task().should_notify() {
            debug!("Notifying gui for task completion");
            let message = self.get_task().get_finish_nofitication_message(&result);

            if let Err(error) = ipc::client::send_ipc_message(
                ipc::SELF_SERVICE_IPC_SOCK_NAME,
                ipc::IpcMessage {
                    r#type: "notification".to_owned(),
                    payload: Some(serde_json::json!({
                        "message": message,
                        "title": if result.is_succeeded() { "Success" } else { "Error" }
                    })),
                    ..Default::default()
                },
            )
            .await
            {
                error!(
                    ?error,
                    "Failed to notify gui for the task finish notification"
                );
            };
        }

        result
    }
}
