use crate::{has_task::HasTask, log_task::LogTask, sync_task::SyncTask, TaskExecutable};
use anyhow::Result;
use async_trait::async_trait;
#[cfg(windows)]
use database::models::ApplicationControlPolicyCommand;
use database::{
    data_types::TaskResult,
    models::{Task, TaskStatus},
};
use logger::ModuleLogger;
use std::sync::Arc;

#[derive(Debug)]
pub struct ApplicationControlPolicyTask {
    task: Task,
    logger: Arc<ModuleLogger>,
}

impl ApplicationControlPolicyTask {
    pub fn new(task: Task) -> Self {
        Self {
            task,
            logger: ModuleLogger::new(
                "application_control_policy",
                None,
                Some("application_control_policy".to_owned()),
            ),
        }
    }

    #[cfg(windows)]
    pub async fn execute_application_control_policy(
        &self,
        application_control_policy_command: ApplicationControlPolicyCommand,
    ) -> Result<TaskResult> {
        use super::command_executor::CommandExecutor;
        use base64::{prelude::BASE64_STANDARD, Engine};
        use database::{data_types::TaskResult, models::TaskStatus};
        use logger::{error, info};
        use tokio::fs;

        let mut task_result = TaskResult::default();

        if !application_control_policy_command.context.is_empty() {
            let xml_path = self.get_task_dir().join(format!("{}.xml", self.get_id()));

            fs::write(
                xml_path.clone(),
                BASE64_STANDARD.decode(application_control_policy_command.context)?,
            )
            .await?;

            self.write_task_log("Wrote XML file successfully.".to_owned(), None)
                .await;

            let cmd = format!("Set-AppLockerPolicy -xml '{}'", xml_path.display());

            let command_executor = CommandExecutor::new_powershell(&cmd, Box::new(self));

            match command_executor.execute().await {
                Ok(output) => {
                    info!(
                        "command {} executed with exit code {}",
                        cmd, output.exit_code
                    );
                    task_result.exit_code = output.exit_code;
                    task_result.status = if output.succeeded() {
                        TaskStatus::Success
                    } else {
                        TaskStatus::Failed
                    };
                }
                Err(error) => {
                    error!(?error, "Failed to execute command {}", cmd);
                    task_result.exit_code = 99;
                    task_result.status = TaskStatus::Failed;
                }
            };
        } else {
            self.write_task_log(
                "No Context found for application control policy".to_owned(),
                Some("ERROR"),
            )
            .await;
            task_result.exit_code = 99;
            task_result.output = "No Context found for application control policy".to_owned();
        }

        Ok(task_result)
    }
}

impl HasTask for ApplicationControlPolicyTask {
    fn get_task(&self) -> &Task {
        &self.task
    }

    fn set_task(&mut self, task: Task) {
        self.task = task;
    }
}

impl LogTask for ApplicationControlPolicyTask {
    fn logger(&self) -> Arc<ModuleLogger> {
        Arc::clone(&self.logger)
    }
}

impl SyncTask for ApplicationControlPolicyTask {}

#[async_trait]
impl TaskExecutable for ApplicationControlPolicyTask {
    #[cfg(not(windows))]
    async fn execute(&mut self) -> Result<TaskResult> {
        self.write_task_log(
            "Application Control Policy task not supported on this platform".to_owned(),
            Some("ERROR"),
        )
        .await;

        Ok(TaskResult {
            status: TaskStatus::Failed,
            output: "Application Control Policy task not supported on this platform".to_owned(),
            exit_code: 99,
        })
    }

    #[cfg(windows)]
    async fn execute(&mut self) -> Result<TaskResult> {
        use logger::error;

        self.write_task_log(
            format!(
                "Initiating execution of application control policy {}",
                self.get_name()
            ),
            None,
        )
        .await;

        let mut task_result = TaskResult::default();

        // if no custom task detail found return error
        if self.task.custom_task_details.is_none() {
            error!(
                "No ApplicationControlPolicy detail found for {:?}",
                self.task
            );
            self.write_task_log(
                "No ApplicationControlPolicy detail found".to_owned(),
                Some("ERROR"),
            )
            .await;
            task_result.status = TaskStatus::Failed;
            task_result.output = "".to_owned();
            task_result.exit_code = 99;
            return Ok(task_result);
        }

        let application_control_policy_task: ApplicationControlPolicyCommand =
            self.task.custom_task_details.as_ref().unwrap().into();

        Ok(
            match self
                .execute_application_control_policy(application_control_policy_task)
                .await
            {
                Ok(result) => result,
                Err(error) => {
                    error!(
                        ?error,
                        "Failed to execute application control policy task {:?}",
                        self.get_task()
                    );
                    task_result.exit_code = 99;
                    task_result.status = TaskStatus::Failed;
                    task_result
                }
            },
        )
    }
}
