use logger::{debug, error};
use serde::{Deserialize, Serialize};
use serde_repr::{Deserialize_repr, Serialize_repr};
use std::{
    collections::HashSet,
    fmt::{Debug, Display},
    sync::{<PERSON>, RwLock},
};

use super::{FIMConfig, QuickCheck, SoftwareMeterRule};

#[derive(Debu<PERSON>, <PERSON><PERSON><PERSON>, Serialize, Deserialize, Clone)]
pub struct AgentRefreshTimeSettings {
    pub refresh_cycle: u64,
    pub system_action_refresh_cycle: u64,
    pub patch_refresh_cycle: u64,
    pub sbom_refresh_cycle: u64,
    pub process_refresh_cycle: u64,
    pub network_refresh_cycle: u64,
    pub certificate_refresh_cycle: u64,
    pub startup_items_refresh_cycle: u64,
    pub users_refresh_cycle: u64,
    pub resources_refresh_cycle: u64,
    pub file_events_refresh_cycle: u64,
    pub service_refresh_cycle: u64,
    pub quick_check_refresh_cycle: u64,
    pub software_meter_refresh_cycle: u64,
    pub max_file_download_speed: Option<usize>,
}

#[derive(Debu<PERSON>, <PERSON><PERSON><PERSON>, Deserialize_repr, Serialize_repr, Clone, Eq, PartialEq)]
#[repr(u8)]
pub enum AgentLogLevel {
    Trace = 0,
    Debug = 1,
    Warn = 2,
    #[default]
    Info = 3,
}

impl From<AgentLogLevel> for logger::Level {
    fn from(value: AgentLogLevel) -> Self {
        match value {
            AgentLogLevel::Trace => logger::Level::TRACE,
            AgentLogLevel::Debug => logger::Level::DEBUG,
            AgentLogLevel::Warn => logger::Level::WARN,
            AgentLogLevel::Info => logger::Level::INFO,
        }
    }
}

impl From<AgentLogLevel> for String {
    fn from(value: AgentLogLevel) -> Self {
        match value {
            AgentLogLevel::Trace => "trace",
            AgentLogLevel::Debug => "debug",
            AgentLogLevel::Warn => "warn",
            AgentLogLevel::Info => "info",
        }
        .to_owned()
    }
}

impl Display for AgentLogLevel {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            AgentLogLevel::Trace => f.write_str("trace"),
            AgentLogLevel::Debug => f.write_str("debug"),
            AgentLogLevel::Warn => f.write_str("warn"),
            AgentLogLevel::Info => f.write_str("info"),
        }
    }
}

#[derive(Debug, Default, Serialize, Deserialize, Clone, Eq, PartialEq)]
pub enum ServerProductType {
    EndpointOps,
    ZiroXpose,
    ZiroPatchPlus,
    #[default]
    ZiroPatch,
}

impl Display for ServerProductType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ServerProductType::EndpointOps => f.write_str("EndpointOps"),
            ServerProductType::ZiroXpose => f.write_str("ZiroXpose"),
            ServerProductType::ZiroPatchPlus => f.write_str("ZiroPatchPlus"),
            ServerProductType::ZiroPatch => f.write_str("ZiroPatch"),
        }
    }
}

impl ServerProductType {
    pub fn is_ziro_patch(&self) -> bool {
        matches!(self, ServerProductType::ZiroPatch)
    }

    pub fn is_ziro_patch_plus(&self) -> bool {
        matches!(self, ServerProductType::ZiroPatchPlus)
    }

    pub fn is_ziro_xpose(&self) -> bool {
        matches!(self, ServerProductType::ZiroXpose)
    }

    pub fn is_endpointops(&self) -> bool {
        matches!(self, ServerProductType::EndpointOps)
    }
}

#[derive(Debug, Default, Serialize, Deserialize, Clone)]
pub struct AgentConfig {
    #[serde(alias = "agent_settings")]
    pub agent_refresh_time_settings: AgentRefreshTimeSettings,
    #[serde(alias = "fim")]
    pub fim_config: Option<HashSet<FIMConfig>>,
    pub quick_checks: Option<HashSet<QuickCheck>>,
    #[serde(alias = "software_meter")]
    pub software_meter_cofigurations: Option<HashSet<SoftwareMeterRule>>,
    pub log_level: Option<AgentLogLevel>,
    pub product_type: Option<ServerProductType>,
}

#[derive(Serialize, Deserialize, Clone, Default)]
pub struct AgentMetadata {
    id: i64,
    config: Arc<RwLock<AgentConfig>>,
}

impl Debug for AgentMetadata {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("AgentMetadata")
            .field("id", &self.id)
            .field("config", &self.config)
            .finish()
    }
}

impl AgentMetadata {
    pub fn new(id: i64, config: AgentConfig) -> Self {
        Self {
            id,
            config: Arc::new(RwLock::new(config)),
        }
    }

    pub fn get_endpoint_id(&self) -> i64 {
        self.id.clone().into()
    }

    pub fn get_config(&self) -> AgentConfig {
        if let Ok(config) = self.config.read() {
            config.clone()
        } else {
            error!("Unable to aquire lock on the agent metadata config");
            AgentConfig::default()
        }
    }

    pub fn get_agent_refresh_settings(&self) -> AgentRefreshTimeSettings {
        if let Ok(config) = self.config.read() {
            config.clone().agent_refresh_time_settings
        } else {
            error!("Unable to aquire lock on the agent metadata config");
            AgentRefreshTimeSettings::default()
        }
    }

    pub fn update_config(&self, updated_config: AgentConfig) {
        debug!(
            "Agent configuration has been updated new config {:?}",
            updated_config
        );

        let config = self.config.write();

        if config.is_err() {
            error!(
                "Failed to aquire lock on agent metadata config {:?}",
                config.err().unwrap()
            );
            return;
        }

        if let Ok(mut config) = config {
            *config = updated_config;
        }

        debug!("Updated Agent metadata config");
    }
}
