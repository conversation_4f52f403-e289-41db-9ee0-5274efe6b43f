use database::models::FileAttachment;

pub trait HasCommands: Send + Sync + 'static {
    #[allow(dead_code)]
    fn get_package_names(&self) -> Option<Vec<String>> {
        None
    }

    #[allow(dead_code)]
    fn is_patch(&self) -> bool {
        false
    }

    #[allow(dead_code)]
    fn is_third_party_path(&self) -> bool {
        false
    }

    fn get_install_command(&self, attachment: &FileAttachment) -> Option<String>;

    fn get_upgrade_command(&self, attachment: &FileAttachment) -> Option<String>;

    fn get_uninstall_command(&self, attachment: &FileAttachment) -> Option<String>;

    #[allow(dead_code)]
    fn get_install_args(&self, attachment: &FileAttachment) -> Option<String>;

    #[allow(dead_code)]
    fn get_uninstall_args(&self, attachment: &FileAttachment) -> Option<String>;
}
