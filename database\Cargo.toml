[package]
name = "database"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
logger = { path = "../logger" }
utils = { path = "../utils" }
anyhow = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
thiserror = { workspace = true }
tokio = { workspace = true }
chrono = { workspace = true }
sysinfo = { workspace = true }
async-trait = { workspace = true }
surrealdb = { version = "2.3.10", features = ["kv-rocksdb"] }
serde_repr = "0.1.20"
serde_with = "3.14.0"
hex = "0.4.3"
sha1 = "0.10.6"
sha2 = "0.10.9"
md-5 = "0.10.6"
