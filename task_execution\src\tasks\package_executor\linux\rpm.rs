use std::time::Duration;

use crate::{
    has_commands::HasCommands,
    tasks::{attachment_downloader::AttachmentDownloader, command_executor::CommandExecutor},
    TaskExecutable,
};
use anyhow::{anyhow, Error};
use database::models::FileAttachment;
use logger::{debug, error};
use shell::ShellOutput;
use tokio::time::sleep;

pub struct Rpm<'a> {
    commands: Box<dyn HasCommands>,
    attachment: &'a FileAttachment,
    task: Box<&'a dyn TaskExecutable>,
}

impl<'a> Rpm<'a> {
    pub fn new(
        commands: Box<dyn HasCommands>,
        attachment: &'a FileAttachment,
        task: Box<&'a dyn TaskExecutable>,
    ) -> Self {
        Self {
            commands,
            attachment,
            task,
        }
    }

    async fn try_downloading_centos_dependency_file(
        &self,
        package: &str,
        max_try: u32,
    ) -> Result<(), Error> {
        let mut current_try = 1;
        self.task
            .write_task_log(format!("Downloading Dependency Package {}", package), None)
            .await;
        while current_try <= max_try {
            sleep(Duration::from_secs(60)).await;

            let attachment = FileAttachment {
                real_name: package.to_owned(),
                ref_name: package.to_owned(),
                zirozen_download_url: Some(format!("/patch/centos-patch/download/{}", package)),
                ..Default::default()
            };

            match AttachmentDownloader::new(attachment, self.task.clone(), None)
                .download()
                .await
            {
                Ok(_) => {
                    return Ok(());
                }
                Err(_) => {
                    current_try += 1;
                }
            }
        }
        return Err(anyhow!(
            "Failed to download dependency package {} after trying {} times",
            package,
            max_try
        ));
    }

    async fn handle_centos_patch_installation(&self) -> Result<(), Error> {
        debug!("Handling centos patch installation");
        let task_dir = self.task.get_task_dir();
        let output = CommandExecutor::new_command(
            r###"sudo yum install --assumeno --downloadonly \
  --downloaddir=__TASK_DIR__ \
  --disablerepo="*" \
  --enablerepo="*-ziro" \
        __RPM_PATH__ 2>/dev/null \
| awk '
/Upgrading|Installing/ {capture=1; next}
capture && NF {
    gsub(/^[0-9]+:/, "", $3);       # remove epoch like 1:
    print $1"-"$3"."$2".rpm"
}
/^$/ {capture=0}
'"###
                .replace("__TASK_DIR__", task_dir.display().to_string().as_str())
                .replace(
                    "__RPM_PATH__",
                    task_dir.join("*.rpm").display().to_string().as_str(),
                )
                .as_str(),
            self.task.clone(),
        )
        .execute()
        .await?;

        if output.failed() {
            self.task
                .write_task_log(
                    "Failed to get list of dependencies packages to install".to_owned(),
                    Some("ERROR"),
                )
                .await;
            return Err(anyhow!(
                "Failed to get list of dependencies packages to install"
            ));
        }

        let files_to_download = output
            .output
            .lines()
            .filter(|package| {
                package.is_empty() == false && task_dir.join(package).exists() == false
            })
            .collect::<Vec<&str>>();

        for file in files_to_download {
            self.try_downloading_centos_dependency_file(file, 5).await?;
        }

        Ok(())
    }

    pub async fn install(self) -> Result<ShellOutput, Error> {
        if self.commands.is_patch() && os_info::get().os_type() == os_info::Type::CentOS {
            self.handle_centos_patch_installation().await?;
        }

        let command = self.commands.get_install_command(self.attachment)
            .map(|i| i.to_owned())
            .unwrap_or(match os_info::get().os_type() {
              os_info::Type::OracleLinux | os_info::Type::CentOS | os_info::Type::RedHatEnterprise | os_info::Type::Redhat | os_info::Type::RockyLinux
 => format!("yum -y --disablerepo=* --setopt=protected_multilib=false --disableplugin=subscription-manager,search-disabled-repos install *.rpm --skip-broken"),
              os_info::Type::openSUSE => format!("zypper --no-refresh -n in {}", self.attachment.real_name),
              _ => "".to_owned()
          });

        Ok(CommandExecutor::new_command(&command, self.task)
            .execute()
            .await?)
    }

    pub async fn uninstall(self) -> Result<ShellOutput, Error> {
        let pkg_finder_command = r#"rpm -q --qf "%{NAME}\n\" -p __PACKAGE__"#
            .replace("__PACKAGE__", &self.attachment.real_name);
        let pkg_finder_result =
            CommandExecutor::new_command(&pkg_finder_command, self.task.clone())
                .capture()
                .execute()
                .await;

        if let Err(error) = pkg_finder_result {
            error!(?error, "Failed to find package id for deb package");
            Err(anyhow!("Failed to find package id for deb package"))
        } else {
            let package_id = pkg_finder_result.unwrap().output;

            debug!("Got package id output: {}", package_id);

            let command = self
                .commands
                .get_uninstall_command(self.attachment)
                .map(|i| i.to_owned())
                .unwrap_or(format!("rpm -ev {}", package_id.trim()));

            Ok(CommandExecutor::new_command(&command, self.task)
                .execute()
                .await?)
        }
    }

    pub async fn upgrade(self) -> Result<ShellOutput, Error> {
        let command = self.commands.get_upgrade_command(self.attachment)
          .map(|i| i.to_owned())
          .unwrap_or(match os_info::get().os_type() {
            os_info::Type::OracleLinux | os_info::Type::CentOS | os_info::Type::RedHatEnterprise | os_info::Type::Redhat | os_info::Type::RockyLinux => format!("yum -y --disablerepo=* --setopt=protected_multilib=false --disableplugin=subscription-manager,search-disabled-repos install {}", self.attachment.real_name),
            os_info::Type::openSUSE => format!("zypper --no-refresh -n in {}", self.attachment.real_name),
            _ => "".to_owned()
        });

        Ok(CommandExecutor::new_command(&command, self.task)
            .execute()
            .await?)
    }
}
