use crate::constants::{ENDPOINTOPS_BINARY_NAME, SELF_SERVICE_APP_NAME};
use std::{io, path::PathBuf};

#[cfg(windows)]
mod windows;

#[cfg(windows)]
pub mod platform {
    pub use super::windows::*;
}

#[cfg(target_os = "macos")]
mod mac;

#[cfg(target_os = "macos")]
pub mod platform {
    pub use super::mac::*;
}

#[cfg(target_os = "linux")]
mod linux;

#[cfg(target_os = "linux")]
pub mod platform {
    pub use super::linux::*;
}

const ICON: &[u8] = include_bytes!("../../../self_service/icons/<EMAIL>");

pub fn register_gui_autostart_all_users(install_path: &str) -> io::Result<()> {
    let app_name = SELF_SERVICE_APP_NAME;
    let exe_path = PathBuf::from(install_path).join(ENDPOINTOPS_BINARY_NAME);

    std::fs::write(PathBuf::from(&install_path).join("icon.png"), ICON).ok();

    platform::register(app_name, &exe_path)
}

pub fn start_now(install_path: &str) -> io::Result<()> {
    let app_name = SELF_SERVICE_APP_NAME;
    let exe_path = PathBuf::from(install_path).join(ENDPOINTOPS_BINARY_NAME);
    platform::start_now(app_name, &exe_path)
}

pub fn deregister_gui_autostart_all_users() -> io::Result<()> {
    let app_name = SELF_SERVICE_APP_NAME;
    platform::deregister(app_name)
}
