#[macro_use]
extern crate cfg_if;

pub mod agent;
mod client;
pub mod data_collection;
pub mod file;
pub mod patch;
mod prelude;
pub mod self_service;
pub mod task;

use anyhow::Result;
use client::ApiClient;
use logger::{debug, error};
pub use prelude::*;
use std::sync::OnceLock;

pub use client::ApiResponseWithCount;

pub(crate) static API_CLIENT: OnceLock<ApiClient> = OnceLock::new();

pub(crate) fn get_client<'a>() -> Result<&'a ApiClient, ApiError> {
    match API_CLIENT.get() {
        Some(client) => Ok(client),
        None => Err(ApiError::Uninitialised),
    }
}

pub fn init(options: ApiOptions) -> Result<(), ApiError> {
    let client = ApiClient::new(options)?;

    if API_CLIENT.get().is_none() {
        match API_CLIENT.set(client) {
            Ok(_) => debug!("Set API Client successfully"),
            Err(_) => {
                error!("Failed to set API Client");
                return Err(ApiError::NotSetOnce);
            }
        };
    } else {
        error!("API_CLIENT is setting again");
    }

    Ok(())
}
