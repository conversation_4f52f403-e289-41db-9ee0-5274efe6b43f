use std::sync::{
    atomic::{AtomicBool, Ordering},
    Arc,
};

pub(crate) struct ThreadLoopHandler {
    is_running: Arc<AtomicBool>,
}

impl ThreadLoopHandler {
    pub(crate) fn new() -> Self {
        ThreadLoopHandler {
            is_running: Arc::new(AtomicBool::new(false)),
        }
    }

    pub(crate) fn start(&self) {
        self.is_running.store(true, Ordering::SeqCst);
    }

    pub(crate) fn stop(&self) {
        self.is_running.store(false, Ordering::SeqCst);
    }

    pub(crate) fn should_continue(&self) -> bool {
        self.is_running.load(Ordering::Relaxed)
    }
}
