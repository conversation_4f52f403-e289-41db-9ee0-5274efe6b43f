import { Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { ConfigProvider, Spin } from 'antd';
import AvailableModules from './available-modules';
import tokens, { components } from './design/theme';
import AppLayout from './layout/Layout';
import NotFound from './components/NotFound';
import Credentials from './components/Credentials';
import Unapproved from './components/Unapproved';
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';
// import { WebviewWindow } from '@tauri-apps/api/window';
import { createContext, useContext, useEffect, useState } from 'react';

const AppContext = createContext();

export const standalonePaths = ['/credentials'];

function App() {
  const [appState, setAppState] = useState(null);

  const { pathname } = useLocation();

  function getLastestState() {
    invoke('get_current_state').then((state) => {
      setAppState(state);
    });
  }

  useEffect(() => {
    getLastestState();
    listen('provisioned', () => {
      if (appState?.is_agent_approved) {
        return;
      }
      getLastestState();
    });
  }, []);

  return (
    <AppContext.Provider
      value={{
        state: appState,
        reloadState: getLastestState
      }}>
      <ConfigProvider
        componentSize={'small'}
        theme={{
          token: tokens,
          components
        }}>
        {appState == null ? (
          <div className="h-screen flex justify-center items-center">
            <Spin />
          </div>
        ) : (
          <Routes>
            <Route path="/" element={<AppLayout />}>
              <Route
                path=""
                element={
                  <Navigate to={appState.is_agent_approved ? '/patch' : '/unapproved'} replace />
                }
              />
              <Route path="credentials" element={<Credentials />} />
              <Route path="unapproved" element={<Unapproved />} />
              {AvailableModules.map((moduleDef) => moduleDef.router)}
              <Route path="404" element={<NotFound />} />
              <Route path="*" element={<NotFound />} />
            </Route>
          </Routes>
        )}
      </ConfigProvider>
    </AppContext.Provider>
  );
}

export default App;

export function useApp() {
  return useContext(AppContext);
}
