use crate::{tasks::command_executor::CommandExecutor, TaskExecutable};
use anyhow::Result;
use database::{
    data_types::TaskResult,
    models::{CommandType, Configuration, ConfigurationType, TaskStatus},
};

pub struct ConfigurationExecutor<'a> {
    task: &'a dyn TaskExecutable,
    configuration: &'a Configuration,
}

impl<'a> ConfigurationExecutor<'a> {
    pub fn new(task: &'a dyn TaskExecutable, configuration: &'a Configuration) -> Self {
        Self {
            task,
            configuration,
        }
    }

    pub async fn execute(&self) -> Result<TaskResult> {
        let mut task_result = TaskResult::default();
        let configuration = self.configuration;

        let default_string = "".to_owned();

        // execute each action
        for action in &configuration.configuration_actions {
            let command_executor = if configuration.configuration_type == ConfigurationType::Script
            {
                CommandExecutor::new_script_attachment(
                    action.script_file.as_ref().unwrap(),
                    Box::new(self.task),
                )
            } else {
                let cmd = action.command.as_ref().unwrap_or_else(|| &default_string);
                if action
                    .command_type
                    .as_ref()
                    .is_some_and(|x| x == &CommandType::Powershell)
                {
                    CommandExecutor::new_powershell(cmd, Box::new(self.task))
                } else {
                    CommandExecutor::new_command(cmd, Box::new(self.task))
                }
            };

            let result = command_executor.execute().await?;

            let is_failed = result.failed();

            task_result.exit_code = result.exit_code;
            task_result.output = result.output;
            task_result.status = if is_failed {
                TaskStatus::Failed
            } else {
                TaskStatus::Success
            };

            if is_failed {
                break;
            }
        }

        Ok(task_result)
    }
}
