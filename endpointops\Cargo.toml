[package]
name = "EndpointOps"
description = "An Entry point for the EndpointOps Agent"
default-run = "endpointops"
edition.workspace = true
version.workspace = true
authors.workspace = true

[[bin]]
name = "endpointops"
path = "src/main.rs"

[features]
default = []
self_service = [
  "dep:tauri",
  "dep:self_service",
  "utils/self_service",
  "task_execution/self_service",
  "agents/self_service",
]

[dependencies]
database = { path = "../database" }
logger = { path = "../logger" }
agents = { path = "../agents" }
api = { path = "../api" }
utils = { path = "../utils" }
ipc = { path = "../ipc" }
agent_manager = { path = "../agent_manager" }
task_execution = { path = "../task_execution" }
data_collection = { path = "../data_collection" }
self_service = { path = "../self_service", optional = true }
tokio = { workspace = true }
thiserror = { workspace = true }
anyhow = { workspace = true }
rayon = { workspace = true }
config = { version = "0.15.13", features = ["ini"] }
backtrace = "0.3.75"
figlet-rs = "0.1.5"
console = "0.16.0"
clap = { version = "4.5.41", features = ["derive"] }
textwrap = "0.16.2"
crossterm = "0.29.0"
self-replace = "=1.5.0"
cargo-util = "0.2.21"
wake-on-lan = "0.2.0"
tauri = { version = "2", features = [
  "macos-private-api",
  "protocol-asset",
  "tray-icon",
], optional = true }

[build-dependencies]
build-target = "0.7.0"

[target.'cfg(windows)'.build-dependencies]
embed-resource = "3.0.5"

[target.'cfg(windows)'.dependencies]
windows-service = "0.8.0"
winreg = "0.55.0"
windows = { version = "0.61.3", features = ["Win32_System_Console"] }
