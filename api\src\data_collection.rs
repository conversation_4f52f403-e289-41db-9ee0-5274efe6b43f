use crate::{get_client, ApiError};
use serde_json::Value;

pub async fn send_system_data(data: Value) -> Result<(), ApiError> {
    get_client()?
        .post::<Value, _>("/zirozen/logger".to_owned(), data)
        .await?;

    Ok(())
}

pub async fn send_fim_data(data: Value) -> Result<(), ApiError> {
    get_client()?
        .post::<Value, _>("/zirozen/events/logger".to_owned(), data)
        .await?;

    Ok(())
}

pub async fn send_quick_check_data(data: Value) -> Result<(), ApiError> {
    get_client()?
        .post::<Value, _>("/zirozen/quick/checks/logger".to_owned(), data)
        .await?;

    Ok(())
}
