use globset::{Glob, GlobSet};
use logger::{debug, error};
use std::{
    fmt::Debug,
    path::{Path, PathBuf},
};

fn glob_root(pattern: &str) -> PathBuf {
    let path = Path::new(pattern);

    // Find the first component that contains a glob character
    let mut root = PathBuf::new();
    for component in path.components() {
        let comp_str = component.as_os_str().to_string_lossy();
        if comp_str.contains('*')
            || comp_str.contains('?')
            || comp_str.contains('[')
            || comp_str.contains('{')
        {
            break; // Stop at the first wildcard
        }
        root.push(component);
    }

    // Default to current directory if no root found
    if root.as_os_str().is_empty() {
        PathBuf::from("")
    } else {
        root
    }
}

pub struct GlobPaths {
    paths: Vec<PathBuf>,
    matcher: GlobSet,
}

impl Debug for GlobPaths {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("GlobPaths")
            .field("paths", &self.paths)
            .finish()
    }
}

impl GlobPaths {
    pub fn new(paths: Vec<PathBuf>) -> Self {
        let mut globset_builder = GlobSet::builder();

        debug!("Creating Glob path for paths {:?}", paths);

        for path in paths.iter() {
            match Glob::new(path.to_str().unwrap_or_default()) {
                Ok(glob) => {
                    globset_builder.add(glob);
                }
                Err(error) => {
                    error!(
                        ?error,
                        "Failed to create glob pattern for path {}",
                        path.display()
                    );
                }
            }
        }

        Self {
            paths,
            matcher: match globset_builder.build() {
                Ok(globset) => globset,
                Err(error) => {
                    error!(?error, "Failed to build globset for paths");
                    GlobSet::default()
                }
            },
        }
    }

    pub fn get_root_paths(&self) -> Vec<PathBuf> {
        self.paths
            .iter()
            .map(|item| glob_root(item.to_str().unwrap()))
            .filter(|p| p.to_string_lossy().is_empty() == false)
            .collect()
    }

    pub fn matches<P: AsRef<Path>>(&self, path: P) -> bool {
        self.matcher.is_match(path)
    }

    pub fn is_empty(&self) -> bool {
        self.matcher.is_empty()
    }
}
