{"name": "self_service", "private": true, "version": "5.0.11", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@ant-design/icons": "^6.0.2", "@tauri-apps/api": "^2", "@tauri-apps/plugin-dialog": "^2.4.0", "@tauri-apps/plugin-fs": "^2.4.2", "@tauri-apps/plugin-opener": "^2.5.0", "@tauri-apps/plugin-os": "^2.3.1", "antd": "^5.21.6", "lodash": "^4.17.21", "moment": "^2.30.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-loading": "^2.0.3", "react-resizable": "^3.0.5", "react-router-dom": "^6.6.1", "react-sortablejs": "^6.1.4", "tailwindcss": "^3.2.4", "moment-timezone": "^0.5.40", "jspdf": "^2.5.1", "jspdf-autotable": "^3.7.1", "mime-types": "^3.0.1"}, "devDependencies": {"@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "less": "^4.4.1", "postcss": "^8.5.6", "vite": "^7.0.4", "vite-plugin-svgr": "^4.5.0"}}