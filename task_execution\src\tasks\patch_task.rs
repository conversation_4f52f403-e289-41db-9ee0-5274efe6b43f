use super::attachment_downloader::AttachmentDownloader;
use crate::{
    has_commands::HasCommands, has_task::HasTask, log_task::LogTask, sync_task::SyncTask,
    tasks::configuration_executor::ConfigurationExecutor, TaskExecutable, TaskExecutionError,
};
use anyhow::{anyhow, Error, Result};
use async_trait::async_trait;
use database::{
    data_types::TaskResult,
    models::{DeploymentScript, DeploymentType, FileAttachment, Patch, Task, TaskStatus},
};
use logger::{debug, error, info, ModuleLogger};
#[cfg(windows)]
use serde::Deserialize;
use shell::ShellOutput;
use std::sync::Arc;

#[cfg(windows)]
#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct HardwareReadinessResult {
    return_code: usize,
    return_reason: String,
    logging: String,
    return_result: String,
}

#[derive(Debug)]
pub struct PatchTask {
    task: Task,
    logger: Arc<ModuleLogger>,
}

impl HasCommands for Patch {
    fn get_package_names(&self) -> Option<Vec<String>> {
        self.package_names.clone()
    }

    fn is_patch(&self) -> bool {
        true
    }

    fn is_third_party_path(&self) -> bool {
        self.is_third_party.is_some_and(|value| value)
    }

    fn get_install_command(&self, _attachment: &FileAttachment) -> Option<String> {
        None
    }

    fn get_upgrade_command(&self, _attachment: &FileAttachment) -> Option<String> {
        None
    }

    fn get_uninstall_command(&self, _attachment: &FileAttachment) -> Option<String> {
        None
    }

    fn get_install_args(&self, _attachment: &FileAttachment) -> Option<String> {
        if let Some(args) = self.install_command.as_ref() {
            if args.is_empty() {
                None
            } else {
                Some(args.to_owned())
            }
        } else {
            None
        }
    }

    fn get_uninstall_args(&self, _attachment: &FileAttachment) -> Option<String> {
        if let Some(args) = self.uninstall_command.as_ref() {
            if args.is_empty() {
                None
            } else {
                Some(args.to_owned())
            }
        } else {
            None
        }
    }
}

impl PatchTask {
    pub fn new(task: Task) -> Self {
        Self {
            task,
            logger: ModuleLogger::new("patch", None, Some("patch".to_owned())),
        }
    }

    #[cfg(target_os = "macos")]
    async fn process_dmg(
        &self,
        attachment: FileAttachment,
        operation_type: DeploymentType,
    ) -> Result<ShellOutput, Error> {
        use crate::tasks::package_executor::Dmg;

        let patch = self.task.patch.as_ref().unwrap();

        let dmg = Dmg::new(
            Box::new(patch.clone()) as Box<dyn HasCommands>,
            &attachment,
            Box::new(self),
        );

        match operation_type {
            DeploymentType::Install => dmg.install().await,
            DeploymentType::Upgrade => dmg.upgrade().await,
            DeploymentType::Uninstall => dmg.uninstall().await,
        }
    }

    #[cfg(target_os = "macos")]
    async fn process_pkg(
        &self,
        attachment: FileAttachment,
        operation_type: DeploymentType,
    ) -> Result<ShellOutput, Error> {
        use crate::tasks::package_executor::Pkg;

        let patch = self.task.patch.as_ref().unwrap();

        let pkg = Pkg::new(
            Box::new(patch.clone()) as Box<dyn HasCommands>,
            &attachment,
            Box::new(self),
        );

        match operation_type {
            DeploymentType::Install => pkg.install().await,
            DeploymentType::Upgrade => pkg.upgrade().await,
            DeploymentType::Uninstall => pkg.uninstall().await,
        }
    }

    #[cfg(target_os = "linux")]
    async fn process_deb(
        &self,
        attachment: FileAttachment,
        operation_type: DeploymentType,
    ) -> Result<ShellOutput, Error> {
        use crate::tasks::package_executor::Deb;

        let patch = self.task.patch.as_ref().unwrap();

        let deb = Deb::new(
            Box::new(patch.clone()) as Box<dyn HasCommands>,
            &attachment,
            Box::new(self),
        );

        match operation_type {
            DeploymentType::Install => deb.install().await,
            DeploymentType::Upgrade => deb.upgrade().await,
            DeploymentType::Uninstall => deb.uninstall().await,
        }
    }

    #[cfg(target_os = "linux")]
    async fn process_rpm(
        &self,
        attachment: FileAttachment,
        operation_type: DeploymentType,
    ) -> Result<ShellOutput, Error> {
        use crate::tasks::package_executor::Rpm;

        let patch = self.task.patch.as_ref().unwrap();

        let rpm = Rpm::new(
            Box::new(patch.clone()) as Box<dyn HasCommands>,
            &attachment,
            Box::new(self),
        );

        match operation_type {
            DeploymentType::Install => rpm.install().await,
            DeploymentType::Upgrade => rpm.upgrade().await,
            DeploymentType::Uninstall => rpm.uninstall().await,
        }
    }

    #[cfg(windows)]
    async fn process_windows(
        &self,
        attachment: FileAttachment,
        operation_type: DeploymentType,
    ) -> Result<ShellOutput, Error> {
        use crate::tasks::package_executor::Iso;
        use crate::tasks::package_executor::Msi;

        let patch = self.task.patch.as_ref().unwrap();

        if attachment.extension() == "iso" {
            let iso = Iso::new(&attachment, Box::new(self));

            match operation_type {
                DeploymentType::Install => iso.install().await,
                DeploymentType::Upgrade => iso.upgrade().await,
                DeploymentType::Uninstall => iso.uninstall().await,
            }
        } else {
            let msi = Msi::new(
                Box::new(patch.clone()) as Box<dyn HasCommands>,
                &attachment,
                Box::new(self),
            );

            match operation_type {
                DeploymentType::Install => msi.install().await,
                DeploymentType::Upgrade => msi.upgrade().await,
                DeploymentType::Uninstall => msi.uninstall().await,
            }
        }
    }

    #[cfg(target_os = "linux")]
    fn parse_rpm_filename(filename: &str) -> Option<(String, String, String, String)> {
        let re = regex::Regex::new(
            r"^(?P<name>.+)-(?P<version>[^-]+)-(?P<release>[^-]+)\.(?P<arch>[^.]+)\.rpm$",
        )
        .unwrap();

        if let Some(captures) = re.captures(filename) {
            let name = captures.name("name").unwrap().as_str().to_string();
            let version = captures.name("version").unwrap().as_str().to_string();
            let release = captures.name("release").unwrap().as_str().to_string();
            let arch = captures.name("arch").unwrap().as_str().to_string();
            Some((name, version, release, arch))
        } else {
            None
        }
    }

    #[cfg(target_os = "linux")]
    fn parse_deb_filename(filename: &str) -> Option<(String, String, String)> {
        let re = regex::Regex::new(r"^(?P<name>[^_]+)_(?P<version>[^_]+)_(?P<arch>[^_]+)\.deb$")
            .unwrap();

        if let Some(captures) = re.captures(filename) {
            let name = captures.name("name").unwrap().as_str().to_string();
            let version = captures.name("version").unwrap().as_str().to_string();
            let arch = captures.name("arch").unwrap().as_str().to_string();
            Some((name, version, arch))
        } else {
            None
        }
    }

    #[cfg(target_os = "linux")]
    async fn get_installed_rpm_version(&self, name: &str) -> Result<(String, String)> {
        use crate::tasks::command_executor::CommandExecutor;

        let installed_version_output = CommandExecutor::new_command(
            format!("rpm -qi {} | grep Version", name).as_str(),
            Box::new(self),
        )
        .skip_task_log()
        .capture()
        .execute()
        .await?;

        let installed_version = installed_version_output
            .output
            .split(":")
            .nth(1)
            .unwrap_or_default()
            .trim();

        let installed_release_output = CommandExecutor::new_command(
            format!("rpm -qi {} | grep Release", name).as_str(),
            Box::new(self),
        )
        .skip_task_log()
        .capture()
        .execute()
        .await?;

        let installed_release = installed_release_output
            .output
            .split(":")
            .nth(1)
            .unwrap_or_default()
            .trim();

        Ok((installed_version.to_string(), installed_release.to_string()))
    }

    #[cfg(target_os = "linux")]
    async fn get_installed_deb_version(&self, name: &str) -> Result<String> {
        use crate::tasks::command_executor::CommandExecutor;

        let installed_version_output = CommandExecutor::new_command(
            r#"dpkg-query -W -f='${Version}' __NAME__"#.replace("__NAME__", name).as_str(),
            Box::new(self),
        )
        .skip_task_log()
        .capture()
        .execute()
        .await?;

        let installed_version = installed_version_output.output.trim();

        Ok(installed_version.to_string())
    }

    #[cfg(target_os = "linux")]
    async fn is_rpm_compatible(&self, name: &str, version: &str, release: &str) -> Result<bool> {
        use crate::tasks::command_executor::CommandExecutor;

        let mut is_valid = false;
        let result =
            CommandExecutor::new_command(format!("rpm -q {}", name).as_str(), Box::new(self))
                .skip_task_log()
                .capture()
                .execute()
                .await?;
        if result
            .output
            .contains(format!("package {} is not installed", name).as_str())
            == false
        {
            let installed_rpm_version = self.get_installed_rpm_version(name).await?;

            let installed_version =
                format!("{}-{}", installed_rpm_version.0, installed_rpm_version.1);

            let incoming_version = format!("{}-{}", version, release);

            match version_compare::compare(&installed_version, &incoming_version) {
                Ok(version_compare::Cmp::Lt) => {
                    let log = format!(
                        "{} Package Installed version {} is less than incoming version {} so downloading file",
                        name, installed_version, incoming_version
                    );
                    info!(log);
                    self.write_task_log(log, None).await;
                    is_valid = true;
                }
                Ok(_) => {
                    let log = format!(
                        "{} Installed version {} is greater than or equal to incoming version {} so skipping file download",
                        name, installed_version, incoming_version
                    );
                    info!(log);
                    self.write_task_log(log, None).await;
                    is_valid = false;
                }
                Err(_) => {
                    let log = format!(
                        "{} Failed to compare installed version {} with incoming version {}",
                        name, installed_version, incoming_version
                    );
                    error!(log);
                    self.write_task_log(log, Some("ERROR")).await;
                    is_valid = true;
                }
            };
        } else if result
            .output
            .contains(format!("package {} is not installed", name).as_str())
        {
            let log = format!("Package {} is not installed so skipping file", name);
            info!(log);
            self.write_task_log(log, None).await;
            is_valid = false;
        }
        Ok(is_valid)
    }

    #[cfg(target_os = "linux")]
    async fn is_deb_compatible(&self, name: &str, version: &str) -> Result<bool> {
        let is_valid;
        let installed_version = self.get_installed_deb_version(name).await?;
        match deb_version::compare_versions(&installed_version, version) {
            std::cmp::Ordering::Less => {
                let log = format!(
                    "{} Package Installed version {} is less than incoming version {} so downloading file",
                    name, installed_version, version
                );
                info!(log);
                self.write_task_log(log, None).await;
                is_valid = true;
            }
            _ => {
                let log = format!(
                    "{} Installed version {} is greater than or equal to incoming version {} so skipping file download",
                    name, installed_version, version
                );
                info!(log);
                self.write_task_log(log, None).await;
                is_valid = false;
            }
        }
        Ok(is_valid)
    }

    async fn download_file(
        &self,
        mut file: FileAttachment,
    ) -> Result<Option<FileAttachment>, TaskExecutionError> {
        // validate rpm/deb before downloading only if required
        #[cfg(target_os = "linux")]
        {
            if file.extension() == "rpm" {
                match Self::parse_rpm_filename(&file.real_name) {
                    Some((name, version, release, _)) => {
                        match self.is_rpm_compatible(&name, &version, &release).await {
                            Ok(false) => {
                                return Ok(None);
                            }
                            _ => {}
                        }
                    }
                    None => {}
                };
            } else if file.extension() == "deb" {
                match Self::parse_deb_filename(&file.real_name) {
                    Some((name, version, _arch)) => {
                        match self
                            .is_deb_compatible(&name, file.version.as_ref().unwrap_or(&version))
                            .await
                        {
                            Ok(false) => {
                                return Ok(None);
                            }
                            _ => {}
                        }
                    }
                    None => {}
                };
            }
        }

        if file.ref_name.len() > 0 {
            file.zirozen_download_url = Some(format!("/patch/download/patch/{}", file.ref_name));
        }
        let attachment = AttachmentDownloader::new(file, Box::new(self), None)
            .download()
            .await?;

        Ok(Some(attachment))
    }

    async fn process_file(
        &self,
        attachment: FileAttachment,
        operation_type: DeploymentType,
    ) -> Result<TaskResult> {
        let mut task_result = TaskResult::default();
        let patch = self.task.patch.as_ref().unwrap();
        let is_uninstallation = operation_type == DeploymentType::Uninstall;

        self.write_task_log(
            format!(
                "Starting to {} {}",
                operation_type,
                patch.name.as_ref().unwrap_or(&"Unknow Patch".to_owned())
            ),
            None,
        )
        .await;

        let extension = attachment.extension().to_owned();

        let output = match extension.as_str() {
            #[cfg(target_os = "macos")]
            "dmg" => self.process_dmg(attachment, operation_type).await,
            #[cfg(target_os = "macos")]
            "pkg" => self.process_pkg(attachment, operation_type).await,
            #[cfg(target_os = "linux")]
            "deb" => self.process_deb(attachment, operation_type).await,
            #[cfg(target_os = "linux")]
            "rpm" => self.process_rpm(attachment, operation_type).await,
            #[cfg(target_os = "windows")]
            "msu" | "cab" | "mst" | "msi" | "msp" | "exe" | "iso" => {
                self.process_windows(attachment, operation_type).await
            }
            _ => {
                return Err(anyhow!(TaskExecutionError::UnsupportedFileExtension(
                    extension.to_owned()
                )))
            }
        }?;

        // if msu file then consider non zero code as well
        if extension.as_str() == "msu" || extension.as_str() == "iso" || is_uninstallation {
            if output.is_windows_restart_code() {
                self.write_task_log(
                    format!(
                        "{} file execution exit code is {} and is considered as succeed",
                        extension.to_uppercase(),
                        output.exit_code
                    ),
                    None,
                )
                .await;
                info!(
                    "{} file execution exit code is {} and is considered as succeed",
                    extension.to_uppercase(),
                    output.exit_code
                );
                if output.exit_code == 3010 {
                    task_result.status = TaskStatus::SuccessWithRebootRequired;
                    task_result.exit_code = output.exit_code;
                } else {
                    task_result.status = TaskStatus::Success;
                    task_result.exit_code = output.exit_code;
                }
            } else {
                task_result.exit_code = output.exit_code;
                task_result.status = TaskStatus::Failed
            }
            return Ok(task_result);
        }

        if output.failed() {
            task_result.exit_code = output.exit_code;
            task_result.status = TaskStatus::Failed
        } else {
            task_result.exit_code = output.exit_code;
            task_result.status = TaskStatus::Success;
        }

        Ok(task_result)
    }

    #[cfg(windows)]
    async fn validate_hardware_requirements_for_iso(&self) -> Result<HardwareReadinessResult> {
        use super::command_executor::CommandExecutor;
        use logger::debug;
        use tokio::fs;

        let file_content = include_bytes!("./HardwareReadiness.ps1");
        let script_path = self.get_task_dir().join("HardwareReadiness.ps1");
        match fs::write(&script_path, file_content).await {
            Ok(_) => {
                debug!("HardwareReadiness script has been written successfully.");
                self.write_task_log("Checking Hardware Requirements".to_owned(), None)
                    .await;
            }
            Err(error) => {
                error!(?error, "Failed to write HardwareReadiness script");
                self.write_task_log(
                    format!("Failed to write HardwareReadiness script {:?}", error),
                    Some("ERROR"),
                )
                .await;
                return Err(error.into());
            }
        };

        let output = match CommandExecutor::new_script(script_path, Box::new(self))
            .capture()
            .execute()
            .await
        {
            Ok(output) => output,
            Err(error) => {
                error!(?error, "Failed to execute hardware readiness check scirpt");
                self.write_task_log(
                    format!("Failed to execute HardwareReadiness script {:?}", error),
                    Some("ERROR"),
                )
                .await;
                return Err(error.into());
            }
        };
        self.write_task_log(format!("Hardware Check result {}", output.output), None)
            .await;

        let result: HardwareReadinessResult = match serde_json::from_str(&output.output) {
            Ok(result) => result,
            Err(error) => {
                error!(?error, "Failed to parse Hardware Check Result");
                self.write_task_log(
                    format!("Failed to parse HardwareReadiness result {:?}", error),
                    Some("ERROR"),
                )
                .await;
                return Err(error.into());
            }
        };

        Ok(result)
    }

    async fn execute_scripts(&self, scripts: &Vec<DeploymentScript>) -> Result<Option<TaskResult>> {
        for script in scripts {
            let script_name = script
                .configuration
                .display_name
                .as_ref()
                .map_or("Unknown Configuration".to_owned(), |v| v.to_owned());

            self.write_task_log(
                format!("===== Executing deployment script {} =====", script_name),
                None,
            )
            .await;

            let task_result = ConfigurationExecutor::new(self, &script.configuration)
                .execute()
                .await?;
            if task_result.status == TaskStatus::Failed {
                self.write_task_log(
                    format!(
                        "Executed deployment script {} with status {}",
                        script_name, task_result.status
                    ),
                    None,
                )
                .await;
                if script.process_on_failed {
                    continue;
                } else {
                    return Ok(Some(task_result));
                }
            }
        }

        Ok(None)
    }
}

impl HasTask for PatchTask {
    fn get_task(&self) -> &Task {
        &self.task
    }

    fn set_task(&mut self, task: Task) {
        self.task = task;
    }
}

impl LogTask for PatchTask {
    fn logger(&self) -> Arc<ModuleLogger> {
        Arc::clone(&self.logger)
    }
}

impl SyncTask for PatchTask {}

#[async_trait]
impl TaskExecutable for PatchTask {
    async fn execute(&mut self) -> Result<TaskResult> {
        self.write_task_log(
            format!("Initiating execution of Package {}", self.get_name()),
            None,
        )
        .await;

        let mut task_result = TaskResult::default();

        // if no package found return error
        if self.task.patch.is_none() {
            error!("No patch found for {:?}", self.task);
            self.write_task_log("No patch found".to_owned(), Some("ERROR"))
                .await;
            task_result.status = TaskStatus::Failed;
            task_result.output = "".to_owned();
            task_result.exit_code = 99;
            return Ok(task_result);
        }

        // if no deployment found return error
        if self.task.deployment.is_none() {
            error!("No deployment found for {:?}", self.task);
            self.write_task_log("No deployment found".to_owned(), Some("ERROR"))
                .await;
            task_result.status = TaskStatus::Failed;
            task_result.output = "".to_owned();
            task_result.exit_code = 99;
            return Ok(task_result);
        }

        let patch = self.task.patch.as_ref().unwrap();

        let deployment = self.task.deployment.as_ref().unwrap();

        self.write_task_log(
            format!(
                "{} of {} has started processing",
                deployment.deployment_type,
                patch
                    .display_name
                    .as_ref()
                    .unwrap_or(&"Unknown Patch".to_owned())
            ),
            None,
        )
        .await;

        // first execute pre deployment script if given
        if self.task.pre_deployment_scripts.is_some() {
            self.write_task_log(
                format!("================ Executing pre deployment scripts ==================="),
                None,
            )
            .await;
            if let Some(task_result) = self
                .execute_scripts(self.task.pre_deployment_scripts.as_ref().unwrap())
                .await?
            {
                return Ok(task_result);
            }
        }

        let download_files = patch.download_files.clone().unwrap_or_default();

        let files_to_process = {
            cfg_if! {
                if #[cfg(windows)] {
                    // if upgrades then select only iso
                    if patch.patch_update_category.as_ref().is_some_and(|value| value.to_lowercase() == "upgrades") {
                        let files = download_files
                            .iter()
                            .filter(|item| item.extension() == "iso")
                            .collect::<Vec<&FileAttachment>>();

                        if files.len() > 1 {
                            self.write_task_log("Unable to determine file to process".to_owned(), Some("ERROR")).await;
                            task_result.status = TaskStatus::Failed;
                            task_result.exit_code = 99;
                            return Ok(task_result);
                        }
                        files
                    } else if patch.is_third_party.is_some_and(|value| value) {
                        download_files.iter().collect()
                    } else {
                        let patch_only_executable_file = download_files
                            .iter()
                            .filter(|item| item.patch_only_file_to_install.is_some_and(|v| v))
                            .collect::<Vec<&FileAttachment>>();

                        if patch_only_executable_file.len() > 0 {
                            patch_only_executable_file
                        } else {
                            let msu_files = download_files
                                .iter()
                                .filter(|item| item.extension() == "msu")
                                .collect::<Vec<&FileAttachment>>();
                            if msu_files.len() > 0 {
                                msu_files
                            } else {
                                let cabs = download_files
                                    .iter()
                                    .filter(|item| item.extension() == "cab")
                                    .collect::<Vec<&FileAttachment>>();

                                if cabs.len() > 0 {
                                    cabs
                                } else {
                                    download_files.iter().collect::<Vec<&FileAttachment>>()
                                }
                            }
                        }
                    }
                } else {
                    if patch.product_type == Some("macOsVersionUpdate".to_owned()) {
                        download_files
                            .iter()
                            .filter(|item| item.real_name == "InstallAssistant.pkg".to_owned())
                            .collect::<Vec<&FileAttachment>>()
                    } else {
                        download_files.iter().collect()
                    }
                }
            }
        }
        .iter()
        .map(|i| i.to_owned())
        .filter(|item| {
            item.url.as_ref().is_some_and(|i| !i.is_empty()) || item.public_url.as_ref().is_some_and(|i| !i.is_empty()) || !item.ref_name.is_empty()
        })
        .collect::<Vec<&FileAttachment>>();

        let mut task_result = TaskResult::default();

        let mut downloaded_attachments = vec![];

        // download file only if deployment type is install else copy everything to downloaded_attachments
        if deployment.deployment_type == DeploymentType::Install
            || patch.is_third_party.is_some_and(|value| value)
            || deployment.deployment_type == DeploymentType::Upgrade
        {
            for attachment in files_to_process {
                if attachment.extension() == "iso" {
                    #[cfg(windows)]
                    {
                        let hardware_check_result = match self
                            .validate_hardware_requirements_for_iso()
                            .await
                        {
                            Ok(result) => result,
                            Err(error) => {
                                error!(?error, "Error in receiving hardware requirement result");
                                task_result.status = TaskStatus::Failed;
                                task_result.exit_code = 199;
                                return Ok(task_result);
                            }
                        };
                        if hardware_check_result.return_code != 0
                            && hardware_check_result.return_result.to_lowercase() == "capable"
                        {
                            self.write_task_log(
                                format!("Failed to meet the minimum requirements to install the given upgrade\n Failed Requirements:\n {}", hardware_check_result.return_reason),
                                Some("ERROR"),
                            )
                            .await;
                            task_result.status = TaskStatus::Failed;
                            task_result.exit_code = 199;
                            return Ok(task_result);
                        } else {
                            self.write_task_log(
                                format!(
                                    "Successfully verified all the minimum requirements with output:\n {}",
                                    hardware_check_result.logging
                                ),
                                None,
                            )
                            .await;
                        }
                    }
                }

                match self.download_file(attachment.to_owned()).await {
                    Ok(attachment) => {
                        if let Some(attachment) = attachment {
                            self.write_task_log(
                                format!(
                                    "Successfully downloaded {} patch file",
                                    attachment.real_name
                                ),
                                None,
                            )
                            .await;
                            downloaded_attachments.push(attachment)
                        }
                    }
                    Err(error) => {
                        error!(?error, "Failed to download patch file {:?}", attachment);
                        self.write_task_log(
                            format!(
                                "Failed to download patch file {} with error {:?}",
                                attachment.real_name, error
                            ),
                            Some("ERROR"),
                        )
                        .await;
                        task_result.exit_code = 99;
                        task_result.status = TaskStatus::Failed;
                        task_result.output = format!(
                            "Failed to download patch file {:?} with error {:?}",
                            attachment, error
                        );
                        return Ok(task_result);
                    }
                };
            }
        } else {
            #[cfg(windows)]
            {
                // windows patch uninstall by package name when when no need to download files
                match self
                    .process_file(
                        FileAttachment::default(),
                        deployment.deployment_type.to_owned(),
                    )
                    .await
                {
                    Ok(r) => {
                        if r.status == TaskStatus::Failed {
                            error!("Attachment processed with result {:?}", r);
                            task_result = r;
                        } else {
                            info!("Attachment processed with result {:?}", r.exit_code);
                            task_result = r;
                        }
                    }
                    Err(error) => {
                        error!(?error, "Failed to process file {:?}", error);
                        task_result.status = TaskStatus::Failed;
                        task_result.exit_code = 99;
                        task_result.output = format!("Failed to process file {:?}", error);
                    }
                };
                return Ok(task_result);
            }
            #[cfg(not(windows))]
            {
                self.write_task_log(
                    "Uninstall of patch is not supported on this platform".to_owned(),
                    Some("ERROR"),
                )
                .await;
                task_result.status = TaskStatus::Failed;
                task_result.exit_code = 99;
                return Ok(task_result);
            }
        }

        if downloaded_attachments.len() == 0 {
            self.write_task_log("No patch file found to process".to_owned(), None)
                .await;
            task_result.status = TaskStatus::Success;
            task_result.exit_code = 0;
            return Ok(task_result);
        }

        let total_count = downloaded_attachments.len();

        debug!("Total {} files to process", total_count);

        for (index, downloaded_attachment) in downloaded_attachments.into_iter().enumerate() {
            match self
                .process_file(
                    downloaded_attachment.clone(),
                    deployment.deployment_type.to_owned(),
                )
                .await
            {
                Ok(r) => {
                    if r.status == TaskStatus::Failed {
                        error!(
                            "Attachment {:?} processed with result {:?}",
                            downloaded_attachment, r
                        );
                        if r.is_windows_patch_not_applicable_code() {
                            // last item
                            debug!("Considering windows not applicable error code last result status {:?}", task_result.status);
                            if task_result.status == TaskStatus::Waiting && index == total_count - 1
                            {
                                debug!("Setting task result to failed as it is last item and task status is waiting");
                                task_result = r;
                            } else {
                                debug!("Skipping not applicable error code as it is not last item and also task status is not waiting");
                            }
                        } else {
                            task_result = r;
                            if patch.should_succeed_on_single_file_installation == false {
                                break;
                            }
                        }
                    } else {
                        info!(
                            "Attachment {:?} processed with result {:?}",
                            downloaded_attachment, r.exit_code
                        );
                        task_result = r;
                        // if extension is deb consider only one time run
                        if patch.should_succeed_on_single_file_installation
                            || downloaded_attachment.extension() == "deb"
                            || downloaded_attachment.extension() == "rpm"
                        {
                            break;
                        }
                    }
                }
                Err(error) => {
                    error!(?error, "Failed to process file {:?}", downloaded_attachment);
                    task_result.status = TaskStatus::Failed;
                    task_result.exit_code = 99;
                    task_result.output = format!(
                        "Failed to process file {:?} with error {:?}",
                        downloaded_attachment, error
                    );
                    if patch.should_succeed_on_single_file_installation == false {
                        break;
                    }
                }
            };
        }

        // first execute pre deployment script if given
        if self.task.post_deployment_scripts.is_some() {
            self.write_task_log(
                format!("================ Executing post deployment scripts ==================="),
                None,
            )
            .await;
            if let Some(task_result) = self
                .execute_scripts(self.task.post_deployment_scripts.as_ref().unwrap())
                .await?
            {
                return Ok(task_result);
            }
        }

        Ok(task_result)
    }
}
