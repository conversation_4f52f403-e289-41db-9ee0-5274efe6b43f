use crate::{Has<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, PrimaryKey};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq, Hash, Default)]
pub enum FIMConfigType {
    #[default]
    #[serde(alias = "fim")]
    FIM,
    #[serde(rename = "configuration_file")]
    ConfigFile,
    #[serde(rename = "registry")]
    Registry,
}

#[derive(Debug, Serialize, Default, Deserialize, Clone, PartialEq, Eq, Hash)]
pub struct FIMConfig {
    pub id: PrimaryKey,
    pub category: String,
    pub config_type: FIMConfigType,
    pub is_usb: Option<bool>,
    pub exclude_path: Vec<PathBuf>,
    pub include_path: Vec<PathBuf>,
}

impl FIMConfig {
    pub fn category(&self) -> &str {
        &self.category
    }

    pub fn id(&self) -> i64 {
        self.get_primary_key().into()
    }

    pub fn excluded_paths(&self) -> &Vec<PathBuf> {
        &self.exclude_path
    }

    pub fn paths(&self) -> &Vec<PathBuf> {
        &self.include_path
    }
}

impl HasPrimaryKey for FIMConfig {
    fn table_name(&self) -> &str {
        "fim_config"
    }

    fn get_primary_key(&self) -> &PrimaryKey {
        &self.id
    }
}

impl Model for FIMConfig {}
