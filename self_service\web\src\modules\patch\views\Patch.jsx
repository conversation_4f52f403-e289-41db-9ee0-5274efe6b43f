import { ask, message } from '@tauri-apps/plugin-dialog';
import { Row, Col, Button } from 'antd';
import Capitalize from 'lodash/capitalize';
import { getMissingPatchApi, installPatchApi } from '../api/patch';
import { CrudProvider, formatDateTime } from '../../../components/CrudProvider';
import Icon from '../../../components/Icon';
import ExternalLink from '../../../components/ExternalLink';
import PageHeading from '../../../components/PageHeading';
import Severity from '../../../components/Severity';
import Status from '../../../components/Status';
import { useApp } from '../../../App';

export default function Patch({ disabled }) {
  const { state } = useApp();
  const columns = [
    {
      title: 'ID',
      key: 'name',
      dataIndex: 'name',
      exportFormatter(record) {
        return record?.name;
      }
    },
    // {
    //   title: 'UUID',
    //   key: 'uuid',
    //   dataIndex: 'uuid',
    //   hidden: true
    // },
    {
      title: 'Title',
      key: 'title',
      dataIndex: 'title'
    },
    {
      title: 'Severity',
      dataIndex: 'patchSeverity',
      key: 'patchSeverity',
      render({ record }) {
        return <Severity severity={record.patchSeverity} useTag />;
      }
    },
    // {
    //   title: 'Platform',
    //   dataIndex: 'platform',
    //   key: 'platform',
    //   sortable: false,
    //   render({ record }) {
    //     return (
    //       <div className="flex items-center">
    //         <Icon
    //           name={`platform_${record.osPlatform.toLowerCase()}`}
    //           title={record.osPlatform}
    //           className="text-lg mr-2"
    //         />
    //         {record.osPlatform}
    //       </div>
    //     );
    //   }
    // },

    {
      title: 'Release Date',
      dataIndex: 'releaseDate',
      key: 'releaseDate',
      sortable: false,
      exportFormatter(record) {
        return formatDateTime(record.releaseDate || '');
      },
      render({ record }) {
        return <span> {formatDateTime(record.releaseDate || '')}</span>;
      }
    },
    // {
    //   title: 'Rollback',
    //   dataIndex: 'rollback',
    //   key: 'rollback',
    //   sortable: false,
    //   hidden: false,
    //   render({ record }) {
    //     return <Status useTag={true} status={record.rollback} />;
    //   }
    // },
    // {
    //   title: 'Category',
    //   dataIndex: 'patchUpdateCategory',
    //   key: 'patchUpdateCategory',
    //   sortable: false,
    //   render({ record }) {
    //     return <span> {Capitalize(record.patchUpdateCategory || '')}</span>;
    //   }
    // },

    {
      title: 'KBID',
      dataIndex: 'kbId',
      key: 'kbId',
      sortable: false,
      render({ record }) {
        return <ExternalLink href={record.supportUrl}>{record.kbId}</ExternalLink>;
      }
    },
    // {
    //   title: 'Patch Approval Status',
    //   dataIndex: 'patchApprovalStatus',
    //   key: 'patchApprovalStatus',
    //   hidden: true,
    //   sortable: false,
    //   render({ record }) {
    //     return <Status useTag={true} status={record.patchApprovalStatus} />;
    //   }
    // },
    // {
    //   title: 'Patch Test Status',
    //   dataIndex: 'patchTestStatus',
    //   key: 'patchTestStatus',
    //   hidden: true,
    //   sortable: false,
    //   render({ record }) {
    //     return Capitalize((record.patchTestStatus || '').replace('_', ' '));
    //   }
    // },
    {
      title: '',
      dataIndex: 'actions',
      key: 'actions',
      width: 100,
      canHaveButtons(record) {
        return [];
      },
      prependAction({ record }) {
        return <Button onClick={() => handleInstallPatch(record)}>Install</Button>;
      }
    }
  ];

  function handleInstallPatch(software) {
    ask(`Are you sure you want to install ${software.title}?`).then((isConfirm) => {
      if (isConfirm) {
        installPatchApi(software)
          .then(() => {
            message('Installation request has been sent successfully', {
              title: 'Success',
              kind: 'info'
            });
          })
          .catch((e) => {
            message(`Failed to send Installation request ${e.toString()}`, {
              title: 'Error',
              kind: 'error'
            });
          });
      }
    });
  }

  return (
    <Row className="h-full ">
      <Col span={24} className="h-full">
        <PageHeading icon="patch" title="Patch" />
        <CrudProvider
          className="h-full"
          columns={columns}
          resourceTitle="Patch"
          disableFormScrolling
          disableColumnSelection
          hasSearch
          fetchFn={(...args) => getMissingPatchApi(state.agent.id, ...args)}
        />
      </Col>
    </Row>
  );
}
