use core::str;
use core_foundation::base::kCFAllocatorNull;
use core_foundation::dictionary::CFMutableDictionaryRef;
use core_foundation::string::{kCFStringEncodingUTF8, CFStringCreateWithBytesNoCopy};
use core_foundation::{
    base::{kCFAllocatorDefault, CFRange, CFRelease, CFTypeRef, TCFType, TCFTypeRef},
    bundle::{CFBundleCreate, CFBundleGetFunctionPointerForName},
    data::{CFDataGetBytes, CFDataGetLength, CFDataRef},
    dictionary::{CFDictionaryGetValue, CFDictionaryGetValueIfPresent, CFDictionaryRef},
    string::{CFString, CFStringRef},
    url::{kCFURLPOSIXPathStyle, CFURLCreateWithFileSystemPath},
};
use io_kit_sys::{
    kIOMasterPortDefault, IOObjectRelease, IORegistryEntryCreateCFProperties,
    IORegistryEntryFromPath,
};
use logger::error;
use serde_json::Value;
use shell::ShellCommand;
use std::collections::HashMap;
use std::{ffi::CString, mem::MaybeUninit};
use sysctl::Sysctl;
use utils::runtime::create_new_runtime;

type CopyVersionFn = unsafe extern "C" fn() -> CFDictionaryRef;

fn get_core_framework_detail(fn_name_to_invoke: &str) -> Option<CFDictionaryRef> {
    unsafe {
        // Create CFURLRef for CoreFoundation framework
        let bundle_url = CFURLCreateWithFileSystemPath(
            kCFAllocatorDefault,
            CFString::new("/System/Library/Frameworks/CoreFoundation.framework")
                .as_concrete_TypeRef(),
            kCFURLPOSIXPathStyle,
            1,
        );

        if bundle_url.is_null() {
            error!("Failed to create URL for CoreFoundation framework");
            return None;
        }

        // Load CoreFoundation bundle
        let bootstrap_bundle = CFBundleCreate(kCFAllocatorDefault, bundle_url);
        if bootstrap_bundle.is_null() {
            error!("Failed to load CoreFoundation framework");
            return None;
        }

        // Load function pointer for _CFCopySystemVersionDictionary
        let func_name = CFString::new(fn_name_to_invoke);
        let func_ptr =
            CFBundleGetFunctionPointerForName(bootstrap_bundle, func_name.as_concrete_TypeRef());

        if func_ptr.is_null() {
            error!("Failed to load {} function pointer", fn_name_to_invoke);
            return None;
        }

        // Cast function pointer and call the function
        let copy_version: CopyVersionFn = std::mem::transmute(func_ptr);
        let version_dict = copy_version();

        if version_dict.is_null() {
            error!("{} returned null", fn_name_to_invoke);
            return None;
        }

        Some(version_dict)
    }
}

fn get_string_from_dict(dict: CFDictionaryRef, key: &str) -> Option<String> {
    unsafe {
        let key_cfstring = CFString::new(key);
        let mut value_ref: CFTypeRef = std::ptr::null_mut();

        if CFDictionaryGetValueIfPresent(
            dict,
            key_cfstring.as_concrete_TypeRef() as *const _,
            &mut value_ref as *mut _ as *mut _,
        ) != 0
        {
            if !value_ref.is_null() {
                let value = CFString::wrap_under_get_rule(value_ref as CFStringRef).to_string();
                return Some(value);
            }
        }
    }
    None
}

pub fn get_os_version() -> HashMap<String, String> {
    match get_core_framework_detail("_CFCopySystemVersionDictionary") {
        Some(ptr) => {
            let mut values = HashMap::new();
            values.insert("platform".to_owned(), "darwin".to_owned());
            values.insert("platform_like".to_owned(), "darwin".to_owned());
            if let Some(value) = get_string_from_dict(ptr, "ProductName") {
                values.insert("name".to_string(), value);
            }
            if let Some(value) = get_string_from_dict(ptr, "ProductBuildVersion") {
                values.insert("build".to_string(), value);
            }
            if let Some(value) = get_string_from_dict(ptr, "ProductVersion") {
                values.insert("version".to_string(), value.clone());
                let parts = value.split(".").collect::<Vec<&str>>();
                values.insert("major".to_owned(), parts[0].to_owned());
                if parts.len() > 1 {
                    values.insert("minor".to_owned(), parts[1].to_owned());
                }
                if parts.len() > 2 {
                    values.insert("patch".to_owned(), parts[2].to_owned());
                }
            }

            unsafe {
                CFRelease(ptr.as_void_ptr());
            }

            if let Some(ptr) = get_core_framework_detail("_CFCopySupplementalVersionDictionary") {
                if let Some(value) = get_string_from_dict(ptr, "ShortVersionString") {
                    values.insert("version".to_owned(), value);
                }
                if let Some(value) = get_string_from_dict(ptr, "ProductBuildVersion") {
                    if !value.is_empty() {
                        values.insert("build".to_owned(), value);
                    }
                }
                unsafe {
                    CFRelease(ptr.as_void_ptr());
                }
            }

            values
        }
        None => HashMap::new(),
    }
}

fn cfstr(val: &str) -> CFStringRef {
    // this creates broken objects if string len > 9
    // CFString::from_static_string(val).as_concrete_TypeRef()
    // CFString::new(val).as_concrete_TypeRef()

    unsafe {
        CFStringCreateWithBytesNoCopy(
            kCFAllocatorDefault,
            val.as_ptr(),
            val.len() as isize,
            kCFStringEncodingUTF8,
            0,
            kCFAllocatorNull,
        )
    }
}

fn cfdict_get_val(dict: CFDictionaryRef, key: &str) -> Option<CFTypeRef> {
    unsafe {
        let key = cfstr(key);
        let val = CFDictionaryGetValue(dict, key as _);
        CFRelease(key as _);

        match val {
            _ if val.is_null() => None,
            _ => Some(val),
        }
    }
}

fn read_property_value(dict: CFDictionaryRef, key: &str) -> String {
    unsafe {
        let obj = match cfdict_get_val(dict, key) {
            Some(value) => value as CFDataRef,
            None => return "".to_owned(),
        };
        let obj_len = CFDataGetLength(obj);
        let obj_val = vec![0u8; obj_len as usize];
        CFDataGetBytes(obj, CFRange::init(0, obj_len), obj_val.as_ptr() as *mut u8);

        match str::from_utf8(&obj_val) {
            Ok(value) => value.trim_matches(char::from(0)).to_owned(),
            Err(_) => "".to_owned(),
        }
    }
}

fn get_io_kit_data() -> HashMap<String, String> {
    unsafe {
        let root_key = CString::new("IODeviceTree:/").unwrap();
        let root = IORegistryEntryFromPath(kIOMasterPortDefault, root_key.as_ptr() as *mut _);
        if root == 0 {
            error!("Cannot get hardware information from IOKit");
            return HashMap::new();
        }

        let mut properties: MaybeUninit<CFMutableDictionaryRef> = MaybeUninit::uninit();
        if IORegistryEntryCreateCFProperties(root, properties.as_mut_ptr(), kCFAllocatorDefault, 0)
            != 0
        {
            error!("Failed to get properties from IORegistryEntryCreateCFProperties");
            IOObjectRelease(root);
            return HashMap::new();
        }
        IOObjectRelease(root);
        let properties = properties.assume_init();

        let mut value_map = HashMap::new();

        value_map.insert(
            "hardware_version".to_owned(),
            read_property_value(properties, "version"),
        );

        value_map.insert(
            "hardware_vendor".to_owned(),
            read_property_value(properties, "manufacturer"),
        );

        value_map.insert(
            "hardware_model".to_owned(),
            read_property_value(properties, "model"),
        );

        CFRelease(properties.as_void_ptr());

        value_map
    }
}

fn read_sysctl_value(key: &str) -> String {
    match sysctl::Ctl::new(key) {
        Ok(ctl) => ctl.value_string().unwrap_or_default(),
        Err(error) => {
            error!(?error, "Failed to read sysctl value for key {}", key);
            "".to_owned()
        }
    }
}

pub fn get_system_info() -> HashMap<String, String> {
    let output = match create_new_runtime().block_on(async {
        ShellCommand::new("system_profiler SPHardwareDataType -json")
            .run()
            .await
    }) {
        Ok(output) => match serde_json::from_str::<Value>(&output.output) {
            Ok(value) => value,
            Err(error) => {
                error!(?error, "Failed to parse output as json {}", output.output);
                return HashMap::new();
            }
        },
        Err(error) => {
            error!(?error, "Failed to get system profiler");
            return HashMap::new();
        }
    };

    let mut data_map = HashMap::new();
    data_map.extend(get_io_kit_data());
    let keys_map = [("serial_number", "hardware_serial")];

    for key in keys_map {
        if let Some(value) = output["SPHardwareDataType"][0].get(key.0) {
            data_map.insert(
                key.1.to_owned(),
                value.as_str().map_or("".to_owned(), |i| i.to_owned()),
            );
        }
    }

    data_map.insert(
        "cpu_brand".to_owned(),
        read_sysctl_value("machdep.cpu.brand_string"),
    );
    if data_map.contains_key("hardware_model") == false {
        data_map.insert("hardware_model".to_owned(), read_sysctl_value("hw.model"));
    }
    let cpu_subtype = read_sysctl_value("hw.cpusubtype");
    data_map.insert(
        "cpu_subtype".to_owned(),
        match cpu_subtype.as_str() {
            // Intel CPU subtypes
            "8" => "Intel Core 2".to_string(),
            "9" => "Intel Core i7".to_string(),
            "10" => "Intel Core i5".to_string(),
            "11" => "Intel Core i3".to_string(),
            "12" => "Intel Xeon".to_string(),

            // Apple Silicon (ARM64) subtypes
            "0" => "Generic ARM64".to_string(),
            "1" => "ARM64 with FP support".to_string(),
            "2" => "Apple ARM64e (A12+, M1/M2)".to_string(),

            _ => format!("Unknown subtype ({})", cpu_subtype),
        },
    );
    let cpu_type = read_sysctl_value("hw.cputype");
    let cput_type = match cpu_type.as_str() {
        "7" => "x86".to_string(),
        "16777223" => "x86_64".to_string(),
        "12" => "arm".to_string(),
        "16777228" => "arm64".to_string(),
        _ => format!("Unknown CPU Type ({})", cpu_type),
    };
    data_map.insert("cpu_type".to_owned(), cput_type);

    data_map
}
