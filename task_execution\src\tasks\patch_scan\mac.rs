use crate::{
    tasks::{
        attachment_downloader::AttachmentDownloader, command_executor::CommandExecutor,
        unzip::Unzip,
    },
    TaskExecutable, TaskExecutionError,
};
use anyhow::{anyhow, Result};
use api::patch::{get_third_party_patch_scripts, send_patch_discovered_data};
use database::models::FileAttachment;
use logger::{debug, error, info, ModuleLogger};
use rayon::iter::{IntoParallelIterator, ParallelIterator};
use serde::Deserialize;
use serde_json::json;
use shell::ShellCommand;
use std::{cmp::Ordering, path::Path, time::Duration};
use tokio::{fs, time::Instant};
use utils::{
    cpu::max_blocking_threads, dir::get_patch_dir, runtime::create_new_runtime,
    shutdown::is_system_running,
};
use version_compare::Version;

#[derive(Debug, <PERSON><PERSON>, Deserialize)]
#[serde(rename_all = "camelCase")]
struct ThirdPartyDetectionScript {
    uuid: String,
    version: String,
    detection_script: String,
}

#[derive(Debug, <PERSON>lone)]
pub struct PatchFile {
    sequence: u32,
    name: String,
    path: Box<Path>,
    evaluation_result: Option<bool>,
}

impl PatchFile {
    pub fn is_valid_file(&self) -> bool {
        let parts = self.get_name_parts();
        parts.len() > 0
    }

    pub fn get_uuid(&self) -> String {
        let parts = self.get_name_parts();
        let uuid = parts.first().unwrap();
        uuid.to_owned()
    }

    pub fn get_name_parts(&self) -> Vec<String> {
        let split_parts = self.name.split('.');
        split_parts.map(|i| i.to_owned()).collect()
    }
}

pub struct MacPatchFinder<'a> {
    version: &'a str,
    endpoint_id: i64,
    task: Box<&'a dyn TaskExecutable>,
    files: Vec<PatchFile>,
}

impl<'a> MacPatchFinder<'a> {
    pub fn new(version: &'a str, endpoint_id: i64, task: Box<&'a dyn TaskExecutable>) -> Self {
        Self {
            version,
            files: vec![],
            endpoint_id,
            task,
        }
    }

    async fn collect_files(&self) -> Result<Vec<PatchFile>> {
        let mut files = vec![];
        let mut counter = 1;
        let dist_folder = get_patch_dir().join("dist");

        let mut dir = fs::read_dir(dist_folder).await?;

        while let Some(entry) = dir.next_entry().await? {
            if entry.path().extension().is_some_and(|ext| ext == "dist") {
                let path = entry.path();

                debug!("Collecting file {:?}", path.display());

                let file = path.file_name();

                if file.is_some() {
                    let patch_file = PatchFile {
                        sequence: counter,
                        path: Box::from(path.as_path()),
                        name: file.unwrap().to_str().unwrap().to_owned(),
                        evaluation_result: None,
                    };

                    files.push(patch_file);

                    counter = counter + 1;
                }
            } else {
                debug!("Skipping file with extension {}", entry.path().display());
            }
        }

        Ok(files)
    }

    fn process_single_file(mut file: PatchFile) -> PatchFile {
        ModuleLogger::new(
            "actors::patch_scan::mac",
            Some(Path::new("patch").join("mac-scanning")),
            Some(file.name.clone()),
        ).with(|| {
            info!("Processing file {}", file.name);
            if file.is_valid_file() {
                let dist_dir = get_patch_dir().join("dist");
                let uuid = file.get_uuid();
                let pkg_path = dist_dir.join(format!("{}.pkg", uuid));

                let cmd = format!(
                    "productbuild --distribution {} {}",
                    file.path.to_str().unwrap(),
                    pkg_path.to_str().unwrap()
                );

                match create_new_runtime().block_on(async {
                    ShellCommand::new(&cmd)
                        .cwd(&dist_dir)
                        .run()
                        .await
                })
                {
                    Ok(output) => {
                        if output.succeeded() {
                            debug!("Built pkg file successfully with output {}", output.output);
                        } else {
                            error!(
                                "Failed to build pkg file from dist file {} with output {} and exit code {}", file.name, output.output, output.exit_code
                            );
                        }
                    }
                    Err(error) => {
                        error!(
                            ?error,
                            "Failed to build pkg file from dist file {}", file.name
                        );
                        file.evaluation_result = Some(false);
                        return file;
                    }
                };

                let check_command = format!("sudo installer -pkg {} -target /", pkg_path.to_str().unwrap());
                match create_new_runtime().block_on(async {
                    ShellCommand::new(&check_command)
                        .cwd(&dist_dir)
                        .run()
                        .await
                })
                {
                    Ok(output) => {
                        if output.succeeded() {
                            debug!(
                                "Installer command exit code {} and output \n {}\n",
                                output.exit_code, output.output
                            );
                            if output.output.contains("installing at base path")
                                || output.output.contains("upgrading at base path")
                            {
                                file.evaluation_result = Some(true);
                            } else {
                                file.evaluation_result = Some(false);
                            }
                        } else {
                            error!(
                                "Failed to run installer command with output {} and exit code {}", output.output, output.exit_code
                            );
                            file.evaluation_result = Some(false);
                        }
                    }
                    Err(error) => {
                        error!(
                            ?error,
                            "Failed to run installer command for file {}", file.name
                        );
                        file.evaluation_result = Some(false);
                    }
                }
            } else {
                error!("{:?} File is not valid to scan", file);
                file.evaluation_result = Some(false);
            }
            // just to cool down cpu
            std::thread::sleep(Duration::from_millis(500));
            file
        })
    }

    async fn process_files(&self) -> Result<Vec<String>> {
        let files = self.files.clone();

        let total_files = files.len();

        debug!("Processing total {} Files", total_files);
        let mut missing_uuids = vec![];
        let allowed_threads = max_blocking_threads();

        debug!("Using {} cores for rayon thread pool", allowed_threads);

        let logger = self.task.logger();

        let (tx, rx) = tokio::sync::oneshot::channel();

        tokio::task::spawn_blocking(move || {
            logger.with(|| {
                let pool = rayon::ThreadPoolBuilder::new()
                    .num_threads(allowed_threads)
                    .build()
                    .unwrap();

                let result = pool.install(|| {
                    files
                        .into_par_iter()
                        .take_any_while(|_| is_system_running())
                        .map(|file| {
                            logger.with(|| {
                                let result = MacPatchFinder::process_single_file(file);

                                debug!(
                                    "Files [{} of {}] is finished processing with status {}",
                                    result.sequence,
                                    total_files,
                                    result.evaluation_result.as_ref().unwrap_or(&false)
                                );
                                result
                            })
                        })
                        .collect::<Vec<PatchFile>>()
                });
                if let Err(error) = tx.send(result) {
                    error!(?error, "Failed to send files to main thread");
                }
            })
        });

        let result = match rx.await {
            Ok(files) => {
                debug!("Received processed files from thread pool");
                files
            }
            Err(error) => {
                error!(?error, "Failed to receive processed files from thread pool");
                return Err(anyhow!("Failed to receive processed files from thread pool").into());
            }
        };

        for file in result {
            if file.evaluation_result.is_some_and(|item| item) {
                missing_uuids.push(file.get_uuid());
                info!("File {} is valid", file.name);
            }
        }

        debug!("Got missing ids: {:?}", missing_uuids);

        Ok(missing_uuids)
    }

    fn installed_version_comparision(
        &self,
        installed_version: &str,
        patch_version: &str,
    ) -> Result<Ordering> {
        let lhs = Version::from(installed_version);
        let rhs = Version::from(patch_version);
        if let (Some(lhs), Some(rhs)) = (&lhs, &rhs) {
            if lhs == rhs {
                Ok(Ordering::Equal)
            } else if lhs < rhs {
                Ok(Ordering::Less)
            } else {
                Ok(Ordering::Greater)
            }
        } else {
            if lhs.is_none() {
                error!("Failed to parse installed version {}", installed_version);
            }
            if rhs.is_none() {
                error!("Failed to parse patch version {}", patch_version);
            }
            Err(anyhow!("Failed to parse version"))
        }
    }

    async fn process_third_party_patches(
        &self,
        patches: Vec<ThirdPartyDetectionScript>,
    ) -> Result<(Vec<String>, Vec<String>)> {
        let mut missing_patches = vec![];
        let mut installed_patches = vec![];

        for patch in patches {
            if !patch.detection_script.is_empty() {
                let installed_version =
                    match CommandExecutor::new_command(&patch.detection_script, self.task.clone())
                        .capture()
                        .execute()
                        .await
                    {
                        Ok(output) => output.output,
                        Err(error) => {
                            error!(
                                ?error,
                                "Failed to execute detection script for patch {}", patch.uuid
                            );
                            continue;
                        }
                    };
                match self.installed_version_comparision(&installed_version, &patch.version) {
                    Ok(Ordering::Less) => {
                        debug!(
                            "[{}] Installed version {} is less than {} so marking it as missing",
                            patch.uuid, installed_version, patch.version
                        );
                        missing_patches.push(patch.uuid);
                    }
                    Ok(Ordering::Equal) => {
                        debug!(
                            "[{}] Installed version {} is equal to {}",
                            patch.uuid, installed_version, patch.version
                        );
                        installed_patches.push(patch.uuid);
                    }
                    Ok(Ordering::Greater) => {
                        debug!(
                            "[{}] Installed version {} is greater than {} so skipping it",
                            patch.uuid, installed_version, patch.version
                        );
                        installed_patches.push(patch.uuid);
                    }
                    Err(error) => {
                        error!(?error, "Failed to compare version for patch {}", patch.uuid);
                    }
                }
            } else {
                error!("Patch detection script is empty so skipping {:?}", patch);
            }
        }

        Ok((missing_patches, installed_patches))
    }

    pub async fn scan(mut self) -> Result<()> {
        let time = Instant::now();
        let patch_directory = get_patch_dir();

        fs::create_dir_all(&patch_directory).await?;

        let attachment = AttachmentDownloader::new(
            FileAttachment {
                real_name: format!("{}.7z", self.version),
                ref_name: format!("{}", self.version),
                zirozen_download_url: Some(format!(
                    "/patch/asset/macos/download-dist/{}",
                    self.version
                )),
                ..Default::default()
            },
            self.task.clone(),
            Some(patch_directory),
        )
        .download()
        .await?;

        info!("macOS patch file download successfully {:?}", attachment);

        match Unzip::new(&attachment, self.task.clone())
            .extract(None)
            .await
        {
            Err(error) => {
                return Err(TaskExecutionError::ExtractionError(format!(
                    "Failed to extract archive {} with error {:?}",
                    attachment.real_name, error
                ))
                .into())
            }
            Ok(_) => {}
        };

        info!("File {} extracted successfully", attachment.real_name);

        self.files = match self.collect_files().await {
            Ok(files) => files,
            Err(error) => {
                error!(?error, "Failed to collect macOS patch file entries");
                return Err(TaskExecutionError::MacOSPatchScanError(format!(
                    "Failed to collect macOS patch file entries with error {:?}",
                    error
                ))
                .into());
            }
        };

        let endpoint_id = self.endpoint_id;

        let mut missing_uuids = self.process_files().await?;
        let mut installed_uuids = vec![];

        // third party for mac
        let third_party_patches: Vec<ThirdPartyDetectionScript> =
            match get_third_party_patch_scripts("mac").await {
                Ok(patches) => match serde_json::from_value(patches) {
                    Ok(patches) => patches,
                    Err(error) => {
                        error!(?error, "Failed to parse third party patch scripts");
                        self.task
                            .write_task_log(
                                format!("Failed to parse third party patch scripts {:?}", error),
                                Some("ERROR"),
                            )
                            .await;
                        vec![]
                    }
                },
                Err(error) => {
                    error!(?error, "Failed to get third party patch scripts");
                    self.task
                        .write_task_log(
                            format!("Failed to get third party patch scripts {:?}", error),
                            Some("ERROR"),
                        )
                        .await;
                    vec![]
                }
            };

        match self.process_third_party_patches(third_party_patches).await {
            Ok((patches, installed_patches)) => {
                missing_uuids.extend(patches);
                installed_uuids.extend(installed_patches);
            }
            Err(error) => {
                error!(?error, "Failed to process third party patches");
                self.task
                    .write_task_log(
                        format!("Failed to process third party patches {:?}", error),
                        Some("ERROR"),
                    )
                    .await;
            }
        }

        info!(
            "Total {} missing patches {} and total {} installed patches {}",
            missing_uuids.len(),
            serde_json::to_string(&missing_uuids)?,
            installed_uuids.len(),
            serde_json::to_string(&installed_uuids)?,
        );

        send_patch_discovered_data(
            endpoint_id,
            json!({
                "macMissingPatches": missing_uuids,
                "patchData": json!({
                    "mac_third_party_installed_list": installed_uuids
                })
            }),
        )
        .await?;

        debug!("Time taken for full scanning {:?}", time.elapsed());

        Ok(())
    }
}
