import { useEffect, useState } from 'react';
import { emit } from '@tauri-apps/api/event';
import { getCurrentWindow } from '@tauri-apps/api/window';
import { Form, Input, Button, Typography, Space } from 'antd';
import { LockOutlined } from '@ant-design/icons';

const { Title } = Typography;

export default function Credentials() {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [title, setTitle] = useState('');

  useEffect(() => {
    (async () => {
      try {
        setTitle(await getCurrentWindow().title());
      } catch (error) {
        console.error('Failed to close window:', error);
      }
    })();
  }, []);

  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      // Emit the password to the Tauri backend
      await emit('password', values.password);
      // Close the window after successful submission
      // The window will be closed by the backend after receiving the password
    } catch (error) {
      console.error('Failed to send password:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="h-screen flex flex-col items-center justify-center bg-gray-50 p-4">
      <div className="text-center my-4 w-full">
        <LockOutlined className="text-4xl text-blue-500 mb-2" />
        <Title level={3} className="mb-2">
          {title}
        </Title>
      </div>

      <Form form={form} onFinish={handleSubmit} className="w-full" layout="vertical" size="large">
        <Form.Item
          name="password"
          label="Password"
          rules={[{ required: true, message: 'Please enter your password' }]}>
          <Input.Password prefix={<LockOutlined />} placeholder="Enter password" />
        </Form.Item>

        <Form.Item
          name="confirm_password"
          label="Confirm Password"
          rules={[
            { required: true, message: 'Please enter your confirm password' },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('password') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('The passwords that you entered do not match!'));
              }
            })
          ]}>
          <Input.Password prefix={<LockOutlined />} placeholder="Enter Confirm password" />
        </Form.Item>

        <Form.Item className="my-4">
          <Space className="w-full" direction="vertical">
            <Button type="primary" htmlType="submit" loading={loading} className="w-full">
              {loading ? 'Sending...' : 'Set Password'}
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </div>
  );
}
