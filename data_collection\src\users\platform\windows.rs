#[allow(non_upper_case_globals)]
use crate::users::{logged_in_users::LoggedInUser, user::SystemUser};
use logger::{error, trace};
use std::{
    collections::{HashMap, HashSet},
    ffi::c_void,
    path::PathBuf,
    ptr::null_mut,
};
use windows::{
    core::{<PERSON><PERSON>TR, PWSTR},
    Win32::{
        NetworkManagement::NetManagement::{
            NERR_Success, NetApiBufferFree, NetUserEnum, FILTER_NORMAL_ACCOUNT,
            MAX_PREFERRED_LENGTH, USER_INFO_3,
        },
        System::RemoteDesktop::{
            WTSActive, WTSClientInfo, WTSClientName, WTSConnectQuery, WTSConnected,
            WTSDisconnected, WTSDown, WTSEnumerateSessionsExW, WTSFreeMemory, WTSFreeMemoryExW,
            WTSIdle, WTSInit, WTS<PERSON>isten, WTSQuerySessionInformationW, WTSReset, WTS<PERSON>essionIn<PERSON>,
            W<PERSON><PERSON><PERSON>ow, W<PERSON><PERSON>IENT<PERSON>, W<PERSON><PERSON><PERSON><PERSON>, WTS_CURRENT_SERVER_HANDLE, WTS_SESSION_INFO_1W,
            WTS_TYPE_CLASS,
        },
    },
};
use windows_registry::WinRegistry;

fn get_user_profile_hash_map() -> HashMap<String, String> {
    let base_registry_path =
        PathBuf::from("HKLM\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\ProfileList");
    match WinRegistry::new(&base_registry_path) {
        Ok(reg) => {
            let mut users_profile_path = HashMap::new();
            for key in reg.keys() {
                match WinRegistry::new(base_registry_path.join(&key)) {
                    Ok(reg) => {
                        let path: String = reg.get_value("ProfileImagePath".to_owned());
                        users_profile_path
                            .insert(key.split("-").last().unwrap_or_default().to_owned(), path);
                    }
                    Err(error) => {
                        error!(
                            ?error,
                            "Failed to open registry for profile path for user {}", key
                        );
                    }
                }
            }
            users_profile_path
        }
        Err(error) => {
            error!(?error, "Failed to open registry at profilelist path");
            HashMap::new()
        }
    }
}

fn from_ptr_to_str(ptr: *const u16) -> String {
    unsafe {
        PWSTR::from_raw(ptr as *mut _)
            .to_string()
            .unwrap_or_default()
    }
}

fn filetime_to_epoch(filetime: i64) -> i64 {
    const WINDOWS_TO_UNIX_EPOCH_SECS: i64 = 11644473600; // Difference between 1601-01-01 and 1970-01-01 in seconds
    filetime / 10_000_000 - WINDOWS_TO_UNIX_EPOCH_SECS
}

/// Gets the SID of a given domain user.
fn get_user_sid(username: &str) -> Option<String> {
    match windows_acl::helper::name_to_sid(username, None) {
        Ok(mut sid) => match windows_acl::helper::sid_to_string(sid.as_mut_ptr() as *mut _) {
            Ok(value) => Some(value),
            Err(error) => {
                error!(?error, "Failed to convert sid to string");
                None
            }
        },
        Err(error) => {
            error!(?error, "Failed to generate sid");
            None
        }
    }
}

fn generate_logged_in_user(session: &WTS_SESSION_INFO_1W) -> Option<LoggedInUser> {
    if session.State != WTSActive || session.SessionId == 0 {
        return None;
    }

    let mut logged_in_user = LoggedInUser::default();

    logged_in_user.tty = unsafe { session.pSessionName.to_string().unwrap_or_default() };

    logged_in_user.r#type = match session.State {
        #[allow(non_upper_case_globals)]
        WTSActive => "active",
        #[allow(non_upper_case_globals)]
        WTSDisconnected => "disconnected",
        #[allow(non_upper_case_globals)]
        WTSConnected => "connected",
        #[allow(non_upper_case_globals)]
        WTSConnectQuery => "connectquery",
        #[allow(non_upper_case_globals)]
        WTSShadow => "shadow",
        #[allow(non_upper_case_globals)]
        WTSIdle => "idle",
        #[allow(non_upper_case_globals)]
        WTSListen => "listen",
        #[allow(non_upper_case_globals)]
        WTSReset => "reset",
        #[allow(non_upper_case_globals)]
        WTSDown => "down",
        #[allow(non_upper_case_globals)]
        WTSInit => "init",
        _ => "unknown",
    }
    .to_owned();

    unsafe {
        let mut session_info: PWSTR = PWSTR::null();
        let mut bytes_returned = 0;

        if let Err(error) = WTSQuerySessionInformationW(
            Some(WTS_CURRENT_SERVER_HANDLE),
            session.SessionId,
            WTSSessionInfo,
            &mut session_info,
            &mut bytes_returned,
        ) {
            error!(
                ?error,
                "Failed to get session info for {}", session.SessionId
            );
            return None;
        }

        let wts_session: WTSINFOW = std::ptr::read(session_info.0 as *const WTSINFOW);

        logged_in_user.user = from_ptr_to_str(wts_session.UserName.as_ptr());

        logged_in_user.time = filetime_to_epoch(wts_session.ConnectTime);

        logged_in_user.pid = -1;

        WTSFreeMemory(session_info.as_ptr() as *mut c_void);

        bytes_returned = 0;
        if let Err(error) = WTSQuerySessionInformationW(
            Some(WTS_CURRENT_SERVER_HANDLE),
            session.SessionId,
            WTSClientInfo,
            &mut session_info,
            &mut bytes_returned,
        ) {
            error!(
                ?error,
                "Failed to get session client info for {}", session.SessionId
            );
            return None;
        }
        let wts_client: WTSCLIENTA = std::ptr::read(session_info.0 as *const WTSCLIENTA);
        logged_in_user.host = from_ptr_to_str(wts_client.ClientAddress.as_ptr());
        if logged_in_user.host.is_empty() {
            let mut client_name = PWSTR::null();
            let mut bytes_returned = 0;
            if let Err(error) = WTSQuerySessionInformationW(
                Some(WTS_CURRENT_SERVER_HANDLE),
                session.SessionId,
                WTSClientName,
                &mut client_name,
                &mut bytes_returned,
            ) {
                error!(
                    ?error,
                    "Error getting client name using session information"
                );
            } else {
                logged_in_user.host = client_name.to_string().unwrap_or_default();
                WTSFreeMemory(client_name.as_ptr() as *mut c_void);
            }
        }

        let domain = format!(
            "{}\\{}",
            PWSTR::from_raw(wts_session.Domain.as_ptr() as *mut _)
                .to_string()
                .unwrap_or_default(),
            PWSTR::from_raw(wts_session.UserName.as_ptr() as *mut _)
                .to_string()
                .unwrap_or_default()
        );
        if let Some(sid) = get_user_sid(&domain) {
            logged_in_user.registry_hive = format!("HKEY_USERS\\{}", sid);
            logged_in_user.sid = sid;
        }
        WTSFreeMemory(session_info.as_ptr() as *mut c_void);
    }

    Some(logged_in_user)
}

pub fn get_logged_in_users() -> HashSet<LoggedInUser> {
    let mut logged_in_users = HashSet::new();
    unsafe {
        let mut level = 1;
        let mut count = 0;
        let mut session_info: *mut WTS_SESSION_INFO_1W = null_mut();
        if let Err(error) = WTSEnumerateSessionsExW(
            Some(WTS_CURRENT_SERVER_HANDLE),
            &mut level,
            0,
            &mut session_info,
            &mut count,
        ) {
            error!(?error, "Failed to get active sessions");
            return logged_in_users;
        }

        let sessions = std::slice::from_raw_parts(session_info, count as usize);

        for session in sessions {
            if let Some(logged_in_user) = generate_logged_in_user(session) {
                logged_in_users.insert(logged_in_user);
            }
        }

        // Free the memory allocated by WTSEnumerateSessionsExW
        if let Err(error) = WTSFreeMemoryExW(WTS_TYPE_CLASS(1), session_info.cast(), count) {
            error!(?error, "Failed to free up memory of session info");
        }
    }
    logged_in_users
}

pub fn get_system_users() -> HashSet<SystemUser> {
    let mut users = HashSet::new();
    unsafe {
        let mut buf_ptr: *mut u8 = std::ptr::null_mut();
        let mut entries_read = 0;
        let mut total_entries = 0;

        let status = NetUserEnum(
            PCWSTR::null(),
            3, // USER_INFO_3 has full details
            FILTER_NORMAL_ACCOUNT,
            &mut buf_ptr as *mut *mut u8,
            MAX_PREFERRED_LENGTH,
            &mut entries_read,
            &mut total_entries,
            None,
        );

        let profile_path_hashmap = get_user_profile_hash_map();

        if status == NERR_Success {
            trace!("Found total {} users entry", entries_read);
            let user_info_ptr = buf_ptr as *mut USER_INFO_3;
            for i in 0..entries_read {
                let user = user_info_ptr.offset(i as isize);
                let user_id = (*user).usri3_user_id;
                let username = (*user).usri3_name.to_string().unwrap_or_default();
                let description = (*user).usri3_comment.to_string().unwrap_or_default();
                let home_dir = match profile_path_hashmap.get(&user_id.to_string()) {
                    Some(path) => path.to_owned(),
                    None => "".to_owned(),
                };

                users.insert(
                    SystemUser::default()
                        .uid(user_id)
                        .username(username)
                        .description(description)
                        .directory(home_dir)
                        .shell(r#"C:\Windows\system32\cmd.exe"#.to_owned()),
                );
            }
            NetApiBufferFree(Some(buf_ptr as *mut _));
        } else {
            error!("Failed to read NetUserEnum with status {}", status);
        }
    }
    users
}
