use crate::users::{logged_in_users::LoggedInUser, user::SystemUser};
use libc::{endpwent, endutxent, getpwent, getutxent, passwd, setutxent};
use std::{
    collections::HashSet,
    ffi::{c_char, CStr},
};

fn c_char_to_str(ptr: *const c_char) -> String {
    unsafe { CStr::from_ptr(ptr).to_string_lossy().to_string() }
}

pub fn get_logged_in_users() -> HashSet<LoggedInUser> {
    let mut logged_in_users = HashSet::new();
    unsafe {
        #[cfg(target_os = "linux")]
        {
            use std::ffi::CString;

            let c_string = CString::new("/var/run/utmp").unwrap();
            let code = libc::utmpxname(c_string.as_ptr());
            if code != 0 {
                logger::error!("Failed to read utmpx with result code {}", code);
                return HashSet::new();
            }
        }
        setutxent();
        while let Some(entry) = getutxent().as_mut() {
            if entry.ut_pid == 1 {
                continue;
            }
            let mut user = LoggedInUser::default();
            // user.sid = c_char_to_str(c_user.ut_id.as_ptr());
            user.user = c_char_to_str(entry.ut_user.as_ptr());
            user.tty = c_char_to_str(entry.ut_line.as_ptr());
            user.host = c_char_to_str(entry.ut_host.as_ptr());
            user.time = entry.ut_tv.tv_sec as i64;
            user.pid = entry.ut_pid;
            user.r#type = match entry.ut_type {
                0 => "empty",
                1 => "runlevel",
                2 => "boot_time",
                3 => "old_time",
                4 => "new_time",
                5 => "init",
                6 => "login",
                7 => "user",
                8 => "dead",
                9 => "accounting",
                _ => "unknown",
            }
            .to_owned();

            logged_in_users.insert(user);
        }
        endutxent();
    };
    logged_in_users
}

pub fn get_system_users() -> HashSet<SystemUser> {
    let mut users = HashSet::new();
    unsafe {
        loop {
            let pw = getpwent();
            if pw.is_null() {
                break;
            }
            let passwd: &passwd = &*pw;
            users.insert(
                SystemUser::default()
                    .uid(passwd.pw_uid)
                    .username(CStr::from_ptr(passwd.pw_name).to_string_lossy().to_string())
                    .description(
                        CStr::from_ptr(passwd.pw_gecos)
                            .to_string_lossy()
                            .to_string(),
                    )
                    .directory(CStr::from_ptr(passwd.pw_dir).to_string_lossy().to_string())
                    .shell(
                        CStr::from_ptr(passwd.pw_shell)
                            .to_string_lossy()
                            .to_string(),
                    ),
            );
        }
        endpwent();
    }
    users
}
