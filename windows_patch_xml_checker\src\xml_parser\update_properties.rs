use serde::Deserialize;

// #[derive(Debug, Deserialize)]
// pub enum OperationRequirement {
//     CanRequestReboot,
// }

// #[derive(Debug, Deserialize)]
// #[serde(rename_all = "PascalCase")]
// pub struct OperationBehaviour {
//     #[serde(rename = "@RebootBehavior")]
//     reboot_behaviour: OperationRequirement,
// }

#[derive(Debug, <PERSON>lone, PartialEq, Deserialize, Default)]
pub enum UpdateType {
    #[default]
    Unknown,
    Software,
    Detectoid,
    Driver,
    Category,
}

impl UpdateType {
    pub fn is_software(&self) -> bool {
        matches!(self, UpdateType::Software)
    }

    pub fn is_detectoid(&self) -> bool {
        matches!(self, UpdateType::Detectoid)
    }
}

impl Into<String> for UpdateType {
    fn into(self) -> String {
        match self {
            UpdateType::Unknown => "Unknown".to_owned(),
            UpdateType::Software => "Software".to_owned(),
            UpdateType::Detectoid => "Detectoid".to_owned(),
            UpdateType::Driver => "Driver".to_owned(),
            UpdateType::Category => "Category".to_owned(),
        }
    }
}

impl Into<UpdateType> for String {
    fn into(self) -> UpdateType {
        match self.as_str() {
            "Software" => UpdateType::Software,
            "Detectoid" => UpdateType::Detectoid,
            "Driver" => UpdateType::Driver,
            "Category" => UpdateType::Category,
            _ => UpdateType::Unknown,
        }
    }
}

impl Into<UpdateType> for &str {
    fn into(self) -> UpdateType {
        match self {
            "Software" => UpdateType::Software,
            "Detectoid" => UpdateType::Detectoid,
            "Driver" => UpdateType::Driver,
            "Category" => UpdateType::Category,
            _ => UpdateType::Unknown,
        }
    }
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct UpdateProperties {
    // #[serde(rename = "@DefaultPropertiesLanguage")]
    // _language: Option<String>,
    #[serde(rename = "@ProductName")]
    pub product_name: Option<String>,
    #[serde(rename = "@UpdateType")]
    pub update_type: Option<UpdateType>,
    // publication_state: Option<String>,
    // max_download_size: Option<u64>,
    // installation_behavior: Option<OperationBehaviour>,
    // uninstallation_behavior: Option<OperationBehaviour>,
    #[serde(rename = "KBArticleID")]
    pub kb_id: Option<String>,
}
