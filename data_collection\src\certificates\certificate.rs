use crate::execute_in_thread_pool;
use logger::{debug, error};
use rayon::iter::{IntoParallelRefIterator, ParallelIterator};
use serde::Serialize;
use std::collections::HashSet;
use utils::shutdown::is_system_running;
use x509_parser::prelude::{FromDer, X509Certificate};

#[derive(Debug, Serialize, Eq, PartialEq, Hash, Default)]
pub enum Status {
    #[serde(rename = "Expired")]
    Expired,
    #[serde(rename = "Not expired")]
    Valid,
    #[default]
    Unknown,
}

#[derive(Debug, Serialize, PartialEq, Eq, Hash, Default)]
pub struct Certificate {
    common_name: String,
    status: Status,
    ca: String,
    self_signed: String,
    not_valid_after: i64,
    not_valid_before: i64,
    issuer: String,
    key_algorithm: String,
    signing_algorithm: String,
}

impl From<X509Certificate<'_>> for Certificate {
    fn from(value: X509Certificate<'_>) -> Self {
        Self {
            common_name: value
                .subject()
                .iter_common_name()
                .last()
                .map(|cn| cn.as_str().unwrap_or(""))
                .map_or(
                    value
                        .subject()
                        .iter_organizational_unit()
                        .last()
                        .map(|cn| cn.as_str().unwrap_or(""))
                        .unwrap_or_default()
                        .to_owned(),
                    |i| i.to_owned(),
                )
                .to_owned(),
            status: if value.validity.is_valid() {
                Status::Valid
            } else {
                Status::Expired
            },
            ca: if value.is_ca() { "yes" } else { "no" }.to_owned(),
            self_signed: if value.subject() == value.issuer() {
                "yes"
            } else {
                "no"
            }
            .to_owned(),
            not_valid_after: value.validity().not_after.timestamp(),
            not_valid_before: value.validity().not_before.timestamp(),
            issuer: value.issuer.to_string(),
            key_algorithm: get_string_from_oid(value.public_key().algorithm.oid().to_id_string()),
            signing_algorithm: get_string_from_oid(value.signature_algorithm.oid().to_id_string()),
        }
    }
}

impl Certificate {
    pub fn collect() -> HashSet<Self> {
        let cert_result = {
            cfg_if! {
                if #[cfg(windows)] {
                    use rustls_pki_types::CertificateDer;
                    use rustls_native_certs::CertificateResult;

                    let mut certificate_result = CertificateResult::default();
                    for store_name in ["MY", "Root", "Trust", "CA"].into_iter() {
                        match schannel::cert_store::CertStore::open_current_user(store_name) {
                            Ok(store) => {
                                for cert in store.certs() {
                                    certificate_result
                                        .certs
                                        .push(CertificateDer::from(cert.to_der().to_vec()));
                                }
                            }
                            Err(error) => {
                                error!(
                                    ?error,
                                    "Failed to get user for store {}", store_name
                                )
                            }
                        };
                        match schannel::cert_store::CertStore::open_local_machine(store_name) {
                            Ok(store) => {
                                for cert in store.certs() {
                                    certificate_result
                                        .certs
                                        .push(CertificateDer::from(cert.to_der().to_vec()));
                                }
                            }
                            Err(error) => {
                                error!(
                                    ?error,
                                    "Failed to get certificates for store {}", store_name
                                )
                            }
                        }
                    }
                    certificate_result
                } else {
                    rustls_native_certs::load_native_certs()
                }
            }
        };

        let certificates = execute_in_thread_pool(|| {
            cert_result
                .certs
                .par_iter()
                .take_any_while(|_| is_system_running())
                .map(|cert| X509Certificate::from_der(cert.as_ref()).to_owned())
                .filter(|value| {
                    if let Err(error) = value {
                        error!(?error, "Failed to parse ceritificate to X509");
                        return false;
                    }
                    return true;
                })
                .map(|cert| cert.unwrap())
                .map(|result| result.1)
                .map(|parsed_certificate| parsed_certificate.into())
                .collect::<HashSet<Certificate>>()
        });

        debug!("Collected total {} certificates", certificates.len(),);

        certificates
    }
}

fn get_string_from_oid(oid: String) -> String {
    match oid.as_str() {
        "1.2.840.113549.1.1.1" => "RSA",
        "1.2.840.10045.2.1" => "ECDSA",
        "1.2.840.10040.4.1" => "DSA",
        "1.2.840.113549.1.1.5" => "SHA-1 with RSA",
        "1.2.840.113549.1.1.11" => "SHA-256 with RSA",
        "1.2.840.113549.1.1.12" => "SHA-384 with RSA",
        "1.2.840.113549.1.1.13" => "SHA-512 with RSA",
        "1.2.840.10045.4.3.2" => "SHA-256 with ECDSA",
        "1.2.840.10045.4.3.3" => "SHA-384 with ECDSA",
        "1.2.840.10045.4.3.4" => "SHA-512 with ECDSA",
        _ => "Unknown Algorithm",
    }
    .to_string()
}
