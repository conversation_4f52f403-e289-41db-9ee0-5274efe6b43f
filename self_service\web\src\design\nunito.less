@font-face {
  font-family: 'Nunito';
  src: url('./nunito/Nunito-BlackItalic.woff2') format('woff2'),
      url('./nunito/Nunito-BlackItalic.woff') format('woff');
  font-weight: 900;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('./nunito/Nunito-BoldItalic.woff2') format('woff2'),
      url('./nunito/Nunito-BoldItalic.woff') format('woff');
  font-weight: bold;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('./nunito/Nunito-ExtraBold.woff2') format('woff2'),
      url('./nunito/Nunito-ExtraBold.woff') format('woff');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('./nunito/Nunito-Black.woff2') format('woff2'),
      url('./nunito/Nunito-Black.woff') format('woff');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('./nunito/Nunito-Bold.woff2') format('woff2'),
      url('./nunito/Nunito-Bold.woff') format('woff');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('./nunito/Nunito-ExtraBoldItalic.woff2') format('woff2'),
      url('./nunito/Nunito-ExtraBoldItalic.woff') format('woff');
  font-weight: bold;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('./nunito/Nunito-MediumItalic.woff2') format('woff2'),
      url('./nunito/Nunito-MediumItalic.woff') format('woff');
  font-weight: 500;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('./nunito/Nunito-Light.woff2') format('woff2'),
      url('./nunito/Nunito-Light.woff') format('woff');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('./nunito/Nunito-SemiBold.woff2') format('woff2'),
      url('./nunito/Nunito-SemiBold.woff') format('woff');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('./nunito/Nunito-LightItalic.woff2') format('woff2'),
      url('./nunito/Nunito-LightItalic.woff') format('woff');
  font-weight: 300;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('./nunito/Nunito-ExtraLight.woff2') format('woff2'),
      url('./nunito/Nunito-ExtraLight.woff') format('woff');
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('./nunito/Nunito-Regular.woff2') format('woff2'),
      url('./nunito/Nunito-Regular.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('./nunito/Nunito-Medium.woff2') format('woff2'),
      url('./nunito/Nunito-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('./nunito/Nunito-Italic.woff2') format('woff2'),
      url('./nunito/Nunito-Italic.woff') format('woff');
  font-weight: normal;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('./nunito/Nunito-ExtraLightItalic.woff2') format('woff2'),
      url('./nunito/Nunito-ExtraLightItalic.woff') format('woff');
  font-weight: 200;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('./nunito/Nunito-SemiBoldItalic.woff2') format('woff2'),
      url('./nunito/Nunito-SemiBoldItalic.woff') format('woff');
  font-weight: 600;
  font-style: italic;
  font-display: swap;
}
