[package]
name = "linux_ebpf"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
logger = { path = "../logger" }
tokio = { workspace = true }
thiserror = { workspace = true }
anyhow = { workspace = true }
chrono = { workspace = true }
serde = { workspace = true }
libc = "0.2.174"

[target.'cfg(target_os = "linux")'.dependencies]
pulsar = { git = "https://github.com/exein-io/pulsar", rev = "bd43bc2ee92b8e0460971fa4bdb4fc6849351a45", default-features = false, features = [
  "sqlite3-vendored",
  "process-monitor",
] }
bpf-common = { git = "https://github.com/exein-io/pulsar", rev = "bd43bc2ee92b8e0460971fa4bdb4fc6849351a45" }
pulsar-core = { git = "https://github.com/exein-io/pulsar", rev = "bd43bc2ee92b8e0460971fa4bdb4fc6849351a45" }

[target.'cfg(target_os = "linux")'.build-dependencies]
bpf-builder = { git = "https://github.com/exein-io/pulsar", rev = "bd43bc2ee92b8e0460971fa4bdb4fc6849351a45" }
