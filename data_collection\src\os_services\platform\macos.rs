use crate::{
    execute_in_thread_pool, os_services::os_service::OsService, platform::get_all_services,
};
use rayon::iter::{IntoParallelIterator, ParallelIterator};
use std::collections::HashSet;
use utils::shutdown::is_system_running;

pub fn get_os_services() -> HashSet<OsService> {
    execute_in_thread_pool(|| {
        get_all_services()
            .into_par_iter()
            .take_any_while(|_| is_system_running())
            .map(|item| OsService {
                name: item.name,
                status: item.current_status,
                cmdline: item.path,
                start_type: item.service_status,
                ..Default::default()
            })
            .collect()
    })
}
