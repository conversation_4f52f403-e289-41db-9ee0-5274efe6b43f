use api::{
    file::download,
    self_service::{create_deployment, get_softwares as get_softwares_api},
    ApiResponseWithCount,
};
use async_speed_limit::Limiter;
use database::models::FileAttachment;
use logger::debug;
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use std::{
    f64::INFINITY,
    sync::{Arc, Mutex},
};
use tauri::{AppHand<PERSON>, Manager, State};

use crate::app_state::AppState;

#[derive(Debug, Clone, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
#[allow(dead_code)]
pub struct Software {
    id: usize,
    name: String,
    display_name: String,
    description: String,
    pkg_type: String,
    os: String,
    version: String,
    os_arch: String,
    icon_file: Option<FileAttachment>,
    self_service_supported: bool,
}

#[tauri::command]
pub async fn get_softwares(
    handle: AppHandle,
    payload: Value,
) -> Result<ApiResponseWithCount<Vec<Software>>, String> {
    debug!("Received Payload {payload:?}");

    let images_path = handle
        .path()
        .app_cache_dir()
        .map_err(|e| e.to_string())?
        .join("images");

    let softwares = get_softwares_api::<Vec<Software>>(payload)
        .await
        .map_err(|e| e.to_string())?;

    debug!("Got softwares {:?}", softwares);

    for software in softwares.result.iter() {
        if software.icon_file.is_some() {
            debug!("Downloading Software Image: {:?}", software.icon_file);
            let mut attachment = software.icon_file.as_ref().unwrap().clone();
            attachment.local_path = Some(Box::from(images_path.clone()));
            download(attachment, Limiter::new(INFINITY)).await.ok();
        }
    }

    Ok(softwares)
}

#[tauri::command]
pub async fn trigger_software_installation(
    state: State<'_, Arc<Mutex<AppState>>>,
    software: Software,
) -> Result<(), String> {
    debug!("Received install request for {software:?}");

    let app_state = state.lock().unwrap().clone();

    let response = create_deployment(json!({
        "IsPkgSelectAsBundle": false,
        "refModel": "package",
        "displayName": format!("SS: Install Package {}", software.display_name),
        "description": format!("SS: Install Package {}", software.display_name),
        "deploymentType": "install",
        "deploymentStage": "initiated",
        "refIds": [software.id],
        "scope": 2,
        "assets": [app_state.agent.get_endpoint_id()],
        "deploymentPolicyId": 1,
        "notifyEmailIds": [],
        "retryCount": 1
    }))
    .await
    .map_err(|e| e.to_string())?;

    debug!("Got response of deployment creation {response:?}");

    Ok(())
}
