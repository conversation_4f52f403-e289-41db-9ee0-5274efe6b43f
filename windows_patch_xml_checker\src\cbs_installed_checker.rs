use std::fmt::Display;

use logger::debug;
use windows_registry::WinRegistry;

#[derive(Debug, PartialEq)]
pub enum CbsState {
    Absent,
    UninstallPending,
    Resolving,
    Resolved,
    Staging,
    Staged,
    Superseded,
    InstallPending,
    Installed,
    Permanent,
    Unknown,
}

impl Display for CbsState {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            CbsState::Absent => f.write_str("Absent"),
            CbsState::UninstallPending => f.write_str("UninstallPending"),
            CbsState::Resolving => f.write_str("Resolving"),
            CbsState::Resolved => f.write_str("Resolved"),
            CbsState::Staging => f.write_str("Staging"),
            CbsState::Staged => f.write_str("Staged"),
            CbsState::Superseded => f.write_str("Superseded"),
            CbsState::InstallPending => f.write_str("InstallPending"),
            CbsState::Installed => f.write_str("Installed"),
            CbsState::Permanent => f.write_str("Permanent"),
            CbsState::Unknown => f.write_str("Unknown"),
        }
    }
}

impl From<u8> for CbsState {
    fn from(value: u8) -> Self {
        match value {
            0 => CbsState::Absent,
            5 => CbsState::UninstallPending,
            16 => CbsState::Resolving,
            32 => CbsState::Resolved,
            48 => CbsState::Staging,
            64 => CbsState::Staged,
            80 => CbsState::Superseded,
            96 => CbsState::InstallPending,
            112 => CbsState::Installed,
            128 => CbsState::Permanent,
            _ => CbsState::Unknown,
        }
    }
}

pub struct CbsInstalledChecker<'a> {
    kb: &'a String,
    wmic_output: &'a Vec<String>,
}

impl<'a> CbsInstalledChecker<'a> {
    pub fn new(kb: &'a String, wmic_output: &'a Vec<String>) -> Self {
        Self { kb, wmic_output }
    }

    fn from_wmic(&self) -> u8 {
        if self.wmic_output.contains(self.kb) {
            112
        } else {
            0
        }
    }

    pub fn is_installed(&self) -> bool {
        let cbs_state: CbsState = WinRegistry::get_cbs_status_for_kb(self.kb).into();
        debug!("Received CBS State for kb {} as {:?}", self.kb, cbs_state);
        if cbs_state == CbsState::Installed || cbs_state == CbsState::Superseded {
            true
        } else if cbs_state == CbsState::Staged || cbs_state == CbsState::InstallPending {
            debug!("Package will be completely installed after system reboot.");
            true
        } else if cbs_state == CbsState::UninstallPending {
            debug!("Package will be completely uninstalled after system reboot.");
            false
        } else {
            if Into::<CbsState>::into(self.from_wmic()) == CbsState::Installed {
                true
            } else {
                false
            }
        }
    }
}
