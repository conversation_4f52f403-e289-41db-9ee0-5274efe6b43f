use crate::dir::get_current_dir;
use anyhow::{anyhow, Result};
use std::{
    fs,
    path::{Path, PathBuf},
};
use tracing::{debug, error, info};

pub async fn copy_installation_files(
    source_dir: &Path,
    destination_dir: &Path,
    excluded_files: Vec<&str>,
) -> Result<()> {
    // install services
    let files = fs::read_dir(source_dir)?;

    for source_file in files.into_iter() {
        if source_file.as_ref().is_ok_and(|f| f.path().is_dir()) {
            continue;
        }
        let file = source_file?
            .path()
            .file_name()
            .unwrap()
            .to_string_lossy()
            .to_string();
        if file == "install.ini" || file == "build.txt" {
            continue;
        }
        if excluded_files.contains(&file.as_str()) {
            debug!("Skipping file {} because it is in excluded files", file);
        } else {
            let destination_path = PathBuf::from(&destination_dir).join(&file);
            if source_dir.join(&file).exists() == false {
                error!(
                    "File {} doesn't exist",
                    get_current_dir().join(&file).display()
                );
                continue;
            }
            match std::fs::copy(source_dir.join(&file), &destination_path) {
                Ok(_) => {
                    #[cfg(not(windows))]
                    {
                        use std::fs::set_permissions;
                        use std::os::unix::fs::PermissionsExt;

                        match set_permissions(&destination_path, PermissionsExt::from_mode(0o775)) {
                            Ok(_) => debug!("Set permissions for file {}", file),
                            Err(error) => {
                                error!(?error, "Failed to set permission for file {}", file);
                                return Err(
                                    anyhow!("Failed to set permission for file {}", file).into()
                                );
                            }
                        }

                        #[cfg(target_os = "macos")]
                        {
                            use std::process::Command;

                            if let Err(error) = Command::new("xattr")
                                .arg("-r")
                                .arg("-d")
                                .arg("com.apple.quarantine")
                                .arg(destination_path.as_os_str())
                                .output()
                            {
                                error!(
                                    ?error,
                                    "Failed to remove quarantine attr to file {}",
                                    destination_path.display()
                                );
                            }
                        }
                    }
                    debug!("File {} copied", file);
                }
                Err(error) => {
                    error!(?error, "Failed to copy file {}", file);
                    return Err(anyhow!("Failed to copy file {}", file).into());
                }
            };
        }
    }

    info!("Files copied successfully");
    Ok(())
}
