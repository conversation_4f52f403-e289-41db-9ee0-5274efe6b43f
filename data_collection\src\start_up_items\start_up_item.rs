use super::platform::get_start_up_items;
use logger::debug;
use serde::Serialize;
use std::collections::HashSet;

#[derive(Debug, Serialize, Default, Eq, Hash, PartialEq)]
pub enum StartUpItemStatus {
    #[default]
    Enable,
    Disable,
}

impl From<String> for StartUpItemStatus {
    fn from(value: String) -> Self {
        match value.to_lowercase().as_str() {
            "active" => Self::Enable,
            "inactive" => Self::Disable,
            "enable" => Self::Enable,
            "disable" => Self::Disable,
            "running" => Self::Enable,
            "not running" => Self::Disable,
            "stopped" => Self::Disable,
            _ => Self::Disable,
        }
    }
}

#[derive(Debug, Serialize, Default, Eq, PartialEq, Hash)]
pub struct StartUpItem {
    name: String,
    path: String,
    status: StartUpItemStatus,
}

impl From<bool> for StartUpItemStatus {
    fn from(value: bool) -> Self {
        match value {
            true => StartUpItemStatus::Enable,
            false => StartUpItemStatus::Disable,
        }
    }
}

impl StartUpItem {
    pub fn name(mut self, name: String) -> Self {
        self.name = name;
        self
    }

    #[cfg(windows)]
    pub fn get_name(&self) -> &str {
        &self.name
    }

    pub fn path(mut self, path: String) -> Self {
        self.path = path;
        self
    }

    pub fn status(mut self, status: StartUpItemStatus) -> Self {
        self.status = status;
        self
    }

    pub fn collect() -> HashSet<Self> {
        let start_up_items = get_start_up_items();

        debug!("Collected total {} start up items", start_up_items.len());

        start_up_items
    }
}
