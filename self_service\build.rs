use std::process::Command;

fn main() {
    // Get current Git commit hash
    let commit = Command::new("git")
        .args(["rev-parse", "HEAD"])
        .output()
        .map(|o| String::from_utf8_lossy(&o.stdout).trim().to_string())
        .unwrap_or_else(|_| "unknown".into());

    // Get current branch or tag name
    let describe = Command::new("git")
        .args(["describe", "--tags", "--always", "--dirty"])
        .output()
        .map(|o| String::from_utf8_lossy(&o.stdout).trim().to_string())
        .unwrap_or_else(|_| "unknown".into());

    println!("cargo:rustc-env=GIT_COMMIT_HASH={}", commit);
    println!("cargo:rustc-env=GIT_BRANCH_OR_TAG={}", describe);

    if cfg!(target_os = "windows") {
        // Include your custom manifest.xml
        let manifest = include_str!("../endpointops/endpointops.manifest");
        let windows_attributes =
            tauri_build::WindowsAttributes::new().app_manifest(manifest.to_string());

        let attributes = tauri_build::Attributes::new().windows_attributes(windows_attributes);

        tauri_build::try_build(attributes).expect("failed to run build script");
    } else {
        tauri_build::build();
    }
}
