use super::TaskStatus;
use crate::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>};
use async_trait::async_trait;
use logger::error;
use serde::{Deserialize, Serialize};
use surrealdb::Uuid;

#[derive(Debug, Deserialize, Serialize, <PERSON><PERSON>, <PERSON><PERSON>ult)]
pub struct RedhatNominationHistory {
    id: PrimaryKey,
    last_performed_at: i64,
    pub sslcacert_hash: String,
    pub sslclientkey_hash: String,
    pub sslclientcert_hash: String,
    pub next_run_remaining_sec: i64,
    scan_status: TaskStatus,
}

impl RedhatNominationHistory {
    pub fn new(
        scan_status: TaskStatus,
        sslcacert_hash: String,
        sslclientkey_hash: String,
        sslclientcert_hash: String,
        next_run_remaining_sec: i64,
    ) -> Self {
        Self {
            scan_status,
            sslcacert_hash,
            sslclientkey_hash,
            sslclientcert_hash,
            next_run_remaining_sec,
            last_performed_at: chrono::Utc::now().timestamp(),
            id: PrimaryKey::LocalId(Uuid::new_v4().to_string()),
        }
    }

    pub fn last_performed_at(&self) -> i64 {
        self.last_performed_at
    }

    pub async fn get_last_performed_record() -> Option<Self> {
        let default_history = Self::default();
        match DB
            .query(format!(
                "select * from {} order by last_performed_at desc limit 1",
                default_history.table_name()
            ))
            .await
        {
            Ok(mut response) => match response.take(0) {
                Ok(history) => history,
                Err(error) => {
                    error!(?error, "Failed to unwrap response from database");
                    None
                }
            },
            Err(error) => {
                error!(?error, "Failed to get scheduled task from database");
                None
            }
        }
    }
}

impl HasPrimaryKey for RedhatNominationHistory {
    fn table_name(&self) -> &str {
        "redhat_nomination_history"
    }

    fn get_primary_key(&self) -> &PrimaryKey {
        &self.id
    }
}

#[async_trait]
impl Model for RedhatNominationHistory {}
