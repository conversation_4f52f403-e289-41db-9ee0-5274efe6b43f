use crate::{Action, AuthEvent, AuthEventError, AuthEventMonitor};
use async_trait::async_trait;
use chrono::Utc;
use logger::{debug, error, trace};
use std::{
    collections::HashMap,
    ffi::c_void,
    sync::{Mutex, OnceLock},
};
use tokio::sync::mpsc::Sender;
use utils::shutdown::get_shutdown_signal;
use windows::{core::w, Win32::System::EventLog::*};

// Global storage for the sender - this is a workaround for the callback limitation
static GLOBAL_SENDER: Mutex<Option<Sender<AuthEvent>>> = Mutex::new(None);

// Deduplication cache: stores (username, action, ip) -> last_seen_timestamp
// This helps avoid sending duplicate events that occur within a short time window
static EVENT_CACHE: OnceLock<Mutex<HashMap<(String, String, String), u64>>> = OnceLock::new();

pub struct WindowsMonitor;

#[async_trait]
impl AuthEventMonitor for WindowsMonitor {
    async fn monitor(&mut self, sender: Sender<AuthEvent>) -> Result<(), AuthEventError> {
        let mut shutdown_recv = get_shutdown_signal();
        // Store the sender in global storage
        {
            let mut global_sender = GLOBAL_SENDER.lock().unwrap();
            *global_sender = Some(sender);
        }

        let subscription = unsafe {
            EvtSubscribe(
                None,
                None,
                w!("Security"),
                w!("*[System[(EventID=4624 or EventID=4625 or EventID=4648)]]"),
                None,
                None,
                Some(subscription_callback),
                EvtSubscribeToFutureEvents.0,
            )
        };

        let handle = match subscription {
            Ok(sub) => sub,
            Err(error) => {
                error!(?error, "Failed to subscribe to event log");
                shutdown_recv.recv().await.ok();
                return Err(AuthEventError::FailedToSubscribeToEventLog(error));
            }
        };

        if handle.0 == 0 {
            error!("EvtSubscribe function returned invalid handle");
            // Clean up the global sender before returning
            {
                let mut global_sender = GLOBAL_SENDER.lock().unwrap();
                *global_sender = None;
            }
            shutdown_recv.recv().await.ok();
            return Err(AuthEventError::FailedToSubscribeToEventLog(
                windows::core::Error::from_win32(),
            ));
        }

        // Wait for shutdown signal
        shutdown_recv.recv().await.ok();

        // Clean up the subscription handle
        unsafe {
            let _ = EvtClose(handle);
        }

        // Clean up the global sender
        {
            let mut global_sender = GLOBAL_SENDER.lock().unwrap();
            *global_sender = None;
        }

        Ok(())
    }
}

#[no_mangle]
unsafe extern "system" fn subscription_callback(
    action: EVT_SUBSCRIBE_NOTIFY_ACTION,
    _ctx: *const c_void,
    h_evt: EVT_HANDLE,
) -> u32 {
    if action != EvtSubscribeActionDeliver {
        error!("Subscribe error or other action: {:?}", action);
        return 1;
    }

    // Query the required buffer size - this call is expected to fail with ERROR_INSUFFICIENT_BUFFER
    let mut needed = 0_u32;
    let mut props = 0_u32;
    let result = unsafe {
        // First call to get buffer size (expected to fail)
        EvtRender(
            None,                // Context (not needed for EventXml)
            h_evt,               // Event handle
            EvtRenderEventXml.0, // Render flags
            0,                   // Buffer size (0 for first call to get size)
            None,                // Buffer pointer (null for first call)
            &mut needed,         // Required buffer size (output)
            &mut props,          // Property count (output)
        )
    };

    // The first call is expected to fail with ERROR_INSUFFICIENT_BUFFER (0x8007007A)
    // We only care about getting the required buffer size
    if let Err(error) = result {
        // Check if it's the expected error (ERROR_INSUFFICIENT_BUFFER)
        let error_code = error.code().0;
        if error_code != 0x8007007A_u32 as i32 {
            error!(?error, "Unexpected error getting buffer size for event XML");
            return 1;
        }
    }

    // Check if we got a valid buffer size
    if needed == 0 {
        error!("Failed to get buffer size for event XML");
        return 1;
    }

    // Allocate buffer with the required size (in bytes, not u16s)
    let mut buf: Vec<u8> = vec![0; needed as usize];
    let mut actual_used = needed;

    // Second call to actually get the data
    if let Err(error) = unsafe {
        EvtRender(
            None,                             // Context
            h_evt,                            // Event handle
            EvtRenderEventXml.0,              // Render flags
            needed,                           // Buffer size
            Some(buf.as_mut_ptr() as *mut _), // Buffer pointer
            &mut actual_used,                 // Actual bytes used
            &mut props,                       // Property count
        )
    } {
        error!(?error, "EvtRender failed");
        return 1;
    }

    // Convert from UTF-16 bytes to String
    let utf16_slice =
        std::slice::from_raw_parts(buf.as_ptr() as *const u16, (actual_used as usize) / 2);

    // Find the null terminator and create string
    let null_pos = utf16_slice
        .iter()
        .position(|&x| x == 0)
        .unwrap_or(utf16_slice.len());
    let xml = String::from_utf16_lossy(&utf16_slice[..null_pos]);

    // Get the sender from global storage
    let sender_opt = {
        let global_sender = GLOBAL_SENDER.lock().unwrap();
        global_sender.clone()
    };

    if let Some(sender) = sender_opt {
        process_authentication_event(&xml, Utc::now().timestamp(), &sender);
    } else {
        error!("No sender available in subscription callback");
    }

    // Note: Don't close h_evt here - it's managed by the Windows Event Log system
    // The event handle will be automatically cleaned up by the system

    0
}

fn process_authentication_event(xml: &str, timestamp: i64, sender: &Sender<AuthEvent>) {
    // Only process authentication-related events
    if xml.contains("<EventID>4624</EventID>") || xml.contains("<EventID>4648</EventID>") {
        let (user, ip, logon_type, domain) = parse_user_ip_logontype(xml);
        let logon_desc = get_logon_type_description(&logon_type);

        if !is_builtin_account(&user, &domain) && is_real_user_logon_type(&logon_type) {
            // Check for duplicate events
            if !is_duplicate_event(&user, "Login", &ip, timestamp as u64) {
                let mut event = AuthEvent::new(Action::Login, user.clone(), ip.clone());
                event.set_event_time(timestamp);
                event.generate_id();

                // Try to send the event - use blocking send since we're in a callback
                if let Err(_) = sender.try_send(event) {
                    error!("Failed to send login event to channel");
                }

                debug!(
                    "{} ✅ Login: user='{}' from IP={} ({})",
                    timestamp, user, ip, logon_desc
                );
            } else {
                trace!(
                    "{} 🔄 Duplicate login event filtered: user='{}' from IP={} ({})",
                    timestamp,
                    user,
                    ip,
                    logon_desc
                );
            }
        } else {
            // Uncomment the lines below to see filtered events
            if is_builtin_account(&user, &domain) {
                trace!(
                    "{} 🔇 Filtered built-in login: user='{}' ({})",
                    timestamp,
                    user,
                    logon_desc
                );
            } else {
                trace!(
                    "{} 🔇 Filtered logon type: user='{}' type={} ({})",
                    timestamp,
                    user,
                    logon_type,
                    logon_desc
                );
            }
        }
    } else if xml.contains("<EventID>4634</EventID>") || xml.contains("<EventID>4647</EventID>") {
        let (user, ip, logon_type, domain) = parse_user_ip_logontype(xml);
        let logon_desc = get_logon_type_description(&logon_type);

        if !is_builtin_account(&user, &domain) && is_real_user_logon_type(&logon_type) {
            // Check for duplicate events
            if !is_duplicate_event(&user, "Logout", &ip, timestamp as u64) {
                let mut event = AuthEvent::new(Action::Logout, user.clone(), ip.clone());
                event.set_event_time(timestamp);
                event.generate_id();

                // Try to send the event - use blocking send since we're in a callback
                if let Err(_) = sender.try_send(event) {
                    error!("Failed to send logout event to channel");
                }

                debug!(
                    "{} 🔒 Logout: user='{}' from IP={} ({})",
                    timestamp, user, ip, logon_desc
                );
            } else {
                trace!(
                    "{} 🔄 Duplicate logout event filtered: user='{}' from IP={} ({})",
                    timestamp,
                    user,
                    ip,
                    logon_desc
                );
            }
        } else {
            // Uncomment the lines below to see filtered events
            if is_builtin_account(&user, &domain) {
                trace!(
                    "{} 🔇 Filtered built-in logout: user='{}' ({})",
                    timestamp,
                    user,
                    logon_desc
                );
            } else {
                trace!(
                    "{} 🔇 Filtered logon type: user='{}' type={} ({})",
                    timestamp,
                    user,
                    logon_type,
                    logon_desc
                );
            }
        }
    } else if xml.contains("<EventID>4625</EventID>") {
        let (user, ip, logon_type, domain) = parse_user_ip_logontype(xml);
        let logon_desc = get_logon_type_description(&logon_type);

        if !is_builtin_account(&user, &domain) && is_real_user_logon_type(&logon_type) {
            // Check for duplicate events
            if !is_duplicate_event(&user, "LoginFailed", &ip, timestamp as u64) {
                let mut event = AuthEvent::new(Action::LoginFailed, user.clone(), ip.clone());
                event.set_event_time(timestamp);
                event.generate_id();

                // Try to send the event - use blocking send since we're in a callback
                if let Err(_) = sender.try_send(event) {
                    error!("Failed to send failed login event to channel");
                }

                debug!(
                    "{} ❌ Failed login: user='{}' from IP={} ({})",
                    timestamp, user, ip, logon_desc
                );
            } else {
                trace!(
                    "{} 🔄 Duplicate failed login event filtered: user='{}' from IP={} ({})",
                    timestamp,
                    user,
                    ip,
                    logon_desc
                );
            }
        } else {
            // Uncomment the lines below to see filtered events
            if is_builtin_account(&user, &domain) {
                trace!(
                    "{} 🔇 Filtered built-in failed login: user='{}' ({})",
                    timestamp,
                    user,
                    logon_desc
                );
            } else {
                trace!(
                    "{} 🔇 Filtered logon type: user='{}' type={} ({})",
                    timestamp,
                    user,
                    logon_type,
                    logon_desc
                );
            }
        }
    }
    // Ignore other events silently
}

fn parse_user_ip_logontype(xml: &str) -> (String, String, String, String) {
    let user = xml
        .split("<Data Name='TargetUserName'>")
        .nth(1)
        .and_then(|s| s.split("</Data>").next())
        .unwrap_or("unknown")
        .to_string();

    let domain = xml
        .split("<Data Name='TargetDomainName'>")
        .nth(1)
        .and_then(|s| s.split("</Data>").next())
        .unwrap_or("unknown")
        .to_string();

    let ip = xml
        .split("<Data Name='IpAddress'>")
        .nth(1)
        .and_then(|s| s.split("</Data>").next())
        .unwrap_or("")
        .to_string();

    let logon_type = xml
        .split("<Data Name='LogonType'>")
        .nth(1)
        .and_then(|s| s.split("</Data>").next())
        .unwrap_or("unknown")
        .to_string();

    (user, ip, logon_type, domain)
}

fn is_builtin_account(username: &str, domain: &str) -> bool {
    // Convert to lowercase for case-insensitive comparison
    let username_lower = username.to_lowercase();
    let domain_lower = domain.to_lowercase();

    // List of built-in Windows accounts to filter out
    let builtin_accounts = [
        // System accounts
        "system",
        "local service",
        "network service",
        "anonymous logon",
        "iusr",
        "iwam",
        // Service accounts (common patterns)
        "nt authority\\system",
        "nt authority\\local service",
        "nt authority\\network service",
        "nt authority\\anonymous logon",
        // Common service accounts
        "aspnet",
        "iis_iusrs",
        "iis_wpg",
        "sqlserveragent",
        "sqlserver",
        "mssqlserver",
        // Windows default accounts
        "defaultaccount",
        "wdagutilityaccount",
        "defaultuser0",
        "defaultuser100",
        // Driver host and window manager related
        "driverhost",
        "driver host process",
        "winlogon",
        "csrss",
        "smss",
        "wininit",
        "lsass",
        "services",
        "spoolsv",
        "explorer",
        "dwm",
        "desktop window manager",
        "audiodg",
        "conhost",
        "svchost",
        // Empty or unknown
        "unknown",
        "",
        "-",
    ];

    // Check against the list of built-in accounts
    if builtin_accounts.contains(&username_lower.as_str()) {
        return true;
    }

    // Check for machine accounts (computer accounts ending with $)
    if username_lower.ends_with('$') {
        return true;
    }

    // Check for NT AUTHORITY accounts
    if username_lower.starts_with("nt authority\\") || domain_lower.starts_with("nt authority") {
        return true;
    }

    // Check for service accounts with common patterns
    if username_lower.starts_with("nt service\\") || domain_lower.starts_with("nt service") {
        return true;
    }

    // Check for virtual accounts
    if username_lower.starts_with("nt virtual machine\\")
        || domain_lower.starts_with("nt virtual machine")
    {
        return true;
    }

    // Check for container accounts
    if username_lower.starts_with("user manager\\") || domain_lower.starts_with("user manager") {
        return true;
    }

    // Check for driver host and window manager patterns
    if username_lower.contains("driver") && username_lower.contains("host") {
        return true;
    }

    if (username_lower.contains("window") && username_lower.contains("manager"))
        || (domain_lower.contains("window") && domain_lower.contains("manager"))
    {
        return true;
    }

    if (username_lower.contains("desktop") && username_lower.contains("window"))
        || (domain_lower.contains("desktop") && domain_lower.contains("window"))
    {
        return true;
    }

    // Check for common Windows process patterns
    if username_lower.starts_with("font driver host\\")
        || domain_lower.starts_with("font driver host")
    {
        return true;
    }

    if username_lower.starts_with("window manager\\") || domain_lower.starts_with("window manager")
    {
        return true;
    }

    // Check for accounts that look like GUIDs or have special patterns
    if username_lower.len() > 30
        && username_lower.contains('-')
        && username_lower
            .chars()
            .all(|c| c.is_ascii_hexdigit() || c == '-')
    {
        return true;
    }

    false
}

fn is_real_user_logon_type(logon_type: &str) -> bool {
    match logon_type {
        "2" => true,  // Interactive (console logon)
        "3" => true,  // Network (SMB, file shares, etc.)
        "10" => true, // RemoteInteractive (RDP, Terminal Services)
        "11" => true, // CachedInteractive (cached domain credentials)

        // Filter out these logon types:
        "0" => false,  // System (used only by the System account)
        "1" => false,  // Interactive (not used in practice)
        "4" => false,  // Batch (scheduled tasks)
        "5" => false,  // Service (Windows services)
        "7" => false,  // Unlock (workstation unlock)
        "8" => false,  // NetworkCleartext (IIS with basic auth)
        "9" => false,  // NewCredentials (RunAs with /netonly)
        "12" => false, // CachedRemoteInteractive (cached RDP)
        "13" => false, // CachedUnlock (cached unlock)

        // Unknown logon types - be conservative and filter out
        _ => false,
    }
}

fn get_logon_type_description(logon_type: &str) -> &'static str {
    match logon_type {
        "0" => "System",
        "1" => "Interactive",
        "2" => "Interactive",
        "3" => "Network",
        "4" => "Batch",
        "5" => "Service",
        "7" => "Unlock",
        "8" => "NetworkCleartext",
        "9" => "NewCredentials",
        "10" => "RemoteInteractive",
        "11" => "CachedInteractive",
        "12" => "CachedRemoteInteractive",
        "13" => "CachedUnlock",
        _ => "Unknown",
    }
}

/// Check if an event is a duplicate within a 5-second window
/// This helps reduce noise from Windows generating multiple events for the same authentication
fn is_duplicate_event(username: &str, action: &str, ip: &str, timestamp: u64) -> bool {
    const DEDUP_WINDOW_SECONDS: u64 = 5;

    let key = (username.to_string(), action.to_string(), ip.to_string());

    // Get or initialize the cache
    let cache_mutex = EVENT_CACHE.get_or_init(|| Mutex::new(HashMap::new()));
    let mut cache = cache_mutex.lock().unwrap();

    if let Some(&last_seen) = cache.get(&key) {
        if timestamp.saturating_sub(last_seen) < DEDUP_WINDOW_SECONDS {
            // This is a duplicate within the time window
            return true;
        }
    }

    // Update the cache with the new timestamp
    cache.insert(key, timestamp);

    // Clean up old entries (older than 1 minute) to prevent memory growth
    cache.retain(|_, &mut last_seen| timestamp.saturating_sub(last_seen) < 60);

    false
}
