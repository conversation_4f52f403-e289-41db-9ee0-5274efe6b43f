use crate::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AuthEventMonitor};
use async_trait::async_trait;
use tokio::sync::mpsc::Sender;
use utils::shutdown::get_shutdown_signal;

pub struct MacMonitor;

#[async_trait]
impl AuthEventMonitor for MacMonitor {
    async fn monitor(&mut self, _sender: Sender<AuthEvent>) -> Result<(), AuthEventError> {
        get_shutdown_signal().recv().await.ok();
        //@TODO implementation is remaining
        Ok(())
    }
}
