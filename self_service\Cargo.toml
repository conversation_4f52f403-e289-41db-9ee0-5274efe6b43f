[package]
name = "self_service"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "self_service_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

# [[bin]]
# name = "dev_run"
# path = "src/main.rs"

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
logger = { path = "../logger" }
api = { path = "../api" }
database = { path = "../database" }
utils = { path = "../utils" }
data_collection = { path = "../data_collection" }
ipc = { path = "../ipc" }
async-speed-limit = { workspace = true }
anyhow = { workspace = true }
tokio = { workspace = true }
tauri = { version = "2", features = [
  "macos-private-api",
  "protocol-asset",
  "tray-icon",
] }
serde = { workspace = true }
serde_json = { workspace = true }
tauri-plugin-opener = "2.5.0"
tauri-plugin-fs = "2"
tauri-plugin-os = "2"
tauri-plugin-dialog = "2"
dirs = "6.0.0"

[target.'cfg(windows)'.dependencies]
tauri-winrt-notification = "0.7.2"

[target.'cfg(not(windows))'.dependencies]
notify-rust = "4.11.7"

[target.'cfg(target_os = "macos")'.dependencies]
objc2 = "0.6"
objc2-app-kit = "0.3"
