pub fn force_reboot_command() -> String {
    #[cfg(windows)]
    {
        "shutdown /r /t 0".to_owned()
    }

    #[cfg(not(windows))]
    {
        "shutdown -r now".to_owned()
    }
}

pub fn warn_reboot_command(time: usize, message: &str) -> String {
    #[cfg(windows)]
    {
        format!("shutdown /r /t {} /c \"{}\"", time, message)
    }

    #[cfg(not(windows))]
    {
        format!("shutdown -r +{} '{}'", time, message)
    }
}
