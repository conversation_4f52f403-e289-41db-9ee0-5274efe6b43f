
use std::fs;
use std::io;
use std::path::PathBuf;
use std::process::Command;

fn plist_path(app_name: &str) -> PathBuf {
    PathBuf::from(format!("/Library/LaunchAgents/{}.plist", app_name))
}

pub fn start_now(app_name: &str, _exe_path: &PathBuf) -> io::Result<()> {
    let plist_path = plist_path(app_name);
    // Load immediately for current user
    Command::new("sh")
        .arg("-c")
        .arg(format!(
            r#"for uid in $(/usr/bin/id -u $(/usr/bin/users)); do
    sudo launchctl bootstrap gui/$uid {}
done"#,
            plist_path.display()
        ))
        .status()
        .ok();

    Ok(())
}

pub fn register(app_name: &str, exe_path: &PathBuf) -> io::Result<()> {
    let plist_path = plist_path(app_name);
    let plist_content = format!(
        r#"<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>Label</key>
	<string>{}</string>
	<key>ProgramArguments</key>
	<array>
		<string>{}</string>
		<string>--gui</string>
	</array>
	<key>KeepAlive</key>
	<true/>
	<key>WorkingDirectory</key>
	<string>{}</string>
	<key>RunAtLoad</key>
	<true/>
</dict>
</plist>"#,
        app_name,
        exe_path.display(),
        exe_path.parent().unwrap().display()
    );

    fs::write(&plist_path, plist_content)?;

    Ok(())
}

pub fn deregister(app_name: &str) -> io::Result<()> {
    let plist_path = plist_path(app_name);

    // Unload for current user (others unload at next logout)
    Command::new("sh")
        .arg("-c")
        .arg(format!(
            r#"for uid in $(/usr/bin/id -u $(/usr/bin/users)); do
    sudo launchctl bootout gui/$uid {}
done"#,
            plist_path.display()
        ))
        .status()
        .ok();

    let _ = fs::remove_file(&plist_path);
    Ok(())
}
