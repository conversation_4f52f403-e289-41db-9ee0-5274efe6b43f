{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "lldb",
      "request": "launch",
      "name": "Debug unit tests in library 'database'",
      "cargo": {
        "args": ["test", "--no-run", "--lib", "--package=database"],
        "filter": {
          "name": "database",
          "kind": "lib"
        }
      },
      "args": [],
      "cwd": "${workspaceFolder}"
    },
    {
      "type": "lldb",
      "request": "launch",
      "name": "Debug unit tests in library 'logger'",
      "cargo": {
        "args": ["test", "--no-run", "--lib", "--package=logger"],
        "filter": {
          "name": "logger",
          "kind": "lib"
        }
      },
      "args": [],
      "cwd": "${workspaceFolder}"
    },
    {
      "type": "lldb",
      "request": "launch",
      "name": "Debug 'EndpointOps'",
      "cargo": {
        "args": ["run", "--", "--agent", "-f", "--log=debug"],
        "filter": {
          "name": "endpointops",
          "kind": "bin"
        }
      },
      "args": ["--agent", "-f", "--log=debug"],
      "cwd": "${workspaceFolder}"
    },
    {
      "type": "lldb",
      "request": "launch",
      "name": "Debug unit tests in executable 'endpointops'",
      "cargo": {
        "args": ["test", "--no-run", "--bin=endpointops", "--package=endpointops"],
        "filter": {
          "name": "endpointops",
          "kind": "bin"
        }
      },
      "args": [],
      "cwd": "${workspaceFolder}"
    }
  ]
}
