use logger::{debug, error};
use serde::Deserialize;
use windows_registry::WinRegistry;

use crate::{
    xml_parser::applicability_rules::{
        expression_evaluator::ExpressionEvaluator, version_evaluator::VersionEvaluator,
    },
    WmiOperatingSystemValues,
};

use super::Operator;

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct DeviceAttribute {
    #[serde(rename = "@Name")]
    name: Option<String>,
    // r#type: Option<String>,
    #[serde(rename = "@Comparison")]
    comparison: Option<Operator>,
    #[serde(rename = "@Value")]
    value: Option<String>,
}

impl DeviceAttribute {
    fn match_system_values(&self, system_value: &WmiOperatingSystemValues) -> bool {
        let name = self.name.as_ref().map_or("".to_owned(), |f| f.to_owned());
        match name.as_str() {
            "OSSkuId" | "sku" => ExpressionEvaluator::new(
                system_value.operating_system_sku.to_string().as_str(),
                self.value.as_ref().unwrap(),
                self.comparison.as_ref().unwrap(),
                &name,
            )
            .run(),
            "OSVersion" | "DL_OSVersion" => {
                let system_version = match WinRegistry::new(
                    "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion",
                ) {
                    Ok(registry) => {
                        let revision: u32 = registry.get_value("UBR".to_owned());
                        format!("{}.{}", system_value.version, revision)
                    }
                    _ => system_value.version.clone(),
                };
                VersionEvaluator::new(
                    &system_version,
                    self.value.as_ref().unwrap(),
                    self.comparison.as_ref().unwrap(),
                    &name,
                )
                .run()
            }
            "IsPortableOperatingSystem" => ExpressionEvaluator::new(
                if system_value.portable_operating_system {
                    1
                } else {
                    0
                }
                .to_string()
                .as_str(),
                self.value.as_ref().unwrap(),
                self.comparison.as_ref().unwrap(),
                &name,
            )
            .run(),
            _ => {
                error!("Name attribute found unsupported value {}", name);
                false
            }
        }
    }

    pub fn evaluate(&self, wmi_operating_system: &Option<WmiOperatingSystemValues>) -> bool {
        debug!("Inspecting Device Attribute");
        if self.comparison.is_none() {
            error!("No operator found for the device attribute rule {:?}", self);
            return false;
        }

        if wmi_operating_system.is_some() {
            return self.match_system_values(wmi_operating_system.as_ref().unwrap());
        }
        error!("No wmi_operating_system data found");
        return false;
    }
}
