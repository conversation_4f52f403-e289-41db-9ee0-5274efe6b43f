use chrono::{DateTime, Utc};
use database::models::FIMConfig;
use notify_debouncer_full::notify::Event;
use std::time::SystemTime;

use crate::FIMEvent;

#[derive(Debug, Clone)]
pub struct GenericFIMEvent {
    timestamp: SystemTime,
    pub event: Event,
    pub config: FIMConfig,
}

impl GenericFIMEvent {
    pub fn new(event: Event, config: FIMConfig) -> Self {
        Self {
            timestamp: SystemTime::now(),
            event,
            config,
        }
    }

    pub async fn build_fim_event(&self) -> FIMEvent {
        let mut fs_event = FIMEvent::build_from(self.event.clone(), &self.config).await;
        let event_time: DateTime<Utc> = self.timestamp.into();
        fs_event.generate_id();
        fs_event.set_event_time(event_time.timestamp());
        fs_event
    }
}
