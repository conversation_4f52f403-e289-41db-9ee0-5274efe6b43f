use super::software::Software;
use crate::{DataCollectionError, DataCollectionExtension};
use anyhow::Result;
use async_trait::async_trait;
use database::models::AgentMetadata;
use logger::{debug, ModuleLogger};
use serde_json::{json, Value};
use std::{collections::HashSet, sync::Arc, time::Instant};

#[derive(Debug)]
pub struct Softwares<'a> {
    softwares: HashSet<Software>,
    agent_metadata: &'a AgentMetadata,
}

impl<'a> Softwares<'a> {
    pub fn new(agent_metadata: &'a AgentMetadata) -> Self {
        Self {
            agent_metadata,
            softwares: HashSet::new(),
        }
    }
}

#[async_trait]
impl<'a> DataCollectionExtension for Softwares<'a> {
    fn get_refresh_interval(&self) -> u64 {
        self.agent_metadata
            .get_agent_refresh_settings()
            .sbom_refresh_cycle
    }

    fn get_name(&self) -> &str {
        "softwares_ext"
    }

    fn get_endpoint_id(&self) -> i64 {
        self.agent_metadata.get_endpoint_id()
    }

    fn build_payload(&self) -> Result<Value, DataCollectionError> {
        Ok(json!({
            "asset_id" : self.get_endpoint_id(),
            "data" : json!({
                "software_details": self.softwares
            })
        }))
    }

    async fn collect(&mut self, logger: Arc<ModuleLogger>) -> Result<(), DataCollectionError> {
        self.softwares = tokio::task::spawn_blocking(move || {
            logger.with(|| {
                let time = Instant::now();
                let collection = Software::collect();
                debug!("Time taken for collection {:?}", time.elapsed());
                collection
            })
        })
        .await?;
        Ok(())
    }
}
