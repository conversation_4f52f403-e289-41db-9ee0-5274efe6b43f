use crate::WinRegistryError;
use logger::{debug, error, trace};
use serde_json::{json, Value};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use winreg::enums::{
    HKEY_CLASSES_ROOT, HKEY_CURRENT_CONFIG, HKEY_CURRENT_USER, HKEY_CURRENT_USER_LOCAL_SETTINGS,
    HKEY_DYN_DATA, HKEY_LOCAL_MACHINE, HKEY_PERFORMANCE_DATA, HKEY_PERFORMANCE_NLSTEXT,
    HKEY_PERFORMANCE_TEXT, HKEY_USERS,
};
use winreg::types::FromRegValue;
use winreg::RegKey;

pub struct WinRegistry {
    path: PathBuf,
    registry_instance: RegKey,
}

impl WinRegistry {
    pub fn new<P: AsRef<Path>>(path: P) -> Result<Self, WinRegistryError> {
        let path_segments: Vec<&str> = path.as_ref().to_str().unwrap_or("").split("\\").collect();

        let root = RegKey::predef(match path_segments[0] {
            "HKEY_CLASSES_ROOT" | "HKCR" => HKEY_CLASSES_ROOT,
            "HKEY_CURRENT_USER" | "HKCU" => HKEY_CURRENT_USER,
            "HKEY_LOCAL_MACHINE" | "HKLM" => HKEY_LOCAL_MACHINE,
            "HKEY_USERS" | "HKU" => HKEY_USERS,
            "HKEY_PERFORMANCE_DATA" | "HKPD" => HKEY_PERFORMANCE_DATA,
            "HKEY_PERFORMANCE_TEXT" | "HKPT" => HKEY_PERFORMANCE_TEXT,
            "HKEY_PERFORMANCE_NLSTEXT" | "HKPN" => HKEY_PERFORMANCE_NLSTEXT,
            "HKEY_CURRENT_CONFIG" | "HKCC" => HKEY_CURRENT_CONFIG,
            "HKEY_DYN_DATA" | "HKDD" => HKEY_DYN_DATA,
            "HKEY_CURRENT_USER_LOCAL_SETTINGS" | "HKCULS" => HKEY_CURRENT_USER_LOCAL_SETTINGS,
            _ => {
                error!(
                    "Failed to resolve hive key for {} so using default HKEY_CURRENT_USER",
                    path_segments[0]
                );
                return Err(WinRegistryError::UnknownHiveError(
                    path_segments[0].to_owned(),
                ));
            }
        });
        let registry_instance = if path_segments.len() < 2 {
            root
        } else {
            let path_to_open = PathBuf::from(path_segments[1..].join("\\"));
            match root.open_subkey(&path_to_open) {
                Err(error) => {
                    trace!(?error, "Failed to open sub key {}", path_to_open.display());
                    return Err(WinRegistryError::UnableToOpenSubKey(format!(
                        "sub key {} with error {:?}",
                        path_to_open.display(),
                        error
                    )));
                }
                Ok(instance) => {
                    trace!(
                        "Successfully opened registry {}",
                        format!("{}\\{}", path_segments[0], path_to_open.display())
                    );
                    instance
                }
            }
        };

        Ok(WinRegistry {
            path: path.as_ref().to_owned(),
            registry_instance,
        })
    }

    pub fn keys(&self) -> Vec<String> {
        self.registry_instance
            .enum_keys()
            .into_iter()
            .filter(|item| {
                if item.is_err() {
                    error!(error = ?item.as_ref().err().unwrap(), "Failed to get registry key");
                }
                item.is_ok()
            })
            .map(|item| item.unwrap_or_default())
            .collect()
    }

    pub fn has_value(&self, value: &str) -> bool {
        self.registry_instance
            .enum_values()
            .filter(|i| i.as_ref().is_ok_and(|item| item.0 == value))
            .count()
            > 0
    }

    pub fn get_value<T: FromRegValue + Default>(&self, key: String) -> T {
        match self.registry_instance.get_value::<T, _>(&key) {
            Ok(v) => v,
            Err(error) => {
                error!(?error, "Failed to get value for key {}", key);
                Default::default()
            }
        }
    }

    pub fn get_users_key() -> Vec<String> {
        if let Ok(registry) = WinRegistry::new("HKU") {
            registry.keys()
        } else {
            vec![]
        }
    }

    pub fn get_cbs_status_for_kb(kb: &str) -> u8 {
        debug!("Checking KB {} status in the registry", kb);
        let base_path = "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Component Based Servicing\\Packages";
        match WinRegistry::new(&base_path) {
            Ok(instance) => {
                let available_keys = instance
                    .keys()
                    .into_iter()
                    .filter(|item| item.contains(kb))
                    .collect::<Vec<String>>();
                if available_keys.len() <= 0 {
                    debug!("No key found matching {}", kb);
                    return 0;
                }
                let owner_path = PathBuf::from(base_path).join(&available_keys[0]);
                match WinRegistry::new(&owner_path) {
                    Ok(owner) => owner.get_value::<u32>("CurrentState".to_owned()) as u8,
                    Err(error) => {
                        error!(
                            ?error,
                            "Failed to open owner {} for kb {}",
                            owner_path.display(),
                            kb
                        );
                        0
                    }
                }
            }
            Err(error) => {
                error!(
                    ?error,
                    "Failed to create registry instance for cbs state checking"
                );
                0
            }
        }
    }

    pub fn values(&self, make_keys_lowercase: Option<bool>) -> HashMap<String, String> {
        let mut values = HashMap::new();

        for (key, value) in self
            .registry_instance
            .enum_values()
            .into_iter()
            .map(|x| x.unwrap())
        {
            let key = if key.is_empty() {
                "Default".to_owned()
            } else {
                key
            };
            values.insert(
                if make_keys_lowercase.or(Some(false)).unwrap() {
                    key.to_lowercase()
                } else {
                    key
                },
                value.to_string(),
            );
        }

        values
    }

    pub fn collect_registry(&self) -> Value {
        let mut values: Vec<Value> = vec![];
        for key in self
            .registry_instance
            .enum_keys()
            .into_iter()
            .map(|x| x.unwrap())
        {
            match WinRegistry::new(self.path.join(&key)) {
                Ok(subkey) => {
                    values.push(json!(subkey.values(Some(true))));
                }
                Err(error) => {
                    error!(
                        ?error,
                        "Failed to open registery key {} at path {}",
                        key,
                        self.path.display()
                    );
                }
            }
        }

        json!(values)
    }

    pub fn scan_patches(&self, collect_root_values: bool) -> HashMap<String, Value> {
        let mut output = HashMap::new();
        let product_key = self.path.to_str().unwrap().split("\\").last().unwrap();
        for key_name in self
            .registry_instance
            .enum_keys()
            .into_iter()
            .map(|x| x.unwrap())
        {
            trace!("Processing key {}", key_name);
            match WinRegistry::new(self.path.join(&key_name)) {
                Ok(child_registry) => {
                    if key_name.contains("Patches") {
                        trace!("Adding values for parent key {}", product_key);
                        output.insert(product_key.to_owned(), child_registry.collect_registry());
                    } else {
                        output.extend(child_registry.scan_patches(collect_root_values));
                    }
                }
                Err(error) => {
                    error!(
                        ?error,
                        "Failed to open key {}",
                        format!("{}\\{}", self.path.display(), key_name)
                    );
                }
            };
        }

        if collect_root_values && self.registry_instance.enum_values().count() > 0 {
            output.insert(product_key.to_owned(), json!(self.values(Some(true))));
        }
        output
    }
}
