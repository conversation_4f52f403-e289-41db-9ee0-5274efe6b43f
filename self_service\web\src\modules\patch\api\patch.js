import { invoke } from '@tauri-apps/api/core';
import { platform } from '@tauri-apps/plugin-os';

const sortKeyMap = {
  createdAt: 'createdTime'
};

const searchableColumns = ['patchName', 'title'];

export function getMissingPatchApi(assetId, offset, size, sortFilter) {
  const payload = {
    offset,
    size,
    ...(sortFilter && sortFilter.sort && sortFilter.sort.field
      ? {
          sortBy: `${sortFilter.sort.order === 'ascend' ? `-` : ''}${
            sortKeyMap[sortFilter.sort.field] || sortFilter.sort.field
          }`
        }
      : {}),
    qualification: [
      { operator: 'equals', column: 'assetId', value: assetId, condition: 'and' },
      {
        operator: 'equals',
        column: 'patchState',
        value: 'missing',
        condition: 'and',
        type: 'enum',
        reference: 'patchState'
      },
      ...(sortFilter.searchTerm
        ? [
            {
              operator: 'field_list_contains',
              columns: searchableColumns,
              value: sortFilter.searchTerm
            }
          ]
        : [])
    ]
  };
  return invoke('get_missing_patches', { payload }).then((response) => {
    return {
      totalCount: response.totalCount,
      result: response.result.map((i) => ({ ...i, ...i.patch }))
    };
  });
}

export function installPatchApi(item) {
  return invoke('trigger_patch_installation', {
    patch: item
  });
}
