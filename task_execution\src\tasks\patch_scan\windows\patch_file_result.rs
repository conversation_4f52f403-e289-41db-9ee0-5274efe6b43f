use crate::tasks::patch_scan::PatchFile;
use database::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, PrimaryKey};
use serde::{Deserialize, Serialize};
use windows_patch_xml_checker::{PatchStatus, WindowsUpdate};

#[derive(Debug, <PERSON><PERSON>, Deserialize, Serialize, Default)]
pub struct PersistedPatchFileResult {
    pub uuid: PrimaryKey,
    pub status: PatchStatus,
    pub timestamp: u64,
    pub category: String,
}

impl HasPrimaryKey for PersistedPatchFileResult {
    fn table_name(&self) -> &str {
        "patch_file_result"
    }

    fn get_primary_key(&self) -> &PrimaryKey {
        &self.uuid
    }
}

impl Model for PersistedPatchFileResult {}

impl Into<PersistedPatchFileResult> for PatchFile {
    fn into(self) -> PersistedPatchFileResult {
        PersistedPatchFileResult {
            uuid: self.csv_record.uuid.clone().into(),
            status: self.evaluation_result.unwrap_or(PatchStatus::Unknown),
            timestamp: self.csv_record.timestamp.unwrap_or_default(),
            category: self.csv_record.category,
        }
    }
}

impl Into<PersistedPatchFileResult> for WindowsUpdate {
    fn into(self) -> PersistedPatchFileResult {
        PersistedPatchFileResult {
            uuid: self.get_parsed_package().get_uuid().into(),
            status: self.get_status(),
            timestamp: 0,
            category: self.get_parsed_package().get_update_type().into(),
        }
    }
}
