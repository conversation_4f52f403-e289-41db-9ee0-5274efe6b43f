import { lazy } from 'react';
import { Route, Outlet } from 'react-router-dom';
import PatchLayout from './layout/PatchLayout.jsx';
import NotFound from '../../components/NotFound.jsx';

const Patch = lazy(() => import(/* webpackChunkName: "settings" */ './views/Patch.jsx'));
/**
 *
 * you can pass any config during init and those config can become prop for any of the route component
 */

export default function getRoutes(config) {
  return (
    <Route path="/patch" key="patch" element={<Outlet />}>
      <Route element={<PatchLayout />}>
        <Route index path="" element={<Patch />} />
        <Route path="*" element={<NotFound redirectTo="/" />} />
      </Route>
    </Route>
  );
}
