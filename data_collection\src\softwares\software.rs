use super::{npm_packages::NpmPackage, python_packages::PythonPackage};
use crate::softwares::{chrome_extensions::ChromeExtension, firefox_extensions::FirefoxExtension};
use logger::debug;
use serde::Serialize;
use serde_json::Value;
use std::collections::HashSet;

#[allow(dead_code)]
#[derive(Debug, Serialize, PartialEq, Eq, Hash, Default)]
pub enum PackageType {
    #[serde(rename = "NPM Packages")]
    NpmPackages,
    #[serde(rename = "Windows Programs")]
    WindowsPrograms,
    #[serde(rename = "Python Packages")]
    PythonPackages,
    #[serde(rename = "Chrome Browser Plugin")]
    ChromeBrowserPlugin,
    #[serde(rename = "Firefox Browser Plugin")]
    FirefoxBrowserPlugin,
    #[serde(rename = "Chocolatey Packages")]
    ChocolateyPackages,
    #[serde(rename = "MacOS Applications")]
    MacOSApplications,
    #[serde(rename = "Safari Browser Plugin")]
    SafariBrowserPlugin,
    #[serde(rename = "Homebrew Packages")]
    HomebrewPackages,
    #[serde(rename = "RPM Packages")]
    RpmPackages,
    #[serde(rename = "Debian Packages")]
    DebianPackages,
    #[serde(rename = "IE Browser Plugin")]
    IEExtension,
    #[default]
    Unknown,
}

#[derive(Debug, Serialize, PartialEq, Eq, Hash, Default)]
pub struct Software {
    pub name: String,
    pub version: String,
    pub r#type: PackageType,
    pub bundle_identifier: String,
    pub release: String,
    pub vendor: String,
    pub arch: String,
    pub properties: Value,
}

impl Software {
    pub fn collect() -> HashSet<Self> {
        let mut packages = HashSet::new();

        let firefox_extensions = FirefoxExtension::collect();

        debug!(
            "Collected total {} Firefox Extensions",
            firefox_extensions.len()
        );

        packages.extend(firefox_extensions);

        let npm_packages = NpmPackage::collect();

        debug!("Collected total {} NPM packages", npm_packages.len());

        packages.extend(npm_packages);

        let python_packages = PythonPackage::collect();

        debug!("Collected total {} Python packages", python_packages.len());

        packages.extend(python_packages);

        let chrome_extensions = ChromeExtension::collect();

        debug!(
            "Collected total {} Chrome extensions",
            chrome_extensions.len()
        );

        packages.extend(chrome_extensions);

        #[cfg(not(windows))]
        {
            use crate::softwares::homebrew_packages::HomebrewPackage;

            let homebrew_packages = HomebrewPackage::collect();

            debug!(
                "Collected total {} Homebrew packages",
                homebrew_packages.len()
            );

            packages.extend(homebrew_packages);
        }

        #[cfg(target_os = "macos")]
        {
            use crate::softwares::macos_apps::MacOSApp;
            use crate::softwares::safari_extensions::SafariExtension;

            let macos_packages = MacOSApp::collect();

            debug!("Collected total {} MacOS pakcages", macos_packages.len());

            packages.extend(macos_packages);

            let safari_extensions = SafariExtension::collect();

            debug!(
                "Collected total {} Safari Extensions",
                safari_extensions.len()
            );

            packages.extend(safari_extensions);
        }

        #[cfg(windows)]
        {
            use crate::softwares::chocolatey_packages::ChocolateyPackage;
            use crate::softwares::ie_extensions::IEExtension;
            use crate::softwares::windows_program::WindowsProgram;

            let chocolatey_packages = ChocolateyPackage::collect();

            debug!(
                "Collected total {} Chocolatey Packages",
                chocolatey_packages.len()
            );

            packages.extend(chocolatey_packages);

            let windows_programs = WindowsProgram::collect();

            debug!(
                "Collected total {} Windows Programs",
                windows_programs.len()
            );

            packages.extend(windows_programs);

            let ie_extensions = IEExtension::collect();

            debug!("Collected total {} IE Extensions", ie_extensions.len());

            packages.extend(ie_extensions);
        }

        #[cfg(target_os = "linux")]
        {
            use crate::softwares::deb_packages::DebPackage;
            use crate::softwares::rpm_packages::RpmPackage;

            let deb_packages = DebPackage::collect();

            debug!("Collected total {} DEB Packages", deb_packages.len());

            packages.extend(deb_packages);

            let rpm_packages = RpmPackage::collect();

            debug!("Collected total {} RPM Packages", rpm_packages.len());

            packages.extend(rpm_packages);
        }

        debug!("Collected total {} packages", packages.len());

        packages
    }
}
