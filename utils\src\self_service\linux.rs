use std::fs;
use std::io;
use std::path::Path;
use std::path::PathBuf;
use std::process::Command;

pub fn start_now(_app_name: &str, _exe_path: &PathBuf) -> io::Result<()> {
    Ok(())
}

pub fn register(app_name: &str, exe_path: &PathBuf) -> io::Result<()> {
    std::fs::write(
        PathBuf::from("/usr/share/icons/hicolor/512x512/apps").join("endpointops.png"),
        super::ICON,
    )
    .ok();

    let desktop_entry = format!(
        r#"[Desktop Entry]
Type=Application
Name={0}
Exec={1} --gui
Icon=endpointops
Hidden=false
NoDisplay=false
X-GNOME-Autostart-enabled=true
Comment=EndpointOps on login
X-GNOME-Autostart-Delay=0
"#,
        app_name,
        exe_path.display(),
    );

    let global_app_dir = Path::new("/usr/share/applications");
    let global_autostart_dir = Path::new("/etc/xdg/autostart");

    fs::create_dir_all(global_app_dir)?;
    fs::create_dir_all(global_autostart_dir)?;

    let app_desktop = global_app_dir.join(format!("{app_name}.desktop"));
    let autostart_desktop = global_autostart_dir.join(format!("{app_name}.desktop"));

    fs::write(&app_desktop, &desktop_entry)?;
    fs::write(&autostart_desktop, &desktop_entry)?;

    // Optionally make sure ownership and permissions are correct
    Command::new("chmod")
        .args([
            "644",
            app_desktop.to_str().unwrap(),
            autostart_desktop.to_str().unwrap(),
        ])
        .status()
        .ok();

    Command::new("gtk-update-icon-cache")
        .args(["/usr/share/icons/hicolor"])
        .status()
        .ok();

    Ok(())
}

pub fn deregister(app_name: &str) -> io::Result<()> {
    let global_app_dir = Path::new("/usr/share/applications");
    let global_autostart_dir = Path::new("/etc/xdg/autostart");

    let app_desktop = global_app_dir.join(format!("{app_name}.desktop"));
    let autostart_desktop = global_autostart_dir.join(format!("{app_name}.desktop"));

    fs::remove_file(&app_desktop)?;
    fs::remove_file(&autostart_desktop)?;
    Ok(())
}
