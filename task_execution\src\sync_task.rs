use crate::has_task::HasTask;
use api::task::change_task_status;
use async_trait::async_trait;
use database::{data_types::TaskResult, models::TaskStatus};
use logger::{error, info};

#[async_trait]
pub trait SyncTask: HasTask {
    async fn send_result_to_server(&self, result: &TaskResult) {
        let id = self.get_id();
        let response = change_task_status(&vec![id], &result).await;
        if let Err(error) = response {
            error!(
                ?error,
                "Failed to sync task status to server for task {:?}", id
            );
        } else {
            info!(
                "Task status {} synced to server for {:?}",
                result.status, id
            );
        }
    }

    async fn change_task_status_in_db(&mut self, status: TaskStatus) {
        if self.get_task().should_persist() {
            self.set_task(self.get_task().clone().change_status(status.clone()).await);
        }
    }

    fn decrement_retry_count(&mut self) {
        if let Some(retry_count) = self.get_task().retry_count {
            let mut task = self.get_task().clone();
            task.retry_count = Some(retry_count - 1);
            self.set_task(task);
        }
    }
}
