use super::{CommandType, OutputMatch};
use crate::{Has<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, PrimaryKey};
use logger::error;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use serde_with::serde_as;
use serde_with::NoneAsEmptyString;

#[serde_as]
#[derive(Debu<PERSON>, De<PERSON>ult, Serialize, Deserialize, Hash, PartialEq, Eq, Clone)]
pub struct QuickCheck {
    pub id: PrimaryKey,
    pub command: String,
    #[serde_as(as = "NoneAsEmptyString")]
    pub command_type: Option<CommandType>,
    pub expected_output: Vec<OutputMatch>,
}

impl From<Value> for QuickCheck {
    fn from(value: Value) -> Self {
        match serde_json::from_value::<Self>(value) {
            Ok(data) => data,
            Err(error) => {
                error!(?error, "Failed to convert Value into ComplianceRule");
                QuickCheck::default()
            }
        }
    }
}

impl From<&Value> for QuickCheck {
    fn from(value: &Value) -> Self {
        match serde_json::from_value::<Self>(value.to_owned()) {
            Ok(data) => data,
            Err(error) => {
                error!(?error, "Failed to convert Value into ComplianceRule");
                QuickCheck::default()
            }
        }
    }
}

impl HasPrimaryKey for QuickCheck {
    fn table_name(&self) -> &str {
        "quick_checks"
    }

    fn get_primary_key(&self) -> &PrimaryKey {
        &self.id
    }
}

impl Model for QuickCheck {}
