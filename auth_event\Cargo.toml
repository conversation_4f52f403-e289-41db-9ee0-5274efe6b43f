[package]
name = "auth_event"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[features]
default = []
self_service = []

[dependencies]
logger = { path = "../logger" }
utils = { path = "../utils" }
database = { path = "../database" }
thiserror = { workspace = true }
anyhow = { workspace = true }
cfg-if = { workspace = true }
serde = { workspace = true }
async-trait = { workspace = true }
chrono = { workspace = true }
tokio = { workspace = true }


[target.'cfg(target_os = "linux")'.dependencies]
inotify = "0.11"
tokio-stream = { workspace = true }
regex = { workspace = true }

[target.'cfg(windows)'.dependencies]
windows = { version = "0.61.3", features = [
  "Win32_Foundation",
  "Win32_System_EventLog",
  "Win32_System_WindowsProgramming",
] }
