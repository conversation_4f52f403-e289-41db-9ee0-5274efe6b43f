use crate::<PERSON>Key;
use serde::{Deserialize, Serialize};
use std::fmt::Display;

#[derive(PartialEq, Default, Eq, Serialize, Deserialize, Clone, Debug)]
pub enum DeploymentResourceType {
    #[serde(rename = "configuration")]
    #[default]
    Configuration,
    #[serde(rename = "package")]
    Package,
    #[serde(rename = "patch", alias = "Patch")]
    Patch,
    #[serde(rename = "compliances")]
    Compliance,
}

impl Display for DeploymentResourceType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            DeploymentResourceType::Compliance => f.write_str("compliance"),
            DeploymentResourceType::Configuration => f.write_str("configuration"),
            DeploymentResourceType::Package => f.write_str("package"),
            DeploymentResourceType::Patch => f.write_str("patch"),
        }
    }
}

#[derive(PartialEq, Default, Eq, Serialize, Deserialize, Clone, Debug)]
pub enum DeploymentType {
    #[serde(rename = "install")]
    #[default]
    Install,
    #[serde(rename = "uninstall")]
    Uninstall,
    #[serde(rename = "upgrade")]
    Upgrade,
}

impl Display for DeploymentType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            DeploymentType::Install => f.write_str("install"),
            DeploymentType::Uninstall => f.write_str("uninstall"),
            DeploymentType::Upgrade => f.write_str("upgrade"),
        }
    }
}

#[derive(PartialEq, Default, Eq, Serialize, Deserialize, Clone, Debug)]
pub enum DeploymentStage {
    #[serde(rename = "initiated")]
    #[default]
    Initiated,
    #[serde(rename = "in_progress")]
    InProgress,
    #[serde(rename = "completed")]
    Completed,
    #[serde(rename = "draft")]
    Draft,
    #[serde(rename = "cancelled")]
    Cancelled,
}

impl Display for DeploymentStage {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            DeploymentStage::Initiated => f.write_str("initiated"),
            DeploymentStage::InProgress => f.write_str("in_progress"),
            DeploymentStage::Completed => f.write_str("completed"),
            DeploymentStage::Draft => f.write_str("draft"),
            DeploymentStage::Cancelled => f.write_str("cancelled"),
        }
    }
}

#[derive(Debug, Default, Serialize, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct Deployment {
    pub id: PrimaryKey,
    pub name: Option<String>,
    pub display_name: Option<String>,
    #[serde(rename = "refModel")]
    pub resource_type: DeploymentResourceType,
    pub deployment_type: DeploymentType,
    pub deployment_policy_id: i32,
    pub retry_count: i32,
    pub deployment_stage: DeploymentStage,
}
