use std::num::ParseIntError;

use surrealdb::Error;
use thiserror::Error;

#[derive(Error, Debug)]
pub enum DatabaseError {
    #[error("DB Error: {0}")]
    DB(String),

    #[error("Database Error: Parse Int Error {0:?}")]
    ParseError(#[from] ParseIntError),

    #[error("Database Error: {0:?}")]
    SurrealError(#[from] Error),

    #[error("Database Error: Failed to convert {0}")]
    ConversionError(String),

    #[error("Database Error: Database Save returned None {0:?}")]
    NoneReturnFromDb(String),

    #[error("Database Error: Record Not found {0:?}")]
    NotFoundInDb(String),

    #[error("Please initialise database with DbOptions using Database::init(option) method")]
    Uninitialised,
}
