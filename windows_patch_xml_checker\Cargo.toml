[package]
name = "windows_patch_xml_checker"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[dependencies]
logger = { path = "../logger" }
shell = { path = "../shell" }
database = { path = "../database" }
windows_registry = { path = "../windows_registry" }
utils = { path = "../utils" }
thiserror = { workspace = true }
anyhow = { workspace = true }
serde = { workspace = true }
chrono = { workspace = true }
serde_json = { workspace = true }
quick-xml = { version = "0.38.0", features = ["serialize"] }
evalexpr = "12.0.2"
wmi = "0.17"
version-compare = "0.2.0"
windows = { version = "0.61.3", features = [
  "Win32_UI_Shell",
  "Win32_Foundation",
  "Win32_Storage_FileSystem",
] }
dashmap = { version = "6.1.0", features = ["rayon"] }
regex = { workspace = true }
