use std::{fmt::Debug, path::Path};

#[derive(Default)]
pub struct DbOptions {
    path: String,
    namespace: String,
    db: String,
    username: String,
    password: String,
}

impl Debug for DbOptions {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("DbOptions")
            .field("path", &self.path)
            .finish()
    }
}

impl DbOptions {
    pub fn new<P: AsRef<Path>>(
        path: P,
        namespace: &str,
        db: &str,
        username: &str,
        password: &str,
    ) -> Self {
        Self {
            path: path.as_ref().to_str().unwrap().to_owned(),
            namespace: namespace.to_owned(),
            db: db.to_owned(),
            username: username.to_owned(),
            password: password.to_owned(),
        }
    }

    pub fn path(&self) -> &str {
        &self.path
    }

    pub fn namespace(&self) -> &str {
        &self.namespace
    }

    pub fn db(&self) -> &str {
        &self.db
    }

    pub fn username(&self) -> &str {
        &self.username
    }

    pub fn password(&self) -> &str {
        &self.password
    }
}
