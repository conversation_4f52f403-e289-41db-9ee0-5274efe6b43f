use crate::{get_client, ApiError};
#[cfg(windows)]
use database::models::WindowsPatchFileStat;
use reqwest::multipart::Form;
use serde_json::{json, Value};
use std::{collections::HashMap, path::Path};

pub async fn send_patch_system_scan_result(
    endpoint_id: i64,
    data: &HashMap<String, Value>,
) -> anyhow::Result<Value, ApiError> {
    get_client()?
        .post::<Value, _>(
            {
                cfg_if! {
                    if #[cfg(target_os = "linux")] {
                        format!("/patch/asset/linux/installed-patches/{}", endpoint_id).to_owned()
                    } else {
                        format!("/patch/asset/discovered-patch-data/{}", endpoint_id).to_owned()
                    }
                }
            },
            json!({
                "patchData": data
            }),
        )
        .await
}

pub async fn send_patch_discovered_data(
    endpoint_id: i64,
    data: Value,
) -> anyhow::Result<Value, ApiError> {
    get_client()?
        .post::<Value, _>(
            format!("/patch/asset/discovered-patch-data/{}", endpoint_id).to_owned(),
            data,
        )
        .await
}

#[cfg(target_os = "linux")]
pub async fn get_linux_latest_package_info(
    endpoint_id: i64,
) -> anyhow::Result<HashMap<String, String>, ApiError> {
    get_client()?
        .get::<HashMap<String, String>, _>(
            format!(
                "/patch/asset/linux/prepare/latestPackageInfo/{}",
                endpoint_id
            )
            .to_owned(),
        )
        .await
}

#[cfg(windows)]
pub async fn get_windows_xml_files(
    endpoint_id: u32,
) -> anyhow::Result<HashMap<String, WindowsPatchFileStat>, ApiError> {
    get_client()?
        .post::<HashMap<String, WindowsPatchFileStat>, _>(
            format!("/patch/asset/applicability-rule-filemap/{}", endpoint_id).to_owned(),
            json!({}),
        )
        .await
}

#[cfg(windows)]
pub async fn get_missing_uuids_with_superseeded_removed(
    ids: &Vec<String>,
) -> anyhow::Result<HashMap<String, bool>, ApiError> {
    get_client()?
        .post::<HashMap<String, bool>, _>(
            "/patch/asset/remove-superseded-patch".to_owned(),
            json!({
                "fileList": ids
            }),
        )
        .await
}

pub async fn trigger_patch_scan(asset_id: i64) -> anyhow::Result<String, ApiError> {
    get_client()?
        .post(
            format!("/patch/asset/execute-scan-patch/{}", asset_id),
            json!({}),
        )
        .await
}

pub async fn trigger_redhat_nomination_task(asset_id: i64) -> anyhow::Result<String, ApiError> {
    get_client()?
        .post(
            format!("/patch/asset/execute-nomination/{}", asset_id),
            json!({}),
        )
        .await
}

pub async fn upload_redhat_patch_cache_dir(path: &Path, folder_path: &str) -> Result<(), ApiError> {
    let form_data = Form::new()
        .text("folderpath", folder_path.to_owned())
        .file("file", path)
        .await?;

    get_client()?
        .post_multipart_form::<Value, _>("/patch/redhat-patch/upload", form_data)
        .await?;

    Ok(())
}

pub async fn upload_redhat_ssl_certificates(path: &Path, asset_id: i64) -> Result<(), ApiError> {
    let form_data = Form::new().file("file", path).await?;

    get_client()?
        .post_multipart_form::<Value, _>(
            format!("/patch/redhat-patch/certificate/upload/{}", asset_id),
            form_data,
        )
        .await?;

    Ok(())
}

pub async fn get_third_party_patch_scripts(os_type: &str) -> anyhow::Result<Value, ApiError> {
    get_client()?
        .get::<Value, _>(format!("/patch/third-party-package/metadata/{}", os_type))
        .await
}
