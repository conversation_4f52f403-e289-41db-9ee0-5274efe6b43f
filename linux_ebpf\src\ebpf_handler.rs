use crate::{
    file_system_monitor::pulsar::FileSystemMonitorModule, file_system_monitor::FileRule, FileEvent,
    LinuxEbpfHandlerError,
};
use logger::{error, info, trace, ModuleLogger, WithSubscriber};
use pulsar::cli;
use std::{path::PathBuf, sync::Arc};
use tokio::sync::mpsc::{self, Sender};

pub struct EbpfHandler {
    channel: Sender<FileEvent>,
    paths: Arc<Vec<PathBuf>>,
    blocked_rules: Arc<Vec<FileRule>>,
    exception_rules: Arc<Vec<FileRule>>,
}

impl EbpfHandler {
    pub fn new(channel: Sender<FileEvent>) -> Self {
        Self {
            channel,
            paths: Arc::new(vec![]),
            blocked_rules: Arc::new(vec![]),
            exception_rules: Arc::new(vec![]),
        }
    }

    pub fn with_paths(mut self, paths: Vec<PathBuf>) -> Self {
        self.paths = Arc::new(paths);
        self
    }

    pub fn with_blocked_rules(mut self, blocked_rules: Vec<FileRule>) -> Self {
        self.blocked_rules = Arc::new(blocked_rules);
        self
    }

    pub fn with_exception_rules(mut self, exception_rules: Vec<FileRule>) -> Self {
        self.exception_rules = Arc::new(exception_rules);
        self
    }

    pub async fn start(&self, logger: Arc<ModuleLogger>) -> Result<(), LinuxEbpfHandlerError> {
        info!("---------------------- Starting EBPF Handler ------------------------");

        let (tx, mut rx) = mpsc::channel(100);

        let options = cli::pulsard::PulsarDaemonOpts { config_file: None };

        let included_paths = self.paths.clone();
        let blocked_rules = self.blocked_rules.clone();
        let exception_rules = self.exception_rules.clone();

        let mut proxy_task = tokio::spawn(
            async move {
                // Run pulsar-exec with crate provided modules
                #[allow(clippy::blocks_in_conditions)]
                pulsar::pulsard::pulsar_daemon_run(&options, move |starter| {
                    match starter.add_module(FileSystemMonitorModule {
                        tx,
                        included_paths,
                        blocked_rules,
                        exception_rules,
                    }) {
                        Err(error) => {
                            error!(?error, "Failed to add file system monitor module");
                        }
                        _ => {}
                    };

                    Ok(())
                })
                .await
            }
            .with_subscriber(logger.subscriber()),
        );

        let result;

        loop {
            tokio::select! {
                biased;


                response = &mut proxy_task => {
                    match response {
                        Ok(_) => {
                            info!("EBPF Proxy task has finished execution");
                            result = Ok(());
                            break;
                        }
                        Err(error) => {
                            error!(?error, "Failed to finish ebpf proxy task");
                            result = Err(error.into());
                            break;
                        }
                    }
                }

                message = rx.recv() => {
                    if let Some(message) = message {
                        trace!("Got event from ebpf {message:?}");
                        if let Err(error) = self.channel.send(message).await {
                            error!(?error, "Failed to forward event to main watcher {:?}", error.0);
                        }
                    }
                }
            }
        }

        // let result = ;

        info!("Linux EBPF Watcher finished executing");

        result
    }
}
