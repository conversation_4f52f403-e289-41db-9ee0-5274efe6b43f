use crate::ParsedPackage;

use super::{
    ApplicabilityRulesEvaluationConfig, CbsRule, DeviceAttribute, DeviceAttributeExists, FileRule,
    LarAnd, LarNot, LarOr, Processor, ProductReleaseInstalled, Registry, WindowsLanguage,
    WindowsVersion, WmiQuery,
};
use logger::error;
use serde::Deserialize;

#[derive(Debug, Deserialize)]
pub enum Rules {
    True,
    False,
    And(LarAnd),
    Or(LarOr),
    Not(LarNot),
    WindowsVersion(WindowsVersion),
    WindowsLanguage(WindowsLanguage),
    ProductReleaseInstalled(ProductReleaseInstalled),
    ProductReleaseVersion(ProductReleaseInstalled),
    DeviceAttribute(DeviceAttribute),
    DeviceAttributeExists(DeviceAttributeExists),
    Processor(Processor),
    RegSz(Registry),
    RegDword(Registry),
    RegKeyExists(Registry),
    RegExpandSz(Registry),
    RegValueExists(Registry),
    RegSzToVersion(Registry),
    RegKeyLoop(Registry),
    FileExists(FileRule),
    FileModified(FileRule),
    FileSize(FileRule),
    FileVersion(FileRule),
    FileExistsPrependRegSz(FileRule),
    FileVersionPrependRegSz(FileRule),
    WmiQuery(WmiQuery),
    CbsPackageInstalled(CbsRule),
    CbsPackageInstallable(CbsRule),
    CbsPackageInstalledByIdentity(CbsRule),
    #[serde(other)]
    None,
}

impl Rules {
    pub fn evaluate(
        &self,
        package: &ParsedPackage,
        config: &ApplicabilityRulesEvaluationConfig,
    ) -> bool {
        match self {
            Rules::True => true,
            Rules::False => false,
            Rules::And(lar_and) => lar_and.evaluate(package, config),
            Rules::Or(lar_or) => lar_or.evaluate(package, config),
            Rules::Not(lar_not) => lar_not.evaluate(package, config),
            Rules::WindowsVersion(windows_version) => {
                windows_version.evaluate(config.wmi_operating_system.as_ref())
            }
            Rules::WindowsLanguage(windows_language) => {
                windows_language.evaluate(config.wmi_operating_system.as_ref())
            }
            Rules::ProductReleaseInstalled(product_release_installed) => {
                product_release_installed.evaluate()
            }
            Rules::ProductReleaseVersion(product_release_installed) => {
                product_release_installed.evaluate()
            }
            Rules::DeviceAttribute(device_attribute) => {
                device_attribute.evaluate(config.wmi_operating_system.as_ref())
            }
            Rules::DeviceAttributeExists(device_attribute_exists) => {
                device_attribute_exists.evaluate()
            }
            Rules::Processor(processor) => processor.evaluate(config.wmi_processor.as_ref()),
            Rules::RegSz(registry) => registry.evaluate(),
            Rules::RegDword(registry) => registry.evaluate(),
            Rules::RegKeyExists(registry) => registry.key_exists(),
            Rules::RegExpandSz(registry) => registry.evaluate(),
            Rules::RegValueExists(registry) => registry.value_exists(),
            Rules::RegSzToVersion(registry) => registry.evaluate_version(),
            Rules::RegKeyLoop(registry) => registry.evaluate_key_loop(),
            Rules::FileExists(file_rule) => file_rule.file_exists(false),
            Rules::FileModified(file_rule) => file_rule.evaluate_modified(),
            Rules::FileSize(file_rule) => file_rule.evaluate_file_size(),
            Rules::FileVersion(file_rule) => file_rule.evaluate_file_version(false),
            Rules::FileExistsPrependRegSz(file_rule) => file_rule.file_exists(true),
            Rules::FileVersionPrependRegSz(file_rule) => file_rule.evaluate_file_version(true),
            Rules::WmiQuery(wmi_query) => wmi_query.evaluate(),
            Rules::CbsPackageInstalled(cbs_rule) => cbs_rule.package_installed(),
            Rules::CbsPackageInstallable(cbs_rule) => cbs_rule.package_installable(),
            Rules::CbsPackageInstalledByIdentity(cbs_rule) => {
                cbs_rule.package_installed_by_identity()
            }
            Rules::None => {
                error!("Encountered None applicability rule");
                false
            }
        }
    }
}
