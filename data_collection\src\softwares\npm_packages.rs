use super::software::{PackageType, Software};
use crate::execute_in_thread_pool;
use glob::glob;
use logger::{error, trace};
use rayon::iter::{IntoParallelIterator, ParallelIterator};
use serde_json::{json, Value};
use std::{
    collections::{HashMap, HashSet},
    fs::File,
    io::BufReader,
    path::{Path, PathBuf},
};
use utils::shutdown::is_system_running;
#[cfg(windows)]
use windows_registry::WinRegistry;

#[derive(Debug, PartialEq, Eq, Hash, Default)]
pub struct NpmPackage {
    name: String,
    version: String,
    description: String,
    author: String,
    license: String,
    homepage: String,
    path: String,
    directory: String,
}

impl From<HashMap<String, Value>> for NpmPackage {
    fn from(value: HashMap<String, Value>) -> Self {
        let mut package = NpmPackage::default();

        if value.contains_key("name") {
            package.name = extract_string_value(&value, "name");
        }
        if value.contains_key("version") {
            package.version = extract_string_value(&value, "version");
        }
        if value.contains_key("description") {
            package.description = extract_string_value(&value, "description");
        }
        if value.contains_key("homepage") {
            package.homepage = extract_string_value(&value, "homepage");
        }
        if let Some(author) = value.get("author") {
            if author.is_string() {
                package.author = author
                    .as_str()
                    .map(|item| item.to_string())
                    .unwrap_or_default();
            } else if author.is_object() {
                let author_hashmap: HashMap<String, Value> =
                    serde_json::from_value(author.to_owned()).unwrap();
                package.author = extract_string_value(&author_hashmap, "name");
            }
        }

        if let Some(license) = value.get("license") {
            if license.is_string() {
                package.license = license
                    .as_str()
                    .map(|item| item.to_string())
                    .unwrap_or_default();
            } else if license.is_object() {
                let license_hashmap: HashMap<String, Value> =
                    serde_json::from_value(license.to_owned()).unwrap();
                package.license = extract_string_value(&license_hashmap, "license");
            }
        }
        package
    }
}

impl From<NpmPackage> for Software {
    fn from(value: NpmPackage) -> Self {
        Software {
            name: value.name,
            version: value.version,
            r#type: PackageType::NpmPackages,
            vendor: value.author,
            properties: json!({
                "description": value.description,
                "license": value.license,
                "homepage": value.homepage,
                "path": value.path,
                "directory": value.directory
            }),
            ..Default::default()
        }
    }
}

fn extract_string_value(map: &HashMap<String, Value>, key: &str) -> String {
    map.get(key)
        .and_then(|item| item.as_str())
        .and_then(|item| Some(item.to_string()))
        .unwrap_or_default()
}

impl NpmPackage {
    fn parse_package_json(path: &Path) -> Option<NpmPackage> {
        trace!("Checking file {}", path.display());
        let reader = match File::open(path) {
            Ok(file) => BufReader::new(file),
            Err(error) => {
                error!(?error, "Failed to read file {}", path.display());
                return None;
            }
        };

        let json_content: HashMap<String, Value> = match serde_json::from_reader(reader) {
            Ok(content) => content,
            Err(error) => {
                error!(
                    ?error,
                    "Failed to parse package.json at path {}",
                    path.display()
                );
                return None;
            }
        };

        Some(json_content.into())
    }

    #[cfg(windows)]
    fn collect_paths_from_registry() -> HashSet<String> {
        match WinRegistry::new("HKLM\\SOFTWARE\\Node.js") {
            Ok(reg) => {
                vec![reg.get_value::<String>("InstallPath".to_owned())]
            }
            Err(error) => {
                error!(?error, "Failed to open HKLM Nodejs install path");
                vec![]
            }
        }
        .into_iter()
        .chain(
            WinRegistry::get_users_key()
                .into_iter()
                .map(|key| WinRegistry::new(format!("HKU\\{}\\SOFTWARE\\Node.js", key)))
                .filter_map(Result::ok)
                .map(|item| item.get_value::<String>("InstallPath".to_owned()))
                .collect::<HashSet<String>>(),
        )
        .collect()
    }

    fn collect_from_directory(base_dir: PathBuf) -> HashSet<NpmPackage> {
        trace!("Visiting directory {}", base_dir.display());
        let glob_pattern = base_dir
            .join("node_modules/*/package.json")
            .to_string_lossy()
            .to_string();
        let paths = match glob(&glob_pattern) {
            Ok(paths) => paths
                .into_iter()
                .filter_map(Result::ok)
                .collect::<Vec<PathBuf>>(),
            Err(error) => {
                error!(
                    ?error,
                    "Failed to generate paths of node_modules for {}",
                    base_dir.display()
                );
                return HashSet::new();
            }
        };

        paths
            .into_iter()
            .map(|path| NpmPackage::parse_package_json(path.as_path()))
            .filter(|item| item.is_some())
            .map(|item| {
                let mut package = item.unwrap();
                package.directory = base_dir.to_string_lossy().to_string();
                package
            })
            .collect::<HashSet<NpmPackage>>()
    }

    pub fn collect() -> HashSet<Software> {
        let mut node_module_paths = if cfg!(windows) {
            vec!["C:\\Users\\<USER>\\AppData\\Roaming\\npm".to_owned()]
        } else {
            vec![
                "/usr/local/lib".to_owned(),
                "/opt/homebrew/lib".to_owned(),
                "/usr/lib".to_owned(),
                "/home/<USER>/.nvm/**/lib".to_owned(),
                "/home/<USER>/.npm-global/lib".to_owned(),
                "/Users/<USER>/.npm-global/lib".to_owned(),
                "/Users/<USER>/.nvm/**/lib".to_owned(),
            ]
        };

        #[cfg(windows)]
        {
            node_module_paths = node_module_paths
                .into_iter()
                .chain(NpmPackage::collect_paths_from_registry())
                .collect();
        }
        #[cfg(not(windows))]
        {
            if let Ok(value) = std::env::var("NVM_DIR") {
                node_module_paths.push(format!("{}/**/lib", value));
            }
        }

        execute_in_thread_pool(|| {
            node_module_paths
                .into_par_iter()
                .take_any_while(|_| is_system_running())
                .map(|item| glob(&item))
                .filter(|item| {
                    if let Err(error) = item.as_ref() {
                        error!(?error, "Failed to generate glob path");
                        return false;
                    }
                    return true;
                })
                .map(|item| item.unwrap())
                .flat_map_iter(|paths| paths.into_iter())
                .filter_map(Result::ok)
                .flat_map(|base_dir| NpmPackage::collect_from_directory(base_dir))
                .map(|item| item.into())
                .collect::<HashSet<Software>>()
        })
    }
}
