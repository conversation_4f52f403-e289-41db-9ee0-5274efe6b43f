use super::FileAttachment;
use crate::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>};
use logger::error;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::{fmt::Display, str::FromStr};
use utils::serde::none_if_empty_value;

#[derive(PartialEq, Eq, Serialize, Deserialize, <PERSON><PERSON>, Debug, De<PERSON>ult)]
pub enum ConfigurationType {
    #[serde(rename = "command")]
    #[default]
    Command,
    #[serde(rename = "script")]
    Script,
}

impl Display for ConfigurationType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ConfigurationType::Command => f.write_str("command"),
            ConfigurationType::Script => f.write_str("script"),
        }
    }
}

#[derive(PartialEq, Eq, Serialize, Deserialize, <PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON><PERSON><PERSON>, Hash)]
pub enum CommandType {
    #[default]
    #[serde(rename = "")]
    None,
    #[serde(rename = "reg")]
    Registry,
    #[serde(rename = "ps")]
    Powershell,
    #[serde(rename = "cmd")]
    Command,
    #[serde(rename = "script")]
    Script,
}

impl Display for CommandType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            CommandType::None => f.write_str(""),
            CommandType::Powershell => f.write_str("powershell"),
            CommandType::Registry => f.write_str("registry"),
            CommandType::Command => f.write_str("command"),
            CommandType::Script => f.write_str("script"),
        }
    }
}

impl FromStr for CommandType {
    type Err = DatabaseError;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        Ok(match s {
            "powershell" | "ps" => CommandType::Powershell,
            "registry" | "reg" => CommandType::Registry,
            "command" => CommandType::Command,
            "script" => CommandType::Script,
            _ => CommandType::None,
        })
    }
}

#[derive(PartialEq, Eq, Serialize, Deserialize, Clone, Debug, Default)]
#[serde(rename_all = "camelCase")]
pub struct ConfigurationAction {
    #[serde(default, deserialize_with = "none_if_empty_value")]
    pub command_type: Option<CommandType>,
    pub command: Option<String>,
    pub script_file: Option<FileAttachment>,
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
#[serde(rename_all = "camelCase")]
pub struct Configuration {
    pub id: Option<PrimaryKey>,
    pub name: Option<String>,
    pub display_name: Option<String>,
    pub description: Option<String>,
    pub configuration_type: ConfigurationType,
    #[serde(alias = "ConfigurationActions")]
    pub configuration_actions: Vec<ConfigurationAction>,
    pub self_service_supported: Option<bool>,
}

impl From<Value> for Configuration {
    fn from(value: Value) -> Self {
        match serde_json::from_value::<Self>(value) {
            Ok(data) => data,
            Err(error) => {
                error!(?error, "Failed to convert Value into Configuration");
                Configuration::default()
            }
        }
    }
}

impl From<&Value> for Configuration {
    fn from(value: &Value) -> Self {
        match serde_json::from_value::<Self>(value.to_owned()) {
            Ok(data) => data,
            Err(error) => {
                error!(?error, "Failed to convert Value into Configuration");
                Configuration::default()
            }
        }
    }
}
