// SPDX-License-Identifier: GPL-2.0
#include "vmlinux.h"
#include "common.bpf.h"
#include "bpf/bpf_helpers.h"
#include "buffer.bpf.h"
#include "get_path.bpf.h"

char LICENSE[] SEC("license") = "GPL v2";

#define FILE_CREATED 0
#define FILE_DELETED 1
#define DIR_CREATED 2
#define DIR_DELETED 3
#define FILE_OPENED 4
#define FILE_LINK 5
#define FILE_RENAME 6

// File blocking constants
#define EACCES 13 // Permission denied error code
#define UID_ANY 0xFFFFFFFF

struct rule_key
{
  __u64 dev_id; // device id (sb->s_dev or similar)
  __u64 ino_id;
};

struct
{
  __uint(type, BPF_MAP_TYPE_HASH);
  __uint(max_entries, 1024);
  __type(key, struct rule_key);
  __type(value, __u32);
} endpointops_blacklist SEC(".maps");

struct
{
  __uint(type, BPF_MAP_TYPE_HASH);
  __uint(max_entries, 1024);
  __type(key, struct rule_key);
  __type(value, __u32);
} endpointops_exception SEC(".maps");

struct file_opened_event
{
  struct buffer_index filename;
  int flags;
};

struct file_link_event
{
  struct buffer_index source;
  struct buffer_index destination;
  bool hard_link;
};

struct file_rename_event
{
  struct buffer_index source;
  struct buffer_index destination;
};

OUTPUT_MAP(fs_event, {
  struct buffer_index created;
  struct buffer_index deleted;
  struct buffer_index dir_created;
  struct buffer_index dir_deleted;
  struct file_opened_event opened;
  struct file_link_event link;
  struct file_rename_event rename;
});

// Utility to build a struct path from a dentry + parent path
static __always_inline struct path make_path(struct dentry *target_dentry,
                                             struct path *parent_path)
{
  struct path target_path = {
      .dentry = target_dentry,
      .mnt = BPF_CORE_READ(parent_path, mnt),
  };
  return target_path;
}

static __always_inline int check_path_hierarchy(struct dentry *dentry, u32 current_uid, int event_variant)
{
  for (int i = 0; i < MAX_PATH_UNROLL; i++)
  { // bounded loop: limit to 8 parents
    if (!dentry)
      break;

    struct inode *inode = BPF_CORE_READ(dentry, d_inode);
    if (!inode)
      break;

    const unsigned char *path_component_name = BPF_CORE_READ(dentry, d_name.name);

    bpf_printk("checking for path component %s", path_component_name);

    __u64 ino = BPF_CORE_READ(inode, i_ino);
    __u64 dev = BPF_CORE_READ(inode, i_sb, s_dev);
    // Lookup in blacklist
    struct rule_key key = {};
    key.dev_id = dev;
    key.ino_id = ino;

    __u32 *exception = bpf_map_lookup_elem(&endpointops_exception, &key);

    if (exception)
    {
      if (*exception == UID_ANY || *exception == current_uid)
      {
        bpf_printk("exception path=%s matched for uid=%u\n", path_component_name, current_uid);
        return 0;
      }
    }

    __u32 *rv = bpf_map_lookup_elem(&endpointops_blacklist, &key);

    if (rv)
    {
      if (*rv == UID_ANY || *rv == current_uid)
      {
        bpf_printk("blocked path=%s matched for uid=%u\n", path_component_name, current_uid);
        return -EACCES;
      }
    }

    // move to parent
    struct dentry *parent = BPF_CORE_READ(dentry, d_parent);
    if (parent == dentry)
      break; // reached root

    dentry = parent;
  }

  return 0; // allow if no match
}

// Check if a path should be blocked
static __always_inline int check_path_blocked(struct path *p, int event_variant)
{
  if (p && p->dentry)
  {
    u32 current_uid = bpf_get_current_uid_gid() & 0xFFFFFFFF;
    return check_path_hierarchy(p->dentry, current_uid, event_variant);
  }
  return 0;
}

/* --------------------
 * Event Handlers
 * -------------------- */

static __always_inline int on_path_mknod(void *ctx, struct path *dir,
                                         struct dentry *dentry, umode_t mode,
                                         unsigned int dev)
{
  struct path path = make_path(dentry, dir);

  if (check_path_blocked(dir, FILE_CREATED))
  {
    return -EACCES;
  }

  pid_t tgid = bpf_get_current_pid_tgid() >> 32;
  struct fs_event *event = init_fs_event(FILE_CREATED, tgid);
  if (!event)
    return 0;
  get_path_str(&path, &event->buffer, &event->created);
  output_fs_event(ctx, event);
  return 0;
}

static __always_inline int on_path_unlink(void *ctx, struct path *dir,
                                          struct dentry *dentry)
{
  struct path path = make_path(dentry, dir);

  if (check_path_blocked(&path, FILE_DELETED))
  {
    return -EACCES;
  }

  pid_t tgid = bpf_get_current_pid_tgid() >> 32;
  struct fs_event *event = init_fs_event(FILE_DELETED, tgid);
  if (!event)
    return 0;
  get_path_str(&path, &event->buffer, &event->deleted);
  output_fs_event(ctx, event);
  return 0;
}

static __always_inline int on_file_open(void *ctx, struct file *file)
{
  struct path path = BPF_CORE_READ(file, f_path);

  if (check_path_blocked(&path, FILE_OPENED))
  {
    return -EACCES;
  }

  pid_t tgid = bpf_get_current_pid_tgid() >> 32;
  struct fs_event *event = init_fs_event(FILE_OPENED, tgid);
  if (!event)
    return 0;
  get_path_str(&path, &event->buffer, &event->opened.filename);
  event->opened.flags = BPF_CORE_READ(file, f_flags);
  output_fs_event(ctx, event);
  return 0;
}

static __always_inline int on_path_link(void *ctx, struct dentry *old_dentry,
                                        struct path *new_dir,
                                        struct dentry *new_dentry)
{
  /* Note: build paths using dentry + parent 'new_dir' or 'old_dir' as appropriate.
     Here we treat 'old_dentry' as source and 'new_dentry' as destination. */
  struct path source = make_path(old_dentry, new_dir);
  struct path destination = make_path(new_dentry, new_dir);
  if (check_path_blocked(&source, FILE_LINK) || check_path_blocked(&destination, FILE_LINK))
  {
    return -EACCES;
  }
  pid_t tgid = bpf_get_current_pid_tgid() >> 32;
  struct fs_event *event = init_fs_event(FILE_LINK, tgid);
  if (!event)
    return 0;
  get_path_str(&source, &event->buffer, &event->link.source);
  get_path_str(&destination, &event->buffer, &event->link.destination);
  event->link.hard_link = true;
  output_fs_event(ctx, event);
  return 0;
}

static __always_inline int on_path_symlink(void *ctx, struct path *dir,
                                           struct dentry *dentry,
                                           char *old_name)
{
  struct path path = make_path(dentry, dir);

  if (check_path_blocked(&path, FILE_LINK))
  {
    return -EACCES;
  }
  // Only proceed with event logging if the process is interesting
  pid_t tgid = bpf_get_current_pid_tgid() >> 32;
  struct fs_event *event = init_fs_event(FILE_LINK, tgid);
  if (!event)
    return 0;
  get_path_str(&path, &event->buffer, &event->link.source);
  buffer_index_init(&event->buffer, &event->link.destination);
  buffer_append_str(&event->buffer, &event->link.destination, old_name,
                    BUFFER_MAX, 0);
  event->link.hard_link = false;
  output_fs_event(ctx, event);
  return 0;
}

static __always_inline int on_path_mkdir(void *ctx, struct path *dir,
                                         struct dentry *dentry, umode_t mode)
{
  struct path path = make_path(dentry, dir);

  if (check_path_blocked(dir, DIR_CREATED))
  {
    return -EACCES;
  }

  // Only proceed with event logging if the process is interesting
  pid_t tgid = bpf_get_current_pid_tgid() >> 32;
  struct fs_event *event = init_fs_event(DIR_CREATED, tgid);
  if (!event)
    return 0;
  get_path_str(&path, &event->buffer, &event->dir_created);
  output_fs_event(ctx, event);
  return 0;
}

static __always_inline int on_path_rmdir(void *ctx, struct path *dir,
                                         struct dentry *dentry)
{
  struct path path = make_path(dentry, dir);

  if (check_path_blocked(&path, DIR_DELETED))
  {
    return -EACCES;
  }

  pid_t tgid = bpf_get_current_pid_tgid() >> 32;
  struct fs_event *event = init_fs_event(DIR_DELETED, tgid);
  if (!event)
    return 0;
  get_path_str(&path, &event->buffer, &event->dir_deleted);
  output_fs_event(ctx, event);
  return 0;
}

static __always_inline int on_path_rename(void *ctx, struct path *old_dir,
                                          struct dentry *old_dentry,
                                          struct path *new_dir,
                                          struct dentry *new_dentry)
{
  struct path source = make_path(old_dentry, old_dir);
  struct path destination = make_path(new_dentry, new_dir);

  if (check_path_blocked(&source, FILE_RENAME) || check_path_blocked(&destination, FILE_RENAME))
  {
    return -EACCES;
  }

  pid_t tgid = bpf_get_current_pid_tgid() >> 32;
  struct fs_event *event = init_fs_event(FILE_RENAME, tgid);
  if (!event)
    return 0;
  get_path_str(&source, &event->buffer, &event->rename.source);
  get_path_str(&destination, &event->buffer, &event->rename.destination);
  output_fs_event(ctx, event);
  return 0;
}

/* --------------------
 * SEC Programs (LSM)
 * -------------------- */

SEC("lsm/path_mknod")
int BPF_PROG(path_mknod, struct path *dir, struct dentry *dentry,
             umode_t mode, unsigned int dev)
{
  return on_path_mknod(ctx, dir, dentry, mode, dev);
}

SEC("lsm/path_unlink")
int BPF_PROG(path_unlink, struct path *dir, struct dentry *dentry)
{
  return on_path_unlink(ctx, dir, dentry);
}

SEC("lsm/file_open")
int BPF_PROG(file_open, struct file *file)
{
  return on_file_open(ctx, file);
}

SEC("lsm/path_link")
int BPF_PROG(path_link, struct dentry *old_dentry, struct path *new_dir,
             struct dentry *new_dentry)
{
  return on_path_link(ctx, old_dentry, new_dir, new_dentry);
}

SEC("lsm/path_symlink")
int BPF_PROG(path_symlink, struct path *dir, struct dentry *dentry,
             char *old_name)
{
  return on_path_symlink(ctx, dir, dentry, old_name);
}

SEC("lsm/path_mkdir")
int BPF_PROG(path_mkdir, struct path *dir, struct dentry *dentry,
             umode_t mode)
{
  return on_path_mkdir(ctx, dir, dentry, mode);
}

SEC("lsm/path_rmdir")
int BPF_PROG(path_rmdir, struct path *dir, struct dentry *dentry)
{
  return on_path_rmdir(ctx, dir, dentry);
}

/* Special case for path_rename: kernel >= 5.19 has extra args */
#ifdef FEATURE_LSM
static __always_inline int shim_5_19_on_path_rename(unsigned long long *ctx,
                                                    struct path *old_dir,
                                                    struct dentry *old_dentry,
                                                    struct path *new_dir,
                                                    struct dentry *new_dentry,
                                                    unsigned int flags,
                                                    int ret)
{
  int block_result = on_path_rename(ctx, old_dir, old_dentry, new_dir, new_dentry);
  return block_result != 0 ? block_result : ret;
}

SEC("lsm/path_rename")
int BPF_PROG(path_rename,
             struct path *old_dir,
             struct dentry *old_dentry,
             struct path *new_dir,
             struct dentry *new_dentry)
{
  if (LINUX_KERNEL_VERSION >= KERNEL_VERSION(5, 19, 0))
  {
    unsigned int flags = (unsigned int)ctx[4];
    int ret = (int)(ctx[5]);
    return shim_5_19_on_path_rename(ctx, old_dir, old_dentry, new_dir,
                                    new_dentry, flags, ret);
  }
  else
  {
    int block_result = on_path_rename(ctx, old_dir, old_dentry, new_dir, new_dentry);
    return block_result != 0 ? block_result : (int)(ctx[4]);
  }
}
#endif

/* --------------------
 * SEC Programs (kprobes fallback)
 * -------------------- */

/* kprobe variants using kernel 'security_*' symbols so the program still
 * works when LSM program type is not available.
 *
 * These duplicate the LSM hooks but as kprobes. They call the same on_* helpers.
 */

SEC("kprobe/security_path_mknod")
int BPF_KPROBE(security_path_mknod,
               struct path *dir, struct dentry *dentry,
               umode_t mode, unsigned int dev)
{
  on_path_mknod(ctx, dir, dentry, mode, dev);
  return 0;
}

SEC("kprobe/security_path_unlink")
int BPF_KPROBE(security_path_unlink,
               struct path *dir, struct dentry *dentry)
{
  on_path_unlink(ctx, dir, dentry);
  return 0;
}

SEC("kprobe/security_file_open")
int BPF_KPROBE(security_file_open,
               struct file *file)
{
  on_file_open(ctx, file);
  return 0;
}

SEC("kprobe/security_path_link")
int BPF_KPROBE(security_path_link,
               struct dentry *old_dentry, struct path *new_dir,
               struct dentry *new_dentry)
{
  on_path_link(ctx, old_dentry, new_dir, new_dentry);
  return 0;
}

SEC("kprobe/security_path_symlink")
int BPF_KPROBE(security_path_symlink,
               struct path *dir, struct dentry *dentry,
               char *old_name)
{
  on_path_symlink(ctx, dir, dentry, old_name);
  return 0;
}

SEC("kprobe/security_path_mkdir")
int BPF_KPROBE(security_path_mkdir,
               struct path *dir, struct dentry *dentry,
               umode_t mode)
{
  on_path_mkdir(ctx, dir, dentry, mode);
  return 0;
}

SEC("kprobe/security_path_rmdir")
int BPF_KPROBE(security_path_rmdir,
               struct path *dir, struct dentry *dentry)
{
  on_path_rmdir(ctx, dir, dentry);
  return 0;
}

SEC("kprobe/security_path_rename")
int BPF_KPROBE(security_path_rename,
               struct path *old_dir,
               struct dentry *old_dentry,
               struct path *new_dir,
               struct dentry *new_dentry)
{
  on_path_rename(ctx, old_dir, old_dentry, new_dir, new_dentry);
  return 0;
}
