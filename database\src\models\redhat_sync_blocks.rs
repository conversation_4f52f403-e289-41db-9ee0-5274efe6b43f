use chrono::{offset::LocalResult, Datelike, TimeZone, Timelike};
use logger::error;
use serde::{Deserialize, Serialize};
use serde_json::Value;

#[derive(Debug, Deserialize, Serialize, <PERSON>lone, Default)]
pub struct RedhatSyncBlock {
    pub baseurl: String,
    pub displayname: String,
    pub folderpath: String,
    pub reponame: String,
}

#[derive(Debug, Deserialize, Serialize, <PERSON><PERSON>, <PERSON><PERSON>ult)]
pub struct RedhatSyncBlocks {
    pub blocks: Vec<RedhatSyncBlock>,
    pub name: Option<String>,
}

impl From<Value> for RedhatSyncBlocks {
    fn from(value: Value) -> Self {
        match serde_json::from_value::<Self>(value) {
            Ok(data) => data,
            Err(error) => {
                error!(?error, "Failed to convert Value into ComplianceRule");
                RedhatSyncBlocks::default()
            }
        }
    }
}

#[derive(Debug, Deserialize, Serialize, <PERSON><PERSON>, <PERSON><PERSON>ult)]
pub enum ScheduleType {
    Daily,
    Hourly,
    #[default]
    Other,
}

#[derive(Debug, Deserialize, Serialize, <PERSON>lone, Default)]
pub struct Schedule {
    r#type: ScheduleType,
    value: i64,
}

impl Schedule {
    pub fn get_next_trigger_remaining_secs(&self) -> i64 {
        match self.r#type {
            ScheduleType::Daily => {
                let timestamp = match chrono::Utc.timestamp_opt(self.value as i64, 0) {
                    LocalResult::Single(timestamp) => timestamp,
                    _ => {
                        error!(
                            "Failed to convert timestamp {} into chrono::DateTime",
                            self.value
                        );
                        // default 24 hours
                        return 86400;
                    }
                };
                let now = chrono::Utc::now();
                let next_day = now.checked_add_days(chrono::Days::new(1)).unwrap();
                logger::debug!("Now is {}", now.to_rfc3339());
                logger::debug!("Next Day is {}", next_day.to_rfc3339());
                let next_run = chrono::Utc
                    .with_ymd_and_hms(
                        next_day.year(),
                        next_day.month(),
                        next_day.day(),
                        timestamp.hour(),
                        timestamp.minute(),
                        timestamp.second(),
                    )
                    .unwrap()
                    .timestamp();
                next_run - now.timestamp()
            }
            ScheduleType::Hourly => self.value * 60 * 60,
            ScheduleType::Other => {
                // default 1 day
                24 * 60 * 60
            }
        }
    }
}

impl From<Value> for Schedule {
    fn from(value: Value) -> Self {
        let mut schedule = Schedule::default();
        let obj_value = match value.as_object() {
            Some(value) => value,
            None => {
                error!("Failed to convert Value {:?} into Schedule", value);
                return Schedule::default();
            }
        };
        schedule.r#type = match obj_value.get("type").unwrap().as_u64().unwrap() {
            1 => ScheduleType::Daily,
            2 => ScheduleType::Hourly,
            _ => ScheduleType::Other,
        };
        schedule.value = obj_value.get("value").unwrap().as_i64().unwrap_or_default();

        schedule
    }
}
