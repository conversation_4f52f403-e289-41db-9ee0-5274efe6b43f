use super::software::{PackageType, Software};
use crate::execute_in_thread_pool;
use glob::glob;
use logger::error;
use rayon::iter::{IntoParallelIterator, ParallelIterator};
use serde_json::{json, Value};
use std::{
    collections::HashSet,
    fs::{self, File},
    io::BufReader,
    path::{Path, PathBuf},
};
use utils::shutdown::is_system_running;

#[derive(Debug, Hash, PartialEq, Eq, Default)]
pub struct FirefoxExtension {
    name: String,
    version: String,
    r#type: String,
    description: String,
    creator: String,
    identifier: String,
    location: String,
    path: String,
    visible: bool,
    active: bool,
    soft_disabled: bool,
    app_disabled: bool,
    user_disabled: bool,
}

impl From<FirefoxExtension> for Software {
    fn from(value: FirefoxExtension) -> Self {
        Self {
            name: value.name,
            version: value.version,
            r#type: PackageType::FirefoxBrowserPlugin,
            // vendor: value.creator,
            vendor: "".to_owned(),
            properties: json!({
                "type": value.r#type,
                "description": value.description,
                "identifier": value.identifier,
                "location": value.location,
                "path": value.path,
                "visible": value.visible,
                "active": value.active,
                "soft_disabled": value.soft_disabled,
                "app_disabled": value.app_disabled,
                "user_disabled": value.user_disabled
            }),
            ..Default::default()
        }
    }
}

impl FirefoxExtension {
    fn browser_paths() -> Vec<String> {
        #[cfg(windows)]
        {
            vec!["C:\\Users\\<USER>\\AppData\\Roaming\\Mozilla\\Firefox\\Profiles".to_owned()]
        }
        #[cfg(target_os = "linux")]
        {
            vec![
                "/home/<USER>/snap/firefox/common/.mozilla/firefox/".to_owned(),
                "/home/<USER>/.mozilla/firefox/".to_owned(),
            ]
        }
        #[cfg(target_os = "macos")]
        {
            vec!["/Users/<USER>/Library/Application Support/Firefox/Profiles".to_owned()]
        }
    }

    fn generate_from_value(addon: &Value) -> Option<FirefoxExtension> {
        let default_locale = match addon.get("defaultLocale") {
            Some(default_locale) => default_locale,
            None => return None,
        };

        let mut package = FirefoxExtension::default();

        if let Some(name) = default_locale.get("name") {
            package.name = name.as_str().map_or("".to_owned(), |i| i.to_owned());
        }

        if let Some(version) = addon.get("version") {
            package.version = version.as_str().map_or("".to_owned(), |i| i.to_owned());
        }

        if let Some(r#type) = addon.get("type") {
            package.r#type = r#type.as_str().map_or("".to_owned(), |i| i.to_owned());
        }

        if let Some(description) = default_locale.get("description") {
            package.description = description.as_str().map_or("".to_owned(), |i| i.to_owned());
        }

        if let Some(creator) = default_locale.get("creator") {
            package.creator = creator.as_str().map_or("".to_owned(), |i| i.to_owned());
        }

        if let Some(identifier) = addon.get("id") {
            package.identifier = identifier.as_str().map_or("".to_owned(), |i| i.to_owned());
        }

        if let Some(location) = addon.get("location") {
            package.location = location.as_str().map_or("".to_owned(), |i| i.to_owned());
        }

        if let Some(path) = addon.get("path") {
            package.path = path.as_str().map_or("".to_owned(), |i| i.to_owned());
        }

        if let Some(visible) = addon.get("visible") {
            package.visible = visible.as_bool().unwrap_or_default();
        }

        if let Some(active) = addon.get("active") {
            package.active = active.as_bool().unwrap_or_default();
        }

        if let Some(soft_disabled) = addon.get("softDisabled") {
            package.soft_disabled = soft_disabled.as_bool().unwrap_or_default();
        }

        if let Some(app_disabled) = addon.get("appDisabled") {
            package.app_disabled = app_disabled.as_bool().unwrap_or_default();
        }

        if let Some(user_disabled) = addon.get("userDisabled") {
            package.user_disabled = user_disabled.as_bool().unwrap_or_default();
        }

        Some(package)
    }

    fn collect_extensions_from_profile(profile_path: &str) -> HashSet<FirefoxExtension> {
        let path = PathBuf::from(profile_path).join("extensions.json");
        let reader = match File::open(&path) {
            Ok(file) => BufReader::new(file),
            Err(_error) => {
                // error!(?error, "Failed to read profile {}", path.display());
                return HashSet::new();
            }
        };

        let extensions: Value = match serde_json::from_reader(reader) {
            Ok(value) => value,
            Err(error) => {
                error!(?error, "Failed to parse extensions.json");
                return HashSet::new();
            }
        };

        let addons = match extensions.get("addons") {
            Some(addons) => match addons.as_array() {
                Some(addons) => addons,
                None => {
                    error!("Extensions addons is not an array");
                    return HashSet::new();
                }
            },
            None => {
                error!("Extensions addons is not available");
                return HashSet::new();
            }
        };

        addons
            .into_iter()
            .map(|addon| FirefoxExtension::generate_from_value(addon))
            .filter(|item| item.is_some())
            .map(|item| item.unwrap())
            .collect()
    }

    fn get_profiles(browser_path: &Path) -> HashSet<String> {
        match fs::read_dir(browser_path) {
            Ok(dir) => dir
                .into_iter()
                .filter_map(Result::ok)
                .map(|entry| entry.path())
                .filter(|item| {
                    let str = item.to_string_lossy();
                    !(str.contains("Crash Reports") || str.contains("Pending Pings"))
                })
                .map(|item| item.to_string_lossy().to_string())
                .collect(),
            Err(error) => {
                error!(?error, "Failed to read path {}", browser_path.display());
                return HashSet::new();
            }
        }
    }

    pub fn collect() -> HashSet<Software> {
        execute_in_thread_pool(|| {
            FirefoxExtension::browser_paths()
                .into_par_iter()
                .take_any_while(|_| is_system_running())
                .map(|path| glob(&path))
                .filter_map(Result::ok)
                .flat_map_iter(|paths| paths.into_iter().filter_map(Result::ok))
                .flat_map(|path| FirefoxExtension::get_profiles(path.as_path()))
                .flat_map(|profile_path| {
                    FirefoxExtension::collect_extensions_from_profile(&profile_path)
                })
                .map(|item| item.into())
                .collect()
        })
    }
}
