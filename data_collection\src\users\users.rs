use super::{logged_in_users::LoggedInUser, user::SystemUser};
use crate::{DataCollectionError, DataCollectionExtension};
use anyhow::Result;
use async_trait::async_trait;
use database::models::AgentMetadata;
use logger::{debug, ModuleLogger};
use serde_json::{json, Value};
use std::{collections::HashSet, sync::Arc, time::Instant};

#[derive(Debug)]
pub struct Users<'a> {
    users: HashSet<SystemUser>,
    logged_in_users: HashSet<LoggedInUser>,
    agent_metadata: &'a AgentMetadata,
}

impl<'a> Users<'a> {
    pub fn new(agent_metadata: &'a AgentMetadata) -> Self {
        Self {
            agent_metadata,
            logged_in_users: HashSet::new(),
            users: HashSet::new(),
        }
    }
}

#[async_trait]
impl<'a> DataCollectionExtension for Users<'a> {
    fn get_refresh_interval(&self) -> u64 {
        self.agent_metadata
            .get_agent_refresh_settings()
            .users_refresh_cycle
    }

    fn get_name(&self) -> &str {
        "users_ext"
    }

    fn get_endpoint_id(&self) -> i64 {
        self.agent_metadata.get_endpoint_id()
    }

    fn build_payload(&self) -> Result<Value, DataCollectionError> {
        Ok(json!({
            "asset_id" : self.get_endpoint_id(),
            "data" : json!({
                "endpoint_user_details": self.users,
                "logged_in_user_details": self.logged_in_users
            })
        }))
    }

    async fn collect(&mut self, logger: Arc<ModuleLogger>) -> Result<(), DataCollectionError> {
        let data = tokio::task::spawn_blocking(move || {
            logger.with(|| {
                let time = Instant::now();
                let users = SystemUser::collect();
                let logged_in_users = LoggedInUser::collect();
                debug!("Time taken for collection {:?}", time.elapsed());
                (users, logged_in_users)
            })
        })
        .await?;

        self.users = data.0;
        self.logged_in_users = data.1;
        Ok(())
    }
}
