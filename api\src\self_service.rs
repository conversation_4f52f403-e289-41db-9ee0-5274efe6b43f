use crate::{client::ApiResponseWithCount, get_client, ApiError};
use serde_json::{json, Value};
use std::{collections::HashMap, fmt::Debug};

pub async fn get_agent_id_by_uuid(uuid: &str) -> Result<HashMap<String, i64>, ApiError> {
    get_client()?
        .post::<HashMap<String, i64>, _>("/patch/agent/search/byUUID", json!({"uuid": uuid}))
        .await
}

pub async fn get_branding() -> Result<Value, ApiError> {
    get_client()?.get::<Value, _>("/settings/branding/0").await
}

pub async fn get_softwares<T: Debug + Clone + serde::de::DeserializeOwned>(
    payload: Value,
) -> Result<ApiResponseWithCount<T>, ApiError> {
    get_client()?
        .post_with_count::<T, _>("/patch/package/search", payload)
        .await
}

pub async fn create_deployment(payload: Value) -> Result<Value, ApiError> {
    get_client()?
        .post::<Value, _>("/patch/deployment", payload)
        .await
}

pub async fn get_missing_patches<T: Debug + Clone + serde::de::DeserializeOwned>(
    payload: Value,
) -> Result<ApiResponseWithCount<T>, ApiError> {
    get_client()?
        .post_with_count::<T, _>("/patch/asset-patch-relation/search", payload)
        .await
}
