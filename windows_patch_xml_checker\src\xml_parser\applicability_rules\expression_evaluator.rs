use super::bar_rules::Operator;
use evalexpr::eval_boolean;
use logger::{debug, error};
use regex::Regex;

pub struct ExpressionEvaluator<'a, T: ?Sized + ToString> {
    current_value: &'a T,
    expected_value: &'a T,
    operator: &'a Operator,
    rule_key_name: &'a str,
}

impl<'a, T: ?Sized + ToString> ExpressionEvaluator<'a, T> {
    pub fn new(
        current_value: &'a T,
        expected_value: &'a T,
        operator: &'a Operator,
        rule_key_name: &'a str,
    ) -> Self {
        Self {
            current_value,
            expected_value,
            operator,
            rule_key_name,
        }
    }

    pub fn run(&self) -> bool {
        let re = Regex::new(r"^[\d\.]+$").unwrap();
        let expression = if re.is_match(self.current_value.to_string().as_str()) == false
            || re.is_match(self.expected_value.to_string().as_str()) == false
        {
            format!(
                "\"{}\" {} \"{}\"",
                self.current_value.to_string(),
                self.operator.to_string(),
                self.expected_value.to_string()
            )
        } else {
            format!(
                "{} {} {}",
                self.current_value.to_string(),
                self.operator.to_string(),
                self.expected_value.to_string()
            )
        };
        match eval_boolean(&expression) {
            Ok(result) => {
                debug!(
                    "{}: Expression {} evaluated to {}",
                    self.rule_key_name.to_string(),
                    expression,
                    result
                );
                result
            }
            Err(error) => {
                error!(
                    ?error,
                    "{} Failed to evaluate expression {}",
                    self.rule_key_name.to_string(),
                    expression
                );
                false
            }
        }
    }
}
