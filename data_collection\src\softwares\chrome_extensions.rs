use super::software::{PackageType, Software};
use crate::execute_in_thread_pool;
use glob::glob;
use logger::{error, trace};
use rayon::iter::{IntoParallelIterator, ParallelIterator};
use serde::Serialize;
use serde_json::{json, Map, Value};
use std::{
    collections::HashSet,
    fs::{self, File},
    io::BufReader,
    path::{Path, PathBuf},
};
use utils::shutdown::is_system_running;

fn merge_values(a: &mut Value, b: Value) {
    match (a, b) {
        (a @ &mut Value::Object(_), Value::Object(b)) => {
            let a = a.as_object_mut().unwrap();
            for (k, v) in b {
                merge_values(a.entry(k).or_insert(Value::Null), v);
            }
        }
        (a, b) => *a = b,
    }
}

fn extract_string_value(map: &Map<String, Value>, key: &str) -> String {
    map.get(key)
        .and_then(|item| item.as_str())
        .and_then(|item| Some(item.to_string()))
        .unwrap_or_default()
}

#[derive(Debug, Default)]
struct BrowserProfile {
    browser_type: BrowserType,
    name: String,
    path: String,
    preferences: Value,
}

#[allow(dead_code)]
#[derive(Debug, Serialize, Default, Hash, PartialEq, Eq, Clone)]
enum BrowserType {
    GoogleChrome,
    GoogleChromeBeta,
    GoogleChromeDev,
    GoogleChromeCanary,
    BraveBrowser,
    Chromium,
    YandexBrowser,
    MicrosoftEdge,
    MicrosoftEdgeBeta,
    Opera,
    Vivaldi,
    Arc,
    #[default]
    Unknown,
}

#[derive(Debug, Serialize, Hash, Eq, Default)]
pub struct ChromeExtension {
    browser_type: BrowserType,
    name: String,
    profile: String,
    profile_path: String,
    path: String,
    identifier: String,
    version: String,
    description: String,
    state: u64,
    author: String,
}

impl PartialEq for ChromeExtension {
    fn eq(&self, other: &Self) -> bool {
        self.browser_type == other.browser_type
            && self.name == other.name
            && self.profile == other.profile
            && self.identifier == other.identifier
            && self.version == other.version
            && self.description == other.description
            && self.state == other.state
            && self.author == other.author
    }
}

impl From<ChromeExtension> for Software {
    fn from(value: ChromeExtension) -> Self {
        Software {
            name: value.name,
            version: value.version,
            r#type: PackageType::ChromeBrowserPlugin,
            // vendor: value.author,
            vendor: "".to_owned(),
            properties: json!({
                "browser_type": value.browser_type,
                "profile": value.profile,
                "profile_path": value.profile_path,
                "identifier": value.identifier,
                "description": value.description,
                "state": value.state,
                "path": value.path,
            }),
            ..Default::default()
        }
    }
}

impl ChromeExtension {
    fn browser_paths() -> Vec<(String, BrowserType)> {
        #[cfg(windows)]
        {
            vec![
                (
                    "C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data".to_owned(),
                    BrowserType::GoogleChrome,
                ),
                (
                    "C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome Beta\\User Data".to_owned(),
                    BrowserType::GoogleChromeBeta,
                ),
                (
                    "C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome Dev\\User Data".to_owned(),
                    BrowserType::GoogleChromeDev,
                ),
                (
                    "C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome SxS\\User Data".to_owned(),
                    BrowserType::GoogleChromeCanary,
                ),
                (
                    "C:\\Users\\<USER>\\AppData\\Roaming\\brave".to_owned(),
                    BrowserType::BraveBrowser,
                ),
                (
                    "C:\\Users\\<USER>\\AppData\\Local\\Chromium".to_owned(),
                    BrowserType::Chromium,
                ),
                (
                    "C:\\Users\\<USER>\\AppData\\Local\\Yandex\\YandexBrowser\\User Data".to_owned(),
                    BrowserType::YandexBrowser,
                ),
                (
                    "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\Edge\\User Data".to_owned(),
                    BrowserType::MicrosoftEdge,
                ),
                (
                    "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\Edge Beta\\User Data".to_owned(),
                    BrowserType::MicrosoftEdgeBeta,
                ),
                (
                    "C:\\Users\\<USER>\\AppData\\Roaming\\Opera Software\\Opera Stable".to_owned(),
                    BrowserType::Opera,
                ),
                (
                    "C:\\Users\\<USER>\\AppData\\Local\\Vivaldi\\User Data".to_owned(),
                    BrowserType::Vivaldi,
                ),
            ]
        }
        #[cfg(target_os = "linux")]
        {
            vec![
                (
                    "/home/<USER>/.config/google-chrome".to_owned(),
                    BrowserType::GoogleChrome,
                ),
                (
                    "/home/<USER>/.config/google-chrome".to_owned(),
                    BrowserType::GoogleChromeBeta,
                ),
                (
                    "/home/<USER>/.config/google-chrome-unstable".to_owned(),
                    BrowserType::GoogleChromeDev,
                ),
                (
                    "/home/<USER>/.config/BraveSoftware/Brave-Browser".to_owned(),
                    BrowserType::BraveBrowser,
                ),
                ("/home/<USER>/.config/chromium".to_owned(), BrowserType::Chromium),
                (
                    "/home/<USER>/snap/chromium/common/chromium".to_owned(),
                    BrowserType::Chromium,
                ),
                (
                    "/home/<USER>/.config/yandex-browser-beta".to_owned(),
                    BrowserType::YandexBrowser,
                ),
                ("/home/<USER>/.config/opera".to_owned(), BrowserType::Opera),
                ("/home/<USER>/.config/vivaldi".to_owned(), BrowserType::Vivaldi),
            ]
        }
        #[cfg(target_os = "macos")]
        {
            vec![
                (
                    "/Users/<USER>/Library/Application Support/Google/Chrome".to_owned(),
                    BrowserType::GoogleChrome,
                ),
                (
                    "/Users/<USER>/Library/Application Support/Google/Chrome Beta".to_owned(),
                    BrowserType::GoogleChromeBeta,
                ),
                (
                    "/Users/<USER>/Library/Application Support/Google/Chrome Dev".to_owned(),
                    BrowserType::GoogleChromeDev,
                ),
                (
                    "/Users/<USER>/Library/Application Support/Google/Chrome Canary".to_owned(),
                    BrowserType::GoogleChromeCanary,
                ),
                (
                    "/Users/<USER>/Library/Application Support/BraveSoftware/Brave-Browser".to_owned(),
                    BrowserType::BraveBrowser,
                ),
                (
                    "/Users/<USER>/Library/Application Support/Chromium".to_owned(),
                    BrowserType::Chromium,
                ),
                (
                    "/Users/<USER>/Library/Application Support/Yandex/YandexBrowser".to_owned(),
                    BrowserType::YandexBrowser,
                ),
                (
                    "/Users/<USER>/Library/Application Support/Microsoft Edge".to_owned(),
                    BrowserType::MicrosoftEdge,
                ),
                (
                    "/Users/<USER>/Library/Application Support/Microsoft Edge Beta".to_owned(),
                    BrowserType::MicrosoftEdgeBeta,
                ),
                (
                    "/Users/<USER>/Library/Application Support/com.operasoftware.Opera".to_owned(),
                    BrowserType::Opera,
                ),
                (
                    "/Users/<USER>/Library/Application Support/Vivaldi".to_owned(),
                    BrowserType::Vivaldi,
                ),
                (
                    "/Users/<USER>/Library/Application Support/Arc/User Data".to_owned(),
                    BrowserType::Arc,
                ),
            ]
        }
    }

    fn build_extension_from_path(path: &Path, profile: &BrowserProfile) -> Option<ChromeExtension> {
        trace!("Checking path {}", path.display());
        let identifier = path.file_name().unwrap().to_string_lossy().to_string();
        let manifest_path = format!("{}/*/manifest.json", path.display());
        let manifest_file = match glob(&manifest_path) {
            Ok(paths) => paths.into_iter().filter_map(Result::ok).last(),
            Err(error) => {
                error!(
                    ?error,
                    "Failed to find manifest file in path {}",
                    path.display()
                );
                return None;
            }
        };

        if manifest_file.is_none() {
            trace!("Failed to find manifest file in path {}", path.display());
            return None;
        }

        let manifest_file = manifest_file.unwrap();

        let reader = match File::open(&manifest_file) {
            Ok(file) => BufReader::new(file),
            Err(error) => {
                error!(?error, "Failed to open file {}", manifest_file.display());
                return None;
            }
        };

        let mut manifest: Value = match serde_json::from_reader(reader) {
            Ok(v) => v,
            Err(error) => {
                error!(
                    ?error,
                    "Failed to parse manifest.json {}",
                    manifest_file.display()
                );
                return None;
            }
        };

        let identifier_preference = profile
            .preferences
            .get("settings")
            .and_then(|value| value.get(&identifier))
            .map_or(json!({}), |f| f.to_owned());

        let extension_preference = identifier_preference
            .get("manifest")
            .map_or(json!({}), |f| f.to_owned());

        merge_values(&mut manifest, extension_preference);

        let manifest = manifest.as_object().unwrap();

        let mut package = ChromeExtension::default();
        package.identifier = identifier;
        package.path = identifier_preference
            .get("path")
            .map_or("".to_owned(), |i| i.as_str().unwrap().to_string());
        if manifest.contains_key("name") {
            package.name = extract_string_value(manifest, "name");
        }
        if manifest.contains_key("version") {
            package.version = extract_string_value(manifest, "version");
        }
        if manifest.contains_key("description") {
            package.description = extract_string_value(manifest, "description");
        }
        if manifest.contains_key("author") {
            if manifest.get("author").unwrap().is_object() {
                package.author = extract_string_value(
                    manifest.get("author").unwrap().as_object().unwrap(),
                    "name",
                );
            } else {
                package.author = extract_string_value(manifest, "author");
            }
        }
        package.state = identifier_preference
            .get("state")
            .map_or(0, |v| v.as_u64().unwrap());
        Some(package)
    }

    fn get_browser_profile(user_data_dir: &str, profile_name: &str) -> BrowserProfile {
        let mut profile = BrowserProfile::default();
        profile.path = format!("{}/{}", user_data_dir, profile_name);
        profile.name = profile_name.to_owned();

        let profile_path = PathBuf::from(format!("{}/{}", user_data_dir, profile_name));
        let preference_path = profile_path.join("Preferences");
        let secure_preference_path = profile_path.join("Secure Preferences");
        if !preference_path.exists() {
            return profile;
        }
        if !secure_preference_path.exists() {
            return profile;
        }
        let mut preference_value = match File::open(&preference_path) {
            Ok(f) => match serde_json::from_reader(BufReader::new(f)) {
                Ok(v) => v,
                Err(error) => {
                    error!(
                        ?error,
                        "Failed to parse Preferences file {}",
                        preference_path.display()
                    );
                    json!({})
                }
            },
            Err(error) => {
                error!(
                    ?error,
                    "Failed to read Preferences file {}",
                    preference_path.display()
                );
                json!({})
            }
        };

        let secure_preference_value = match File::open(&secure_preference_path) {
            Ok(f) => match serde_json::from_reader(BufReader::new(f)) {
                Ok(v) => v,
                Err(error) => {
                    error!(
                        ?error,
                        "Failed to parse Secure Preferences file {}",
                        secure_preference_path.display()
                    );
                    json!({})
                }
            },
            Err(error) => {
                error!(
                    ?error,
                    "Failed to read Secure Preferences file {}",
                    secure_preference_path.display()
                );
                json!({})
            }
        };

        merge_values(&mut preference_value, secure_preference_value);

        profile.preferences = preference_value
            .get("extensions")
            .map_or(json!({}), |v| v.to_owned());
        profile
    }

    fn check_profile_for_extension(profile: BrowserProfile) -> HashSet<ChromeExtension> {
        let glob_pattern = format!("{}/Extensions/*/", profile.path);
        let paths = match glob(&glob_pattern) {
            Ok(paths) => paths.into_iter().filter_map(Result::ok),
            Err(error) => {
                error!(?error, "Failed to visit glob pattern {}", glob_pattern);
                return HashSet::new();
            }
        };

        paths
            .map(|path| ChromeExtension::build_extension_from_path(path.as_path(), &profile))
            .filter(|extension| extension.is_some())
            .map(|e| {
                let mut extension = e.unwrap();
                extension.profile = profile.name.to_owned();
                extension.profile_path = profile.path.to_owned();
                extension
            })
            .collect()
    }

    fn visit_browser_path(
        user_data_path: &str,
        browser_type: BrowserType,
    ) -> HashSet<ChromeExtension> {
        let browser_user_data_dirs = match glob(&user_data_path) {
            Ok(paths) => paths.into_iter().filter_map(Result::ok),
            Err(error) => {
                error!(?error, "Failed to read glob pattern {}", user_data_path);
                return HashSet::new();
            }
        };
        browser_user_data_dirs
            .map(|path| fs::read_dir(path))
            .filter_map(Result::ok)
            .flat_map(|dir| dir.into_iter().filter_map(Result::ok))
            .map(|path| {
                let profile = path.file_name().to_string_lossy().to_string();
                let data_path = path.path().parent().unwrap().to_string_lossy().to_string();
                let mut profile = ChromeExtension::get_browser_profile(&data_path, &profile);
                profile.browser_type = browser_type.clone();
                profile
            })
            .flat_map(|profile| ChromeExtension::check_profile_for_extension(profile))
            .map(|mut extension| {
                extension.browser_type = browser_type.clone();
                extension
            })
            .collect()
    }

    pub fn collect() -> HashSet<Software> {
        execute_in_thread_pool(|| {
            ChromeExtension::browser_paths()
                .into_par_iter()
                .take_any_while(|_| is_system_running())
                .flat_map_iter(|item| ChromeExtension::visit_browser_path(&item.0, item.1))
                .map(|item| item.into())
                .collect()
        })
    }
}
