[package]
name = "agents"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[features]
default = []
self_service = [
  "task_execution/self_service",
  "utils/self_service",
  "auth_event/self_service",
]

[dependencies]
logger = { path = "../logger" }
data_collection = { path = "../data_collection" }
agent_manager = { path = "../agent_manager" }
task_execution = { path = "../task_execution" }
utils = { path = "../utils" }
database = { path = "../database" }
api = { path = "../api" }
fim = { path = "../fim" }
auth_event = { path = "../auth_event" }
cfg-if = { workspace = true }
anyhow = { workspace = true }
async-trait = { workspace = true }
tokio = { workspace = true }
serde_json = { workspace = true }
sysinfo = { workspace = true }
serde = { workspace = true }
futures-util = { workspace = true }
tokio-stream = { workspace = true }
chrono = { workspace = true }

[target.'cfg(windows)'.dependencies]
winreg = "0.55.0"
