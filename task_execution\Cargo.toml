[package]
name = "task_execution"
description.workspace = true
edition.workspace = true
version.workspace = true
authors.workspace = true

[features]
default = []
self_service = ["utils/self_service"]

[dependencies]
logger = { path = "../logger" }
agent_manager = { path = "../agent_manager" }
data_collection = { path = "../data_collection" }
database = { path = "../database" }
shell = { path = "../shell" }
api = { path = "../api" }
utils = { path = "../utils" }
ipc = { path = "../ipc" }
thiserror = { workspace = true }
anyhow = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
tokio = { workspace = true }
chrono = { workspace = true }
tokio-stream = { workspace = true }
sysinfo = { workspace = true }
async-trait = { workspace = true }
cfg-if = { workspace = true }
futures-util = { workspace = true }
tokio-util = { workspace = true }
rayon = { workspace = true }
async-speed-limit = { workspace = true }
evalexpr = "12.0.2"
async_zip = { version = "0.0.17", features = [
  "full",
  "tokio",
  "tokio-fs",
  "tokio-util",
] }
tokio-tar = "0.3.1"
sanitize-filename = "0.6.0"
sevenz-rust = "0.6.1"
csv-async = { version = "1.3.1", features = ["tokio"] }
base64 = "0.22.1"
self-replace = "=1.5.0"
configparser = { version = "3.1.0", features = ["async-std", 'tokio'] }
version-compare = "0.2.0"
regex = { workspace = true }

[target.'cfg(windows)'.dependencies]
windows_registry = { path = "../windows_registry" }
windows_patch_xml_checker = { path = "../windows_patch_xml_checker" }
windows = { version = "0.61.3", features = [
  "Win32_UI_Shell",
  "Win32_Storage_FileSystem",
  "Win32_Foundation",
  "Win32_System",
  "Win32_System_Com",
  "Win32_System_UpdateAgent",
] }
dashmap = { version = "6.1.0", features = ["rayon"] }
encoding_rs_io = "0.1.7"
encoding_rs = "0.8.35"

[target.'cfg(target_os = "linux")'.dependencies]
os_info = "3.12.0"
deb-version = "0.1.1"
