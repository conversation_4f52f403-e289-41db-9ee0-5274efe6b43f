use crate::{<PERSON>, Log<PERSON><PERSON>le, LoggerError, DEFAULT_MODULE_FILTER};
use std::{collections::HashMap, sync::RwLock};
use tracing::{debug, error, info};

pub struct LogLevelManager {
    log_level: RwLock<Level>,
    active_reload_handles: RwLock<HashMap<String, LogHandle>>,
}

impl LogLevelManager {
    pub fn new() -> Self {
        Self {
            log_level: RwLock::new(Level::INFO),
            active_reload_handles: RwLock::new(HashMap::new()),
        }
    }

    pub fn register_logger(&self, name: String, reload_handle: LogHandle) {
        match self.active_reload_handles.write() {
            Err(error) => {
                error!(
                    ?error,
                    "Failed to get lock on active handles to register logger {}", name
                );
                return;
            }
            Ok(mut active_loggers) => {
                if active_loggers.contains_key(&name) {
                    error!("Logger {} already exists", name);
                    return;
                }
                debug!("Registering logger {}", name);
                active_loggers.insert(name, reload_handle);
            }
        }
    }

    pub fn unregister_logger(&self, name: &str) {
        match self.active_reload_handles.write() {
            Err(error) => {
                error!(
                    ?error,
                    "Failed to get lock on active handles to unregister logger {}", name
                );
                return;
            }
            Ok(mut active_loggers) => {
                active_loggers.remove(name);
                debug!("Removed logger {}", name);
            }
        }
    }

    pub fn set_level<L: Into<Level>>(&self, level: L) -> Result<(), LoggerError> {
        let applied_level = level.into();
        match self.log_level.write() {
            Err(error) => {
                error!(?error, "Failed to get lock on global log level");
                return Err(LoggerError::LogError(
                    "Failed to get lock on global log level".to_owned(),
                ));
            }
            Ok(mut log_level) => {
                *log_level = applied_level.clone();
                self.apply_log_level_to_all_handles(applied_level);
                Ok(())
            }
        }
    }

    fn apply_log_level_to_all_handles(&self, level: Level) {
        match self.active_reload_handles.write() {
            Err(error) => {
                error!(
                    ?error,
                    "Failed to get lock on active handles to apply new log level {}", level
                );
                return;
            }
            Ok(active_loggers) => {
                for (name, handle) in active_loggers.iter() {
                    self.apply_log_level_to_handle(name, level.clone(), handle);
                }
            }
        }
    }

    fn apply_log_level_to_handle(&self, name: &str, level: Level, handle: &LogHandle) {
        let filter_str = DEFAULT_MODULE_FILTER
            .replace("{LEVEL}", level.clone().into())
            .replace("{MODULE}", name);

        match handle.modify(|filter| *filter = filter_str.into()) {
            Err(error) => {
                error!(
                    ?error,
                    "Failed to set log level of logger {} to {}", name, level
                );
            }
            Ok(_) => {
                info!("Log level set to {} for logger {}", level, name);
            }
        }
    }

    pub fn get_current_level(&self) -> Level {
        self.log_level.read().unwrap().to_owned()
    }
}
