// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use logger::{LogLevelManager, LOG_LEVEL_MANAGER};

fn main() {
    LOG_LEVEL_MANAGER.get_or_init(|| {
        let manager = LogLevelManager::new();
        manager
            .set_level("debug")
            .expect("Failed to set Global Log Level");
        manager
    });

    self_service_lib::run().unwrap();
}
